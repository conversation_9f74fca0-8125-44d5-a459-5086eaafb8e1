# risk_analysis/risk_analyzer.py
"""
智能风险分析器 - 核心风险识别引擎
整合规则匹配、AI分析和风险评估功能
"""

import asyncio
import logging
import time
import re
import uuid
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime

from .models import (
    RiskRule, RiskPoint, RiskAnalysisResult, RiskAssessment,
    RiskCategory, RiskLevel, RiskImpact, RiskProbability,
    RiskMitigationPlan
)
from .risk_knowledge_base import risk_knowledge_base

logger = logging.getLogger(__name__)

class RiskAnalyzer:
    """智能风险分析器"""
    
    def __init__(self, qwen_client=None):
        """
        初始化风险分析器
        
        Args:
            qwen_client: 千问3 API客户端
        """
        self.qwen_client = qwen_client
        self.knowledge_base = risk_knowledge_base
        
        # 分析配置
        self.config = {
            'min_confidence': 0.6,
            'enable_ai_verification': True,
            'max_ai_calls': 15,
            'context_window': 200,
            'risk_aggregation': True
        }
        
        # 风险评分权重
        self.risk_weights = {
            RiskLevel.CRITICAL: 10.0,
            RiskLevel.HIGH: 7.5,
            RiskLevel.MEDIUM: 5.0,
            RiskLevel.LOW: 2.5,
            RiskLevel.NEGLIGIBLE: 1.0
        }
        
        # 统计信息
        self.stats = {
            'total_analyses': 0,
            'ai_calls_used': 0,
            'average_processing_time': 0.0,
            'detection_accuracy': 0.0
        }
    
    async def analyze_risks(
        self,
        contract_text: str,
        contract_type: str = "general",
        risk_categories: Optional[List[RiskCategory]] = None,
        min_risk_level: RiskLevel = RiskLevel.LOW
    ) -> RiskAnalysisResult:
        """
        执行完整的风险分析
        
        Args:
            contract_text: 合同文本
            contract_type: 合同类型
            risk_categories: 关注的风险分类
            min_risk_level: 最低风险等级
            
        Returns:
            风险分析结果
        """
        start_time = time.time()
        
        logger.info(f"开始风险分析 - 合同类型: {contract_type}")
        
        try:
            # 1. 获取适用的风险规则
            applicable_rules = self._get_applicable_rules(
                contract_type, risk_categories, min_risk_level
            )
            logger.info(f"找到 {len(applicable_rules)} 个适用风险规则")
            
            # 2. 执行风险检测
            risk_points = await self._detect_risks(
                contract_text, applicable_rules, contract_type
            )
            
            # 3. 风险评估和聚合
            risk_assessment = self._assess_risks(risk_points, contract_text)
            
            # 4. 构建分析结果
            processing_duration = time.time() - start_time
            
            result = RiskAnalysisResult(
                contract_type=contract_type,
                risk_points=risk_points,
                risk_assessment=risk_assessment,
                processing_duration=processing_duration,
                rules_applied=len(applicable_rules),
                ai_calls_used=self.stats['ai_calls_used'],
                detection_confidence=self._calculate_detection_confidence(risk_points),
                coverage_score=self._calculate_coverage_score(applicable_rules, risk_points)
            )
            
            # 更新统计信息
            self._update_stats(processing_duration)
            
            logger.info(f"风险分析完成 - 耗时: {processing_duration:.2f}秒")
            logger.info(f"识别风险: {len(risk_points)}, 整体风险等级: {risk_assessment.overall_risk_level}")
            
            return result
            
        except Exception as e:
            logger.error(f"风险分析失败: {e}")
            raise
    
    def _get_applicable_rules(
        self,
        contract_type: str,
        risk_categories: Optional[List[RiskCategory]],
        min_risk_level: RiskLevel
    ) -> List[RiskRule]:
        """获取适用的风险规则"""
        
        # 获取合同类型相关的规则
        rules = self.knowledge_base.get_rules_by_contract_type(contract_type)
        
        # 按风险分类过滤
        if risk_categories:
            rules = [rule for rule in rules if rule.risk_category in risk_categories]
        
        # 按风险等级过滤
        min_weight = self.risk_weights[min_risk_level]
        rules = [rule for rule in rules 
                if self.risk_weights[rule.base_risk_level] >= min_weight]
        
        return rules
    
    async def _detect_risks(
        self,
        contract_text: str,
        rules: List[RiskRule],
        contract_type: str
    ) -> List[RiskPoint]:
        """检测风险点"""
        
        all_risk_points = []
        
        for rule in rules:
            # 执行规则匹配
            rule_risks = await self._apply_risk_rule(contract_text, rule)
            all_risk_points.extend(rule_risks)
        
        # AI增强验证（如果启用）
        if self.qwen_client and self.config['enable_ai_verification']:
            verified_risks = await self._ai_verify_risks(
                contract_text, all_risk_points, contract_type
            )
            all_risk_points = verified_risks
        
        # 去重和聚合
        aggregated_risks = self._aggregate_risks(all_risk_points)
        
        # 按风险评分排序
        aggregated_risks.sort(key=lambda x: x.risk_score, reverse=True)
        
        return aggregated_risks
    
    async def _apply_risk_rule(
        self,
        contract_text: str,
        rule: RiskRule
    ) -> List[RiskPoint]:
        """应用单个风险规则"""
        
        risk_points = []
        
        # 关键词匹配
        keyword_matches = self._keyword_risk_detection(contract_text, rule)
        risk_points.extend(keyword_matches)
        
        # 正则表达式匹配
        regex_matches = self._regex_risk_detection(contract_text, rule)
        risk_points.extend(regex_matches)
        
        # 语义模式匹配（如果有AI客户端）
        if self.qwen_client and rule.semantic_patterns:
            semantic_matches = await self._semantic_risk_detection(contract_text, rule)
            risk_points.extend(semantic_matches)
        
        return risk_points
    
    def _keyword_risk_detection(
        self,
        contract_text: str,
        rule: RiskRule
    ) -> List[RiskPoint]:
        """基于关键词的风险检测"""
        
        risk_points = []
        text_lower = contract_text.lower()
        
        for keyword in rule.keywords:
            keyword_lower = keyword.lower()
            
            # 查找所有匹配位置
            start = 0
            while True:
                pos = text_lower.find(keyword_lower, start)
                if pos == -1:
                    break
                
                # 提取上下文
                context_start = max(0, pos - self.config['context_window'])
                context_end = min(len(contract_text), pos + len(keyword) + self.config['context_window'])
                context = contract_text[context_start:context_end]
                matched_text = contract_text[pos:pos + len(keyword)]
                
                # 计算风险评分
                risk_score = self._calculate_risk_score(rule, context, "keyword")
                
                if risk_score >= self.config['min_confidence'] * 10:
                    risk_point = RiskPoint(
                        id=str(uuid.uuid4()),
                        rule_id=rule.id,
                        risk_name=rule.rule_name,
                        risk_category=rule.risk_category,
                        risk_level=rule.base_risk_level,
                        matched_text=matched_text,
                        start_position=pos,
                        end_position=pos + len(keyword),
                        context=context,
                        impact_level=rule.impact_level,
                        probability=rule.probability,
                        risk_score=risk_score,
                        confidence=min(risk_score / 10, 1.0),
                        description=rule.description,
                        potential_consequences=rule.potential_consequences,
                        mitigation_strategies=rule.mitigation_strategies,
                        recommended_actions=rule.recommended_actions,
                        urgency_level=self._determine_urgency(rule.base_risk_level),
                        legal_basis=rule.legal_basis,
                        detection_method="keyword_matching"
                    )
                    risk_points.append(risk_point)
                
                start = pos + 1
        
        return risk_points
    
    def _regex_risk_detection(
        self,
        contract_text: str,
        rule: RiskRule
    ) -> List[RiskPoint]:
        """基于正则表达式的风险检测"""
        
        risk_points = []
        
        for pattern in rule.regex_patterns:
            try:
                matches = re.finditer(pattern, contract_text, re.IGNORECASE | re.MULTILINE)
                
                for match in matches:
                    # 提取上下文
                    context_start = max(0, match.start() - self.config['context_window'])
                    context_end = min(len(contract_text), match.end() + self.config['context_window'])
                    context = contract_text[context_start:context_end]
                    
                    # 计算风险评分
                    risk_score = self._calculate_risk_score(rule, context, "regex")
                    
                    if risk_score >= self.config['min_confidence'] * 10:
                        risk_point = RiskPoint(
                            id=str(uuid.uuid4()),
                            rule_id=rule.id,
                            risk_name=rule.rule_name,
                            risk_category=rule.risk_category,
                            risk_level=rule.base_risk_level,
                            matched_text=match.group(),
                            start_position=match.start(),
                            end_position=match.end(),
                            context=context,
                            impact_level=rule.impact_level,
                            probability=rule.probability,
                            risk_score=risk_score,
                            confidence=min(risk_score / 10, 1.0),
                            description=rule.description,
                            potential_consequences=rule.potential_consequences,
                            mitigation_strategies=rule.mitigation_strategies,
                            recommended_actions=rule.recommended_actions,
                            urgency_level=self._determine_urgency(rule.base_risk_level),
                            legal_basis=rule.legal_basis,
                            detection_method="regex_matching"
                        )
                        risk_points.append(risk_point)
                        
            except re.error as e:
                logger.error(f"正则表达式错误 {pattern}: {e}")
        
        return risk_points
    
    async def _semantic_risk_detection(
        self,
        contract_text: str,
        rule: RiskRule
    ) -> List[RiskPoint]:
        """基于语义模式的风险检测"""
        
        if not self.qwen_client:
            return []
        
        try:
            # 构建语义检测提示
            prompt = self._build_semantic_risk_prompt(contract_text, rule)
            
            # 调用AI API
            response = await self.qwen_client._call_api(prompt, max_tokens=800)
            
            # 解析响应
            risk_points = self._parse_semantic_risk_response(response, rule, contract_text)
            
            return risk_points
            
        except Exception as e:
            logger.error(f"语义风险检测失败 {rule.rule_name}: {e}")
            return []
    
    def _build_semantic_risk_prompt(self, contract_text: str, rule: RiskRule) -> str:
        """构建语义风险检测提示"""
        return f"""
请分析以下合同文本，识别与指定风险相关的内容。

风险规则信息：
- 风险名称：{rule.rule_name}
- 风险分类：{rule.risk_category.value}
- 风险描述：{rule.description}
- 风险说明：{rule.risk_explanation}
- 语义模式：{', '.join(rule.semantic_patterns)}

合同文本（前3000字符）：
{contract_text[:3000]}

请仔细分析合同文本，识别可能存在的风险点。
注意：
1. 重点关注风险的实质内容而非表面文字
2. 考虑条款的缺失、模糊或不当表述
3. 评估风险的严重程度和影响范围

请以JSON格式回复：
{{
    "risks_found": [
        {{
            "matched_text": "相关文本内容",
            "start_position": 开始位置,
            "end_position": 结束位置,
            "risk_score": 7.5,
            "confidence": 0.85,
            "risk_reason": "风险识别原因",
            "severity_assessment": "严重程度评估"
        }}
    ]
}}

如果没有发现相关风险，请返回空的risks_found数组。
"""
    
    def _parse_semantic_risk_response(
        self,
        response: str,
        rule: RiskRule,
        contract_text: str
    ) -> List[RiskPoint]:
        """解析语义风险检测响应"""
        
        try:
            import json
            
            # 提取JSON部分
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                result = json.loads(json_str)
                
                risk_points = []
                for risk_data in result.get('risks_found', []):
                    risk_score = risk_data.get('risk_score', 5.0)
                    confidence = risk_data.get('confidence', 0.7)
                    
                    if confidence >= self.config['min_confidence']:
                        risk_point = RiskPoint(
                            id=str(uuid.uuid4()),
                            rule_id=rule.id,
                            risk_name=rule.rule_name,
                            risk_category=rule.risk_category,
                            risk_level=rule.base_risk_level,
                            matched_text=risk_data.get('matched_text', ''),
                            start_position=risk_data.get('start_position', 0),
                            end_position=risk_data.get('end_position', 0),
                            context=risk_data.get('matched_text', ''),
                            impact_level=rule.impact_level,
                            probability=rule.probability,
                            risk_score=risk_score,
                            confidence=confidence,
                            description=f"{rule.description} - {risk_data.get('risk_reason', '')}",
                            potential_consequences=rule.potential_consequences,
                            mitigation_strategies=rule.mitigation_strategies,
                            recommended_actions=rule.recommended_actions,
                            urgency_level=self._determine_urgency(rule.base_risk_level),
                            legal_basis=rule.legal_basis,
                            detection_method="semantic_analysis"
                        )
                        risk_points.append(risk_point)
                
                return risk_points
                
        except Exception as e:
            logger.error(f"解析语义风险响应失败: {e}")
        
        return []
    
    def _calculate_risk_score(
        self,
        rule: RiskRule,
        context: str,
        detection_method: str
    ) -> float:
        """计算风险评分"""
        
        # 基础评分
        base_score = self.risk_weights[rule.base_risk_level]
        
        # 影响程度调整
        impact_multiplier = {
            RiskImpact.SEVERE: 1.2,
            RiskImpact.MAJOR: 1.1,
            RiskImpact.MODERATE: 1.0,
            RiskImpact.MINOR: 0.9,
            RiskImpact.MINIMAL: 0.8
        }
        base_score *= impact_multiplier[rule.impact_level]
        
        # 概率调整
        probability_multiplier = {
            RiskProbability.VERY_HIGH: 1.2,
            RiskProbability.HIGH: 1.1,
            RiskProbability.MEDIUM: 1.0,
            RiskProbability.LOW: 0.9,
            RiskProbability.VERY_LOW: 0.8
        }
        base_score *= probability_multiplier[rule.probability]
        
        # 检测方法调整
        method_confidence = {
            "keyword": 0.8,
            "regex": 0.9,
            "semantic": 1.0
        }
        base_score *= method_confidence.get(detection_method, 0.8)
        
        # 上下文相关性调整
        context_bonus = self._calculate_context_relevance(rule, context)
        base_score += context_bonus
        
        return min(base_score, 10.0)
    
    def _calculate_context_relevance(self, rule: RiskRule, context: str) -> float:
        """计算上下文相关性加分"""
        
        context_lower = context.lower()
        relevance_score = 0.0
        
        # 检查相关关键词
        for keyword in rule.keywords:
            if keyword.lower() in context_lower:
                relevance_score += 0.1
        
        # 检查语义模式
        for pattern in rule.semantic_patterns:
            if pattern.lower() in context_lower:
                relevance_score += 0.2
        
        return min(relevance_score, 1.0)
    
    def _determine_urgency(self, risk_level: RiskLevel) -> str:
        """确定紧急程度"""
        urgency_map = {
            RiskLevel.CRITICAL: "立即处理",
            RiskLevel.HIGH: "优先处理",
            RiskLevel.MEDIUM: "及时处理",
            RiskLevel.LOW: "适时处理",
            RiskLevel.NEGLIGIBLE: "可延后处理"
        }
        return urgency_map[risk_level]
    
    async def _ai_verify_risks(
        self,
        contract_text: str,
        risk_points: List[RiskPoint],
        contract_type: str
    ) -> List[RiskPoint]:
        """AI验证风险点"""
        
        if not risk_points or not self.qwen_client:
            return risk_points
        
        try:
            # 构建验证提示
            risk_summaries = []
            for risk in risk_points[:10]:  # 限制验证数量
                risk_summaries.append({
                    "risk_name": risk.risk_name,
                    "matched_text": risk.matched_text,
                    "risk_score": risk.risk_score
                })
            
            prompt = f"""
请验证以下识别的风险点是否真实有效：

合同类型：{contract_type}

识别的风险点：
{json.dumps(risk_summaries, ensure_ascii=False, indent=2)}

合同文本（前2000字符）：
{contract_text[:2000]}

请对每个风险点进行验证，判断是否为真实风险。

请以JSON格式回复：
{{
    "verified_risks": [
        {{
            "risk_index": 0,
            "is_valid": true,
            "confidence": 0.85,
            "adjusted_score": 7.5,
            "verification_reason": "验证理由"
        }}
    ]
}}
"""
            
            response = await self.qwen_client._call_api(prompt, max_tokens=1000)
            
            # 解析验证结果
            verified_risks = self._parse_verification_response(response, risk_points)
            return verified_risks
            
        except Exception as e:
            logger.error(f"AI风险验证失败: {e}")
            return risk_points
    
    def _parse_verification_response(
        self,
        response: str,
        risk_points: List[RiskPoint]
    ) -> List[RiskPoint]:
        """解析AI验证响应"""
        
        try:
            import json
            
            json_start = response.find('{')
            json_end = response.rfind('}') + 
