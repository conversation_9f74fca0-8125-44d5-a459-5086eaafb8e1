# risk_analysis/models.py
"""
风险点识别系统 - 数据模型定义
多维度合同风险识别和评估
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any
from datetime import datetime
from enum import Enum

class RiskCategory(str, Enum):
    """风险分类枚举"""
    LEGAL = "legal"           # 法律风险
    COMMERCIAL = "commercial" # 商业风险
    FINANCIAL = "financial"   # 财务风险
    OPERATIONAL = "operational" # 操作风险
    COMPLIANCE = "compliance" # 合规风险
    TECHNICAL = "technical"   # 技术风险
    REPUTATION = "reputation" # 声誉风险

class RiskLevel(str, Enum):
    """风险等级枚举"""
    CRITICAL = "critical"     # 严重风险
    HIGH = "high"            # 高风险
    MEDIUM = "medium"        # 中风险
    LOW = "low"              # 低风险
    NEGLIGIBLE = "negligible" # 可忽略风险

class RiskImpact(str, Enum):
    """风险影响程度"""
    SEVERE = "severe"        # 严重影响
    MAJOR = "major"          # 重大影响
    MODERATE = "moderate"    # 中等影响
    MINOR = "minor"          # 轻微影响
    MINIMAL = "minimal"      # 最小影响

class RiskProbability(str, Enum):
    """风险发生概率"""
    VERY_HIGH = "very_high"  # 很高概率 (>80%)
    HIGH = "high"            # 高概率 (60-80%)
    MEDIUM = "medium"        # 中等概率 (30-60%)
    LOW = "low"              # 低概率 (10-30%)
    VERY_LOW = "very_low"    # 很低概率 (<10%)

class RiskRule(BaseModel):
    """风险识别规则"""
    id: Optional[int] = Field(None, description="规则ID")
    rule_name: str = Field(..., description="规则名称")
    risk_category: RiskCategory = Field(..., description="风险分类")
    contract_types: List[str] = Field(..., description="适用合同类型")
    
    # 检测规则
    keywords: List[str] = Field(default=[], description="风险关键词")
    regex_patterns: List[str] = Field(default=[], description="正则表达式")
    semantic_patterns: List[str] = Field(default=[], description="语义模式")
    
    # 风险评估
    base_risk_level: RiskLevel = Field(..., description="基础风险等级")
    impact_level: RiskImpact = Field(..., description="影响程度")
    probability: RiskProbability = Field(..., description="发生概率")
    
    # 规则描述
    description: str = Field(..., description="风险描述")
    risk_explanation: str = Field(..., description="风险说明")
    potential_consequences: List[str] = Field(default=[], description="潜在后果")
    
    # 缓解建议
    mitigation_strategies: List[str] = Field(default=[], description="缓解策略")
    recommended_actions: List[str] = Field(default=[], description="建议措施")
    
    # 法律依据
    legal_basis: Optional[str] = Field(None, description="法律依据")
    regulatory_requirements: List[str] = Field(default=[], description="监管要求")
    
    # 元数据
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    is_active: bool = Field(True, description="是否启用")

class RiskPoint(BaseModel):
    """识别的风险点"""
    id: Optional[str] = Field(None, description="风险点ID")
    rule_id: int = Field(..., description="触发的规则ID")
    risk_name: str = Field(..., description="风险名称")
    risk_category: RiskCategory = Field(..., description="风险分类")
    risk_level: RiskLevel = Field(..., description="风险等级")
    
    # 位置信息
    matched_text: str = Field(..., description="匹配的文本")
    start_position: int = Field(..., description="开始位置")
    end_position: int = Field(..., description="结束位置")
    paragraph_index: Optional[int] = Field(None, description="段落索引")
    context: Optional[str] = Field(None, description="上下文")
    
    # 风险评估
    impact_level: RiskImpact = Field(..., description="影响程度")
    probability: RiskProbability = Field(..., description="发生概率")
    risk_score: float = Field(..., ge=0.0, le=10.0, description="风险评分")
    confidence: float = Field(..., ge=0.0, le=1.0, description="识别置信度")
    
    # 风险详情
    description: str = Field(..., description="风险描述")
    potential_consequences: List[str] = Field(default=[], description="潜在后果")
    affected_parties: List[str] = Field(default=[], description="受影响方")
    
    # 缓解建议
    mitigation_strategies: List[str] = Field(default=[], description="缓解策略")
    recommended_actions: List[str] = Field(default=[], description="建议措施")
    urgency_level: str = Field(..., description="紧急程度")
    
    # 相关信息
    related_clauses: List[str] = Field(default=[], description="相关条款")
    legal_basis: Optional[str] = Field(None, description="法律依据")
    
    # 检测信息
    detection_method: str = Field(..., description="检测方法")
    detection_time: datetime = Field(default_factory=datetime.now, description="检测时间")

class RiskAssessment(BaseModel):
    """风险评估结果"""
    overall_risk_level: RiskLevel = Field(..., description="整体风险等级")
    total_risk_score: float = Field(..., ge=0.0, le=10.0, description="总风险评分")
    risk_distribution: Dict[str, int] = Field(..., description="风险分布")
    
    # 分类风险评估
    category_risks: Dict[RiskCategory, Dict[str, Any]] = Field(default={}, description="分类风险")
    
    # 关键风险
    critical_risks: List[RiskPoint] = Field(default=[], description="严重风险")
    high_risks: List[RiskPoint] = Field(default=[], description="高风险")
    
    # 统计信息
    total_risks: int = Field(..., description="风险总数")
    risks_by_category: Dict[str, int] = Field(..., description="按分类统计")
    risks_by_level: Dict[str, int] = Field(..., description="按等级统计")
    
    # 评估建议
    priority_actions: List[str] = Field(default=[], description="优先措施")
    risk_mitigation_plan: List[str] = Field(default=[], description="风险缓解计划")

class RiskAnalysisResult(BaseModel):
    """风险分析结果"""
    contract_id: Optional[str] = Field(None, description="合同ID")
    contract_type: str = Field(..., description="合同类型")
    analysis_time: datetime = Field(default_factory=datetime.now, description="分析时间")
    
    # 识别的风险点
    risk_points: List[RiskPoint] = Field(default=[], description="风险点列表")
    
    # 风险评估
    risk_assessment: RiskAssessment = Field(..., description="风险评估")
    
    # 处理信息
    processing_duration: float = Field(..., description="处理耗时")
    rules_applied: int = Field(..., description="应用的规则数")
    ai_calls_used: int = Field(0, description="AI调用次数")
    
    # 质量指标
    detection_confidence: float = Field(..., ge=0.0, le=1.0, description="检测置信度")
    coverage_score: float = Field(..., ge=0.0, le=1.0, description="覆盖率评分")

class RiskMitigationPlan(BaseModel):
    """风险缓解计划"""
    risk_point_id: str = Field(..., description="风险点ID")
    mitigation_type: str = Field(..., description="缓解类型")
    priority: int = Field(..., ge=1, le=5, description="优先级")
    
    # 缓解措施
    actions: List[str] = Field(..., description="具体措施")
    timeline: str = Field(..., description="时间安排")
    responsible_party: str = Field(..., description="负责方")
    
    # 成本和效果
    estimated_cost: Optional[float] = Field(None, description="预估成本")
    expected_effectiveness: float = Field(..., ge=0.0, le=1.0, description="预期效果")
    
    # 监控指标
    success_metrics: List[str] = Field(default=[], description="成功指标")
    review_schedule: str = Field(..., description="审查计划")

# API请求响应模型
class RiskAnalysisRequest(BaseModel):
    """风险分析请求"""
    contract_text: str = Field(..., description="合同文本")
    contract_type: str = Field(..., description="合同类型")
    
    # 分析配置
    risk_categories: Optional[List[RiskCategory]] = Field(None, description="关注的风险分类")
    min_risk_level: RiskLevel = Field(RiskLevel.LOW, description="最低风险等级")
    enable_ai_analysis: bool = Field(True, description="启用AI分析")
    detailed_analysis: bool = Field(False, description="详细分析")
    
    # 可选参数
    include_mitigation_plan: bool = Field(True, description="包含缓解计划")
    context_info: Optional[Dict[str, Any]] = Field(None, description="上下文信息")

class RiskAnalysisResponse(BaseModel):
    """风险分析响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    result: Optional[RiskAnalysisResult] = Field(None, description="分析结果")
    mitigation_plans: List[RiskMitigationPlan] = Field(default=[], description="缓解计划")
    
    # 元数据
    request_id: Optional[str] = Field(None, description="请求ID")
    processing_time: float = Field(..., description="处理时间")
    api_version: str = Field("1.0", description="API版本")

class RiskRuleQuery(BaseModel):
    """风险规则查询"""
    categories: Optional[List[RiskCategory]] = Field(None, description="风险分类")
    contract_types: Optional[List[str]] = Field(None, description="合同类型")
    risk_levels: Optional[List[RiskLevel]] = Field(None, description="风险等级")
    keyword: Optional[str] = Field(None, description="关键词搜索")
    
    # 分页参数
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页大小")

class RiskStatistics(BaseModel):
    """风险统计信息"""
    total_rules: int = Field(..., description="规则总数")
    rules_by_category: Dict[str, int] = Field(..., description="按分类统计")
    rules_by_level: Dict[str, int] = Field(..., description="按等级统计")
    
    # 使用统计
    most_triggered_rules: List[Dict[str, Any]] = Field(default=[], description="最常触发的规则")
    detection_accuracy: float = Field(..., description="检测准确率")
    
    # 更新信息
    last_updated: datetime = Field(default_factory=datetime.now, description="最后更新时间")
