"""
pytest配置文件

定义全局测试夹具和配置
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock

from src.config import Settings
from src.core.ai_client import QwenAPIClient
from src.element_extraction import <PERSON><PERSON><PERSON>x<PERSON>or, ElementConfigManager
from src.clause_detection import ClauseDetectionEngine, SemanticMatcher
from src.risk_analysis import RiskAnalyzer, RiskKnowledgeBase

@pytest.fixture(scope="session")
def event_loop():
    """事件循环夹具"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def test_settings():
    """测试配置夹具"""
    return Settings(
        environment="test",
        debug=True,
        database_url="sqlite:///:memory:",
        redis_url="redis://localhost:6379/15",  # 使用测试数据库
        log_level="DEBUG"
    )

@pytest.fixture
def temp_dir():
    """临时目录夹具"""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path)

@pytest.fixture
def mock_qwen_client():
    """模拟千问API客户端"""
    client = Mock(spec=QwenAPIClient)
    
    # 模拟API响应
    client.analyze_text_async.return_value = {
        "analysis": "模拟分析结果",
        "confidence": 0.85,
        "elements": []
    }
    
    client._call_api.return_value = "模拟API响应"
    
    return client

@pytest.fixture
def element_extractor(mock_qwen_client):
    """要素提取器夹具"""
    config_manager = Mock(spec=ElementConfigManager)
    return ElementExtractor(config_manager, mock_qwen_client)

@pytest.fixture
def clause_detector(mock_qwen_client):
    """条款检测器夹具"""
    return ClauseDetectionEngine(mock_qwen_client)

@pytest.fixture
def semantic_matcher():
    """语义匹配器夹具"""
    return SemanticMatcher()

@pytest.fixture
def risk_analyzer(mock_qwen_client):
    """风险分析器夹具"""
    return RiskAnalyzer(mock_qwen_client)

@pytest.fixture
def sample_contract_text():
    """示例合同文本"""
    return """
    甲方：ABC科技有限公司
    乙方：XYZ服务有限公司
    
    根据《中华人民共和国合同法》等相关法律法规，甲乙双方本着平等、自愿、公平、诚信的原则，
    就技术服务事宜达成如下协议：
    
    第一条 服务内容
    乙方为甲方提供软件开发服务，包括但不限于系统设计、编码实现、测试部署等。
    
    第二条 服务期限
    本合同服务期限为2024年1月1日至2024年12月31日。
    
    第三条 服务费用
    甲方应向乙方支付服务费用总计人民币100万元，分四期支付。
    
    第四条 违约责任
    任何一方违反本合同约定，应承担相应的违约责任。
    
    第五条 争议解决
    因本合同引起的争议，双方应友好协商解决；协商不成的，
    提交北京仲裁委员会仲裁。
    
    本合同一式两份，甲乙双方各执一份，具有同等法律效力。
    
    甲方：ABC科技有限公司（盖章）
    乙方：XYZ服务有限公司（盖章）
    日期：2024年1月1日
    """

@pytest.fixture
def sample_element_config():
    """示例要素配置"""
    return {
        "contract_type": "service",
        "elements": [
            {
                "name": "甲方",
                "category": "party",
                "extraction_rules": {
                    "keywords": ["甲方"],
                    "regex": r"甲方[：:]\s*([^\\n]+)"
                }
            },
            {
                "name": "乙方", 
                "category": "party",
                "extraction_rules": {
                    "keywords": ["乙方"],
                    "regex": r"乙方[：:]\s*([^\\n]+)"
                }
            },
            {
                "name": "合同金额",
                "category": "financial",
                "extraction_rules": {
                    "keywords": ["费用", "金额", "价款"],
                    "regex": r"人民币\s*([0-9]+(?:\.[0-9]+)?)\s*[万元]"
                }
            }
        ]
    }

@pytest.fixture
def sample_standard_clauses():
    """示例标准条款"""
    return [
        {
            "id": "payment_001",
            "name": "付款条款",
            "category": "payment",
            "content": "甲方应在收到发票后30日内支付款项",
            "keywords": ["付款", "支付", "发票", "30日"],
            "importance_level": "high"
        },
        {
            "id": "liability_001",
            "name": "责任限制条款", 
            "category": "liability",
            "content": "任何一方的赔偿责任不超过合同总金额",
            "keywords": ["责任", "赔偿", "合同总金额"],
            "importance_level": "critical"
        }
    ]

# 测试标记
def pytest_configure(config):
    """pytest配置"""
    config.addinivalue_line("markers", "unit: 单元测试")
    config.addinivalue_line("markers", "integration: 集成测试")
    config.addinivalue_line("markers", "e2e: 端到端测试")
    config.addinivalue_line("markers", "slow: 慢速测试")
    config.addinivalue_line("markers", "ai: 需要AI服务的测试")