"""
AI合同审核系统 - 测试套件

包含完整的测试体系：
- unit: 单元测试
- integration: 集成测试  
- e2e: 端到端测试
- fixtures: 测试数据和夹具

测试覆盖：
- 要素提取模块
- 条款检测模块
- 风险分析模块
- API接口
- 工具函数
"""

import pytest
import asyncio
from pathlib import Path

# 测试配置
TEST_DATA_DIR = Path(__file__).parent / "fixtures"
SAMPLE_CONTRACTS_DIR = TEST_DATA_DIR / "contracts"
EXPECTED_RESULTS_DIR = TEST_DATA_DIR / "expected_results"

# 异步测试支持
@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环用于异步测试"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

# 测试工具函数
def load_test_contract(filename: str) -> str:
    """加载测试合同文件"""
    file_path = SAMPLE_CONTRACTS_DIR / filename
    if file_path.exists():
        return file_path.read_text(encoding='utf-8')
    raise FileNotFoundError(f"测试合同文件不存在: {filename}")

def load_expected_result(filename: str) -> dict:
    """加载预期结果文件"""
    import json
    file_path = EXPECTED_RESULTS_DIR / filename
    if file_path.exists():
        return json.loads(file_path.read_text(encoding='utf-8'))
    raise FileNotFoundError(f"预期结果文件不存在: {filename}")

__all__ = [
    "TEST_DATA_DIR",
    "SAMPLE_CONTRACTS_DIR", 
    "EXPECTED_RESULTS_DIR",
    "load_test_contract",
    "load_expected_result"
]