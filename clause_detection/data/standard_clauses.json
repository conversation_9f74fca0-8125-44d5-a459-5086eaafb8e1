{"clauses": [{"id": 1, "clause_name": "合同当事方", "clause_category": "basic_info", "contract_types": ["sales", "service", "lease", "employment", "general"], "importance_level": "critical", "standard_text": "本合同由以下当事方签署：甲方（委托方）：[公司名称]，乙方（受托方）：[公司名称]", "description": "明确合同双方的身份信息", "purpose": "确定合同主体，明确权利义务承担者", "keywords": ["甲方", "乙方", "当事方", "委托方", "受托方"], "regex_patterns": [], "semantic_keywords": ["合同主体", "签约方", "合同双方"], "match_threshold": 0.7, "match_methods": ["hybrid"], "recommended_text": "甲方：[公司全称]\n地址：[详细地址]\n法定代表人：[姓名]\n联系电话：[电话号码]\n\n乙方：[公司全称]\n地址：[详细地址]\n法定代表人：[姓名]\n联系电话：[电话号码]", "alternatives": [], "created_at": "2025-09-02T10:26:26.372957", "updated_at": "2025-09-02T10:26:26.372967", "version": "1.0", "source": null}, {"id": 2, "clause_name": "付款方式", "clause_category": "payment_terms", "contract_types": ["sales", "service", "lease"], "importance_level": "critical", "standard_text": "甲方应按照约定的付款方式和期限向乙方支付合同款项", "description": "规定付款的方式、时间和条件", "purpose": "确保款项按时足额支付，避免付款纠纷", "keywords": ["付款", "支付", "款项", "费用", "价款"], "regex_patterns": ["付款.*?方式", "支付.*?期限", ".*?元.*?支付"], "semantic_keywords": ["付款条件", "支付安排", "结算方式"], "match_threshold": 0.7, "match_methods": ["hybrid"], "recommended_text": "付款方式：[银行转账/现金/支票等]\n付款期限：[具体时间]\n付款账户：[银行账户信息]\n逾期付款责任：[违约金或利息计算方式]", "alternatives": [], "created_at": "2025-09-02T10:26:26.373448", "updated_at": "2025-09-02T10:26:26.373457", "version": "1.0", "source": null}, {"id": 3, "clause_name": "违约责任", "clause_category": "liability", "contract_types": ["sales", "service", "lease", "employment", "general"], "importance_level": "critical", "standard_text": "任何一方违反本合同约定的，应承担相应的违约责任", "description": "规定违约行为及其法律后果", "purpose": "约束当事方履行合同义务，保护守约方利益", "keywords": ["违约", "责任", "赔偿", "损失", "违约金"], "regex_patterns": [], "semantic_keywords": ["违约后果", "法律责任", "损害赔偿"], "match_threshold": 0.7, "match_methods": ["hybrid"], "recommended_text": "1. 违约方应向守约方支付违约金，违约金为合同总金额的[百分比]%\n2. 违约金不足以弥补损失的，违约方还应赔偿超出部分\n3. 因违约导致合同解除的，违约方还应承担相应的损害赔偿责任", "alternatives": [], "created_at": "2025-09-02T10:26:26.373866", "updated_at": "2025-09-02T10:26:26.373873", "version": "1.0", "source": null}, {"id": 4, "clause_name": "争议解决", "clause_category": "dispute_resolution", "contract_types": ["sales", "service", "lease", "employment", "general"], "importance_level": "important", "standard_text": "因本合同引起的争议，双方应协商解决；协商不成的，可向有管辖权的人民法院起诉", "description": "规定解决合同争议的方式和程序", "purpose": "为争议解决提供明确的途径和程序", "keywords": ["争议", "纠纷", "仲裁", "诉讼", "协商"], "regex_patterns": [], "semantic_keywords": ["争议处理", "纠纷解决", "法律途径"], "match_threshold": 0.7, "match_methods": ["hybrid"], "recommended_text": "1. 双方应首先通过友好协商解决争议\n2. 协商不成的，可以向[仲裁委员会]申请仲裁\n3. 也可以直接向[法院名称]提起诉讼\n4. 适用中华人民共和国法律", "alternatives": [], "created_at": "2025-09-02T10:26:26.374457", "updated_at": "2025-09-02T10:26:26.374466", "version": "1.0", "source": null}, {"id": 5, "clause_name": "合同生效", "clause_category": "basic_info", "contract_types": ["sales", "service", "lease", "employment", "general"], "importance_level": "important", "standard_text": "本合同自双方签字盖章之日起生效", "description": "规定合同生效的条件和时间", "purpose": "明确合同何时开始产生法律效力", "keywords": ["生效", "签字", "盖章", "签署"], "regex_patterns": [], "semantic_keywords": ["合同效力", "生效条件", "签约生效"], "match_threshold": 0.7, "match_methods": ["hybrid"], "recommended_text": "本合同自双方法定代表人或授权代表签字并加盖公章之日起生效，有效期至[具体日期]", "alternatives": [], "created_at": "2025-09-02T10:26:26.375233", "updated_at": "2025-09-02T10:26:26.375267", "version": "1.0", "source": null}, {"id": 6, "clause_name": "保密义务", "clause_category": "confidentiality", "contract_types": ["service", "employment", "general"], "importance_level": "important", "standard_text": "双方应对在合同履行过程中知悉的对方商业秘密承担保密义务", "description": "规定保密信息的范围和保密义务", "purpose": "保护商业秘密和敏感信息不被泄露", "keywords": ["保密", "商业秘密", "机密", "泄露"], "regex_patterns": [], "semantic_keywords": ["保密责任", "信息保护", "商业机密"], "match_threshold": 0.7, "match_methods": ["hybrid"], "recommended_text": "1. 保密信息包括但不限于：技术资料、商业计划、客户信息等\n2. 保密期限：[具体期限]\n3. 违反保密义务的，应承担相应的法律责任和经济赔偿", "alternatives": [], "created_at": "2025-09-02T10:26:26.375868", "updated_at": "2025-09-02T10:26:26.375876", "version": "1.0", "source": null}, {"id": 7, "clause_name": "不可抗力", "clause_category": "force_majeure", "contract_types": ["sales", "service", "lease", "general"], "importance_level": "optional", "standard_text": "因不可抗力导致合同无法履行的，受影响方不承担违约责任", "description": "规定不可抗力事件的处理方式", "purpose": "为不可预见的重大事件提供免责条款", "keywords": ["不可抗力", "天灾", "战争", "政府行为"], "regex_patterns": [], "semantic_keywords": ["免责事由", "意外事件", "客观原因"], "match_threshold": 0.7, "match_methods": ["hybrid"], "recommended_text": "1. 不可抗力包括：自然灾害、战争、政府行为、法律变更等\n2. 发生不可抗力应及时通知对方并提供证明\n3. 可根据影响程度延期履行、部分履行或解除合同", "alternatives": [], "created_at": "2025-09-02T10:26:26.376658", "updated_at": "2025-09-02T10:26:26.376698", "version": "1.0", "source": null}, {"id": 8, "clause_name": "知识产权", "clause_category": "intellectual_property", "contract_types": ["service"], "importance_level": "important", "standard_text": "合同履行过程中产生的知识产权归属应予明确约定", "description": "规定知识产权的归属和使用权", "purpose": "避免知识产权纠纷，保护各方合法权益", "keywords": ["知识产权", "专利", "著作权", "商标"], "regex_patterns": [], "semantic_keywords": ["产权归属", "版权", "专有权利"], "match_threshold": 0.7, "match_methods": ["hybrid"], "recommended_text": "1. 乙方在履行本合同过程中开发的技术成果，知识产权归[甲方/乙方/双方共有]\n2. 使用第三方知识产权的，应确保合法授权\n3. 侵犯第三方知识产权的责任由[责任方]承担", "alternatives": [], "created_at": "2025-09-02T10:26:26.377490", "updated_at": "2025-09-02T10:26:26.377499", "version": "1.0", "source": null}], "next_id": 9, "last_updated": "2025-09-02T10:26:26.377665"}