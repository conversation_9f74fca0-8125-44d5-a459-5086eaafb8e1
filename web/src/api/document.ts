import request from '@/utils/request'

export interface DocumentInfo {
  id: string
  filename: string
  size: number
  status: 'uploading' | 'processing' | 'completed' | 'error'
  uploadTime: string
  processTime?: string
  progress?: number
}

export interface UploadResponse {
  success: boolean
  file_id: string
  filename: string
  size: number
  message: string
}

export interface DocumentListResponse {
  success: boolean
  contracts: DocumentInfo[]
}

// 上传文档
export const uploadDocument = async (
  file: File,
  onProgress?: (progress: number) => void
): Promise<UploadResponse> => {
  const formData = new FormData()
  formData.append('file', file)

  return request.post<UploadResponse>('/v1/contracts/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent: ProgressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    },
  })
}

// 获取文档列表
export const getDocumentList = async (): Promise<DocumentListResponse> => {
  return request.get<DocumentListResponse>('/v1/contracts/list')
}

// 删除文档
export const deleteDocument = async (id: string): Promise<void> => {
  return request.delete(`/v1/contracts/${id}`)
}

// 批量删除文档
export const batchDeleteDocuments = async (ids: string[]): Promise<void> => {
  return request.post('/v1/contracts/batch-delete', { ids })
}

// 开始处理文档
export const processDocument = async (id: string): Promise<void> => {
  return request.post(`/v1/contracts/${id}/process`)
}

// 批量处理文档
export const batchProcessDocuments = async (ids: string[]): Promise<void> => {
  return request.post('/v1/contracts/batch-process', { ids })
}


// 获取文档详情
export const getDocumentDetail = async (id: string): Promise<DocumentInfo> => {
  return request.get<DocumentInfo>(`/v1/contracts/${id}`)
}
