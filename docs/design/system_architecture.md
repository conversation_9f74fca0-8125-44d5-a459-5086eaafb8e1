# AI合同审核系统 - 设计文档

## 1. 系统架构设计

### 1.1 整体架构（当前实现状态）
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面层     │    │   API网关层     │    │   业务服务层     │
│  (计划中)       │◄──►│   FastAPI ✅    │◄──►│  已完成 ✅      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AI算法层      │    │   数据访问层     │    │   基础设施层     │
│  千问3 API ✅   │◄──►│   文件存储 ✅   │◄──►│   Docker ✅     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**当前实现状态**：
- ✅ **API网关层**：FastAPI异步框架，完整的RESTful API
- ✅ **业务服务层**：配置化要素管理、智能条款检测、风险分析
- ✅ **AI算法层**：千问3 API集成，语义分析，规则引擎降级
- ✅ **数据访问层**：文件存储，配置缓存，临时数据管理
- ✅ **基础设施层**：Docker容器化，开发环境配置
- ⏳ **前端界面层**：Vue 3 + Element Plus（下一阶段开发）

### 1.2 技术栈选择（基于MVP验证结果）

#### 1.2.1 前端技术（MVP验证 + 完整版规划）
- **MVP现状**：简单HTML界面，验证基础交互
- **完整版规划**：
  - **框架**：Vue.js 3.x + TypeScript
  - **UI组件库**：Element Plus 最新版本
  - **状态管理**：Pinia
  - **构建工具**：Vite
  - **样式方案**：Element Plus + 自定义CSS

#### 1.2.2 后端技术（已完成实现）
- **主框架**：FastAPI (Python 3.8+) ✅ 完整实现
- **异步处理**：asyncio + uvicorn ✅ 高性能异步处理
- **文件处理**：python-docx ✅ 智能文档解析
- **AI集成**：OpenAI SDK (千问3兼容) ✅ 深度集成
- **配置管理**：Pydantic Settings ✅ 类型安全配置
- **数据验证**：Pydantic Models ✅ 完整数据模型
- **中间件**：CORS、Gzip、日志 ✅ 生产级中间件
- **认证授权**：JWT + OAuth2（下一阶段规划）

#### 1.2.3 AI/ML技术栈（MVP已验证）
- **核心模型**：千问3 (Qwen) API服务 ✅ 已验证
- **API集成**：OpenAI SDK兼容接口 ✅ 已验证
- **文档处理**：python-docx ✅ 已验证
- **文本处理**：正则表达式 + 自定义规则引擎 ✅ 已验证
- **降级机制**：规则匹配备用方案 ✅ 已验证
- **错误处理**：重试机制 + 异常处理 ✅ 已验证

#### 1.2.4 数据存储（完整版规划）
- **MVP现状**：临时文件存储，无持久化
- **完整版规划**：
  - **关系数据库**：PostgreSQL（用户、审核历史）
  - **文件存储**：本地存储 / 云存储
  - **缓存**：Redis（API响应缓存）
  - **配置存储**：JSON文件 / 数据库

## 2. 系统模块设计

### 2.1 用户管理模块
```
用户管理模块
├── 用户注册/登录
├── 权限管理
├── 角色分配
└── 操作日志
```

**核心功能**（下一阶段开发）：
- JWT令牌认证
- RBAC权限控制
- 用户会话管理
- 审计日志记录

### 2.3 配置化要素管理模块（已完成实现）
```
配置化要素管理模块 ✅
├── 要素模板配置 ✅ (动态配置系统)
├── 提取规则管理 ✅ (正则、关键词、AI提示)
├── 要素关系配置 ✅ (依赖、互斥、组合、计算)
├── 验证规则引擎 ✅ (格式、范围、业务规则)
├── 配置热更新 ✅ (TTL缓存机制)
└── 导入导出功能 ✅ (批量配置管理)
```

**核心特性**：
- ✅ **完全配置化**：支持无代码配置新合同类型和要素
- ✅ **多层级分类**：动态要素分类树，支持任意层级
- ✅ **灵活提取规则**：正则表达式、关键词匹配、AI提示词
- ✅ **复杂关系处理**：要素间依赖、互斥、组合、计算关系
- ✅ **动态验证**：格式验证、范围检查、业务规则验证
- ✅ **热更新支持**：配置变更实时生效，无需重启服务
- ✅ **批量管理**：配置的导入导出和模板管理

### 2.4 智能条款检测模块（已完成实现）
```
智能条款检测模块 ✅
├── 标准条款库 ✅ (8个预置条款，支持扩展)
├── 多维度匹配 ✅ (语义、关键词、正则、混合)
├── 缺失条款检测 ✅ (AI增强的智能检测)
├── 条款推荐系统 ✅ (个性化推荐和位置建议)
├── 评分统计 ✅ (完整性、合规性、综合评分)
└── API接口 ✅ (完整的RESTful API)
```

**核心算法**：
- ✅ **语义匹配**：基于千问3 API的深度语义理解
- ✅ **关键词匹配**：高效的文本匹配算法
- ✅ **正则匹配**：精确的模式识别
- ✅ **混合匹配**：多种方法的智能组合和权重分配
- ✅ **AI验证**：千问3 API深度验证检测结果
- ✅ **缓存优化**：智能缓存减少API调用成本
- ✅ **降级机制**：AI服务不可用时自动降级到规则匹配

### 2.5 风险分析模块（已完成实现）
```
风险分析模块 ✅
├── 多维度风险分类 ✅ (法律、商业、财务、操作)
├── 智能风险检测 ✅ (关键词、正则、语义、AI验证)
├── 风险等级评估 ✅ (5级风险等级评估)
├── 风险知识库 ✅ (可扩展的风险规则库)
├── 风险聚合去重 ✅ (智能风险点聚合)
└── 风险报告生成 ✅ (结构化风险评估报告)
```

**风险分析能力**：
- ✅ **多维度分析**：法律、商业、财务、操作四大风险分类
- ✅ **智能检测**：关键词、正则、语义、AI验证多种检测方法
- ✅ **精确评估**：风险等级、影响程度、概率评估
- ✅ **知识库驱动**：可配置的风险规则和知识库
- ✅ **AI增强**：千问3 API语义风险检测和验证
- ✅ **性能优化**：缓存机制、并发处理、降级策略

### 2.2 文档处理模块（已完成实现）
```
文档处理模块 ✅
├── 文档上传 ✅ (FastAPI multipart/form-data)
├── 格式验证 ✅ (.docx/.doc格式检查，安全扫描)
├── 文本提取 ✅ (python-docx异步处理)
├── 结构化解析 ✅ (段落、表格、样式、元数据)
├── 合同类型识别 ✅ (多维度智能分类)
└── 配置化要素提取 ✅ (动态规则引擎)
```

**完整实现的技术特性**：
- ✅ **智能文档解析**：支持Word格式（.docx/.doc，50MB限制）
- ✅ **结构化分析**：段落索引、表格解析、样式识别
- ✅ **合同类型识别**：基于关键词和模式的智能分类
- ✅ **配置化要素提取**：支持多种数据类型和提取规则
- ✅ **多层级要素分类**：动态分类树结构
- ✅ **要素关系处理**：依赖、互斥、组合、计算关系
- ✅ **质量保证机制**：完整性检查、准确性验证、异常检测
- ✅ **文件安全管理**：临时文件清理、安全扫描

### 2.2.1 文本提取技术架构
```
智能合同要素提取系统架构
├── 文档输入层
│   ├── 文件上传验证
│   │   ├── 格式检查（仅支持.doc/.docx）
│   │   ├── 大小限制（最大50MB）
│   │   ├── 完整性验证（文件头校验）
│   │   └── 安全扫描（病毒检测）
│   └── 文档预处理
│       ├── Word文档解析（python-docx）
│       ├── 文档结构分析（段落/表格/样式）
│       ├── 文本标准化（编码/格式/清理）
│       └── 段落索引构建（位置映射）
├── 合同类型识别层
│   ├── 多维度分析引擎
│   │   ├── 关键词权重匹配
│   │   ├── 正则模式识别
│   │   ├── 上下文语义分析
│   │   └── 机器学习分类（可选）
│   ├── 类型评分机制
│   │   ├── 综合置信度计算
│   │   ├── 多候选类型排序
│   │   ├── 阈值过滤机制
│   │   └── 默认类型降级
│   └── 配置化规则引擎
│       ├── 合同类型定义库
│       ├── 识别规则配置
│       ├── 权重参数调优
│       └── 规则热更新支持
├── 统一要素提取层
│   ├── 要素配置加载器
│   │   ├── 基于合同类型的要素筛选
│   │   ├── 要素优先级排序
│   │   ├── 依赖关系解析
│   │   └── 配置缓存管理
│   ├── 多类型要素处理引擎
│   │   ├── 文档结构要素处理器
│   │   │   ├── 标题要素提取（位置+关键词+样式）
│   │   │   ├── 编号要素提取（模式匹配）
│   │   │   ├── 页眉页脚处理
│   │   │   └── 文档元数据提取
│   │   ├── 条款要素处理器
│   │   │   ├── 条款编号识别（多层级支持）
│   │   │   ├── 条款内容提取（去编号处理）
│   │   │   ├── 条款类型分类（语义匹配）
│   │   │   ├── 条款层级分析（主从关系）
│   │   │   └── 条款完整性验证
│   │   ├── 实体要素处理器
│   │   │   ├── 当事方信息提取
│   │   │   ├── 金额数据处理（单位转换）
│   │   │   ├── 日期时间解析（多格式支持）
│   │   │   ├── 地址信息标准化
│   │   │   └── 联系方式提取
│   │   └── 自定义要素处理器
│   │       ├── 用户定义要素类型
│   │       ├── 动态处理逻辑
│   │       ├── 扩展数据类型支持
│   │       └── 第三方处理器集成
│   ├── 正则表达式引擎
│   │   ├── 模式编译优化
│   │   ├── 匹配性能监控
│   │   ├── 模式库管理
│   │   └── 调试和测试工具
│   └── 提取结果处理
│       ├── 置信度评分算法
│       ├── 重复结果去重
│       ├── 结果质量评估
│       └── 位置信息标记
├── 要素关系分析层
│   ├── 依赖关系处理器
│   │   ├── 必需要素验证
│   │   ├── 条件依赖检查
│   │   └── 缺失要素补偿
│   ├── 互斥关系处理器
│   │   ├── 冲突检测算法
│   │   ├── 优先级解决策略
│   │   └── 置信度比较机制
│   ├── 组合关系处理器
│   │   ├── 复合要素构建
│   │   ├── 要素聚合规则
│   │   └── 组合验证逻辑
│   └── 计算关系处理器
│       ├── 公式计算引擎
│       ├── 数值运算处理
│       ├── 字符串拼接逻辑
│       └── 自定义计算函数
├── 质量保证层
│   ├── 提取质量评估
│   │   ├── 完整性检查（必需要素覆盖）
│   │   ├── 准确性验证（格式/范围校验）
│   │   ├── 一致性分析（要素间逻辑校验）
│   │   └── 异常检测（异常值识别）
│   ├── 验证规则引擎
│   │   ├── 业务规则验证
│   │   ├── 数据格式校验
│   │   ├── 逻辑关系检查
│   │   └── 自定义验证函数
│   └── 错误处理机制
│       ├── 错误分类和等级
│       ├── 自动修复策略
│       ├── 人工审核标记
│       └── 错误统计分析
└── 输出标准化层
    ├── 结果结构化
    │   ├── 分类层级构建（动态分类树）
    │   ├── 要素数据标准化
    │   ├── 元数据信息附加
    │   └── JSON格式输出
    ├── 性能指标统计
    │   ├── 提取耗时统计
    │   ├── 成功率计算
    │   ├── 置信度分布
    │   └── 错误率分析
    └── 可视化支持
        ├── 提取结果高亮显示
        ├── 要素位置标记
        ├── 关系图谱生成
        └── 统计图表输出
```

**核心技术组件**：
```python
# 智能合同要素提取核心类
class SmartContractElementExtractor:
    def __init__(self):
        self.supported_formats = ['.doc', '.docx']
        self.max_file_size = 50 * 1024 * 1024  # 50MB
        self.config_manager = ElementConfigManager()
        self.type_classifier = ContractTypeClassifier()
        self.element_processors = {
            'text': TextElementProcessor(),
            'amount': AmountElementProcessor(),
            'date': DateElementProcessor(),
            'party_info': PartyInfoElementProcessor(),
            'clause': ClauseElementProcessor()
        }

    async def extract_contract_elements(self, file_path: str) -> Dict:
        """智能合同要素提取主方法"""
        # 1. 文档输入处理
        doc_content = await self._process_document_input(file_path)

        # 2. 合同类型识别
        contract_type = await self._classify_contract_type(doc_content)

        # 3. 统一要素提取
        all_elements = await self._extract_all_elements(doc_content, contract_type)

        # 4. 要素关系分析
        analyzed_elements = await self._analyze_element_relationships(all_elements)

        # 5. 质量保证检查
        validated_elements = await self._validate_extraction_quality(analyzed_elements)

        # 6. 结果标准化输出
        standardized_result = await self._standardize_output(validated_elements, contract_type)

        return standardized_result

    async def _process_document_input(self, file_path: str) -> Dict:
        """文档输入处理"""
        # 文件验证
        await self._validate_file(file_path)

        # Word文档解析
        doc_content = await self._parse_word_document(file_path)

        # 文本预处理
        processed_content = await self._preprocess_text(doc_content)

        # 段落索引构建
        indexed_content = await self._build_paragraph_index(processed_content)

        return indexed_content

    async def _classify_contract_type(self, doc_content: Dict) -> str:
        """合同类型智能识别"""
        return await self.type_classifier.classify(doc_content)

    async def _extract_all_elements(self, doc_content: Dict, contract_type: str) -> Dict:
        """统一要素提取"""
        # 加载要素配置
        element_templates = await self.config_manager.get_element_templates(contract_type)

        extracted_elements = {}
        full_text = self._build_full_text(doc_content['paragraphs'])

        # 按要素配置进行提取
        for template in element_templates:
            element_name = template['element_name']
            element_category = template['element_category']
            data_type = template.get('data_type', 'text')

            # 选择对应的处理器
            processor = self.element_processors.get(data_type, self.element_processors['text'])

            # 执行要素提取
            element_results = await processor.extract(
                full_text,
                template['element_config'],
                template,
                doc_content['paragraphs']
            )

            # 构建分类结构
            if element_results:
                category_path = element_category.split('/')
                current_level = extracted_elements

                for category in category_path:
                    if category not in current_level:
                        current_level[category] = {}
                    current_level = current_level[category]

                current_level[element_name] = element_results

        return extracted_elements

# 合同类型分类器
class ContractTypeClassifier:
    def __init__(self):
        self.config_cache = {}

    async def classify(self, doc_content: Dict) -> str:
        """智能合同类型分类"""
        # 构建分析文本（前20段）
        paragraphs = doc_content['paragraphs'][:20]
        analysis_text = ' '.join([p['cleaned_text'] for p in paragraphs])

        # 加载分类配置
        type_config = await self._load_classification_config()

        # 多维度评分
        type_scores = {}
        for contract_type in type_config['contract_types']:
            score = await self._calculate_type_score(analysis_text, contract_type)
            if score > 0:
                type_scores[contract_type['type']] = score

        # 返回最佳匹配类型
        if type_scores:
            best_type = max(type_scores.keys(), key=lambda k: type_scores[k])
            return best_type

        return 'general'

    async def _calculate_type_score(self, text: str, contract_type: Dict) -> float:
        """计算合同类型匹配分数"""
        import re
        score = 0.0

        # 关键词匹配评分
        for keyword_config in contract_type['keywords']:
            if keyword_config['keyword'] in text:
                score += keyword_config['weight']

        # 模式匹配评分
        for pattern_config in contract_type.get('patterns', []):
            if re.search(pattern_config['pattern'], text):
                score += pattern_config['weight']

        return score

# 条款要素处理器
class ClauseElementProcessor:
    def __init__(self):
        self.clause_patterns = {
            'main_clause': [
                r'^第([一二三四五六七八九十\d]+)条\s*(.*)$',
                r'^([一二三四五六七八九十]+)、\s*(.*)$',
                r'^(\d+)\.\s*(.*)$'
            ],
            'sub_clause': [
                r'^第([一二三四五六七八九十\d]+)款\s*(.*)$',
                r'^（([一二三四五六七八九十\d]+)）\s*(.*)$',
                r'^([1-9]\d*)\.\s*([1-9]\d*)\s*(.*)$'
            ],
            'item': [
                r'^([1-9]\d*)\)\s*(.*)$',
                r'^[①②③④⑤⑥⑦⑧⑨⑩]\s*(.*)$'
            ]
        }

    async def extract(self, text: str, config: Dict, template: Dict, paragraphs: List[Dict]) -> List[Dict]:
        """条款要素提取"""
        import re
        results = []

        for pattern_config in config['patterns']:
            pattern = pattern_config['pattern']
            matches = re.finditer(pattern, text, re.MULTILINE | re.DOTALL)

            for match in matches:
                # 分析条款层级
                clause_level = await self._analyze_clause_level(match.group(), pattern_config)

                # 提取条款内容
                clause_content = await self._extract_clause_content(match.group())

                # 查找段落位置
                paragraph_index = self._find_paragraph_index(text, match.start())

                result = {
                    'element_name': template['element_name'],
                    'element_category': template['element_category'],
                    'pattern_type': pattern_config.get('type', 'default'),
                    'text': match.group(),
                    'confidence': pattern_config.get('confidence', 0.8),
                    'value': clause_content,
                    'clause_info': {
                        'clause_number': match.group(1) if len(match.groups()) >= 1 else '',
                        'clause_level': clause_level,
                        'clause_type': pattern_config.get('clause_type', 'general'),
                        'full_text': match.group()
                    },
                    'source_position': {
                        'paragraph': paragraph_index,
                        'start': match.start(),
                        'end': match.end()
                    }
                }

                results.append(result)

        return results

    async def _analyze_clause_level(self, clause_text: str, pattern_config: Dict) -> str:
        """分析条款层级"""
        import re

        for level, patterns in self.clause_patterns.items():
            for pattern in patterns:
                if re.match(pattern, clause_text):
                    return level

        return pattern_config.get('default_level', 'general')

    async def _extract_clause_content(self, clause_text: str) -> str:
        """提取条款核心内容"""
        import re

        # 去除条款编号的模式
        content_patterns = [
            r'^第[一二三四五六七八九十\d]+条[：:\s]*(.+)$',
            r'^第[一二三四五六七八九十\d]+款[：:\s]*(.+)$',
            r'^\([一二三四五六七八九十\d]+\)[：:\s]*(.+)$',
            r'^\d+\)[：:\s]*(.+)$',
            r'^[①②③④⑤⑥⑦⑧⑨⑩][：:\s]*(.+)$'
        ]

        for pattern in content_patterns:
            match = re.match(pattern, clause_text, re.DOTALL)
            if match:
                return match.group(1).strip()

        return clause_text.strip()

    def _find_paragraph_index(self, full_text: str, match_position: int) -> int:
        """查找段落索引"""
        text_before_match = full_text[:match_position]
        return text_before_match.count('\n')

# 金额要素处理器
class AmountElementProcessor:
    async def extract(self, text: str, config: Dict, template: Dict, paragraphs: List[Dict]) -> List[Dict]:
        """金额要素提取"""
        import re
        results = []

        for pattern_config in config['patterns']:
            pattern = pattern_config['pattern']
            matches = re.finditer(pattern, text)

            for match in matches:
                try:
                    # 提取数字部分
                    amount_text = match.group(2) if len(match.groups()) >= 2 else match.group(1)
                    amount_value = float(amount_text.replace(',', '').replace('，', ''))

                    # 处理单位
                    currency = '人民币'
                    unit = '元'
                    if '万元' in match.group():
                        amount_value *= 10000
                        unit = '万元'
                    elif 'USD' in match.group() or '美元' in match.group():
                        currency = '美元'

                    result = {
                        'element_name': template['element_name'],
                        'element_category': template['element_category'],
                        'pattern_type': pattern_config.get('type', 'default'),
                        'text': match.group(),
                        'confidence': pattern_config.get('confidence', 0.8),
                        'value': amount_value,
                        'currency': currency,
                        'unit': unit,
                        'original_text': amount_text
                    }

                    results.append(result)

                except (ValueError, IndexError):
                    continue

        return results

# 日期要素处理器
class DateElementProcessor:
    async def extract(self, text: str, config: Dict, template: Dict, paragraphs: List[Dict]) -> List[Dict]:
        """日期要素提取"""
        import re
        results = []

        for pattern_config in config['patterns']:
            pattern = pattern_config['pattern']
            matches = re.finditer(pattern, text)

            for match in matches:
                date_text = match.group(2).strip() if len(match.groups()) >= 2 else match.group(1).strip()
                parsed_date = await self._parse_date_string(date_text)

                result = {
                    'element_name': template['element_name'],
                    'element_category': template['element_category'],
                    'pattern_type': pattern_config.get('type', 'default'),
                    'text': match.group(),
                    'confidence': pattern_config.get('confidence', 0.8),
                    'value': date_text,
                    'parsed_date': parsed_date,
                    'date_type': pattern_config.get('date_type', 'general')
                }

                results.append(result)

        return results

    async def _parse_date_string(self, date_text: str) -> str:
        """解析日期字符串"""
        import re

        # 中文日期格式
        match = re.search(r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日', date_text)
        if match:
            year, month, day = match.groups()
            return f"{year}-{month.zfill(2)}-{day.zfill(2)}"

        # 数字日期格式
        match = re.search(r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})', date_text)
        if match:
            year, month, day = match.groups()
            return f"{year}-{month.zfill(2)}-{day.zfill(2)}"

        return date_text

# 文本要素处理器
class TextElementProcessor:
    async def extract(self, text: str, config: Dict, template: Dict, paragraphs: List[Dict]) -> List[Dict]:
        """文本要素提取"""
        import re
        results = []

        for pattern_config in config['patterns']:
            pattern = pattern_config['pattern']
            matches = re.finditer(pattern, text, re.MULTILINE | re.DOTALL)

            for match in matches:
                extracted_value = match.group(2).strip() if len(match.groups()) >= 2 else match.group(1).strip()

                result = {
                    'element_name': template['element_name'],
                    'element_category': template['element_category'],
                    'pattern_type': pattern_config.get('type', 'default'),
                    'text': match.group(),
                    'confidence': pattern_config.get('confidence', 0.8),
                    'value': extracted_value
                }

                results.append(result)

        return results

    async def _parse_word_document(self, file_path: str) -> Dict:
        """Word文档解析实现"""
        from docx import Document

        doc = Document(file_path)
        content = {
            'paragraphs': [],
            'tables': [],
            'headers': [],
            'footers': [],
            'metadata': {}
        }

        # 提取段落内容
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                content['paragraphs'].append({
                    'text': paragraph.text,
                    'style': paragraph.style.name,
                    'alignment': paragraph.alignment
                })

        # 提取表格内容
        for table in doc.tables:
            table_data = []
            for row in table.rows:
                row_data = [cell.text for cell in row.cells]
                table_data.append(row_data)
            content['tables'].append(table_data)

        # 提取文档元数据
        content['metadata'] = {
            'title': doc.core_properties.title,
            'author': doc.core_properties.author,
            'created': doc.core_properties.created,
            'modified': doc.core_properties.modified
        }

        return content

    async def _preprocess_text(self, doc_content: Dict) -> Dict:
        """文本预处理实现"""
        import re
        import jieba

        processed_content = doc_content.copy()

        # 文本清理和规范化
        for paragraph in processed_content['paragraphs']:
            text = paragraph['text']

            # 统一编码
            text = text.encode('utf-8').decode('utf-8')

            # 清理特殊字符
            text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()（）【】""''、。，；：！？]', '', text)

            # 规范化空白字符
            text = re.sub(r'\s+', ' ', text).strip()

            # 分词处理（用于后续分析）
            words = list(jieba.cut(text))

            paragraph.update({
                'cleaned_text': text,
                'word_count': len(words),
                'keywords': [w for w in words if len(w) > 1]
            })

        return processed_content

    async def _analyze_structure(self, processed_content: Dict) -> Dict:
        """合同结构化分析 - 详细实现"""
        import re
        from datetime import datetime

        structure = {
            'title': {'text': '', 'confidence': 0.0, 'position': -1},
            'parties': [],
            'clauses': [],
            'key_info': {
                'amounts': [],
                'dates': [],
                'addresses': [],
                'contacts': []
            },
            'signatures': [],
            'attachments': []
        }

        # 1. 合同标题识别 - 多重策略
        title_result = await self._identify_title(processed_content['paragraphs'])
        structure['title'] = title_result

        # 2. 合同当事方识别 - 增强版
        parties_result = await self._identify_parties(processed_content['paragraphs'])
        structure['parties'] = parties_result

        # 3. 条款章节识别 - 层级分析
        clauses_result = await self._identify_clauses(processed_content['paragraphs'])
        structure['clauses'] = clauses_result

        # 4. 合同类型识别（基于内容分析）
        contract_type = await self._determine_contract_type(processed_content['paragraphs'])

        # 5. 统一要素提取（包括标题、条款、其他要素）
        all_elements = await self._extract_all_elements(processed_content['paragraphs'], contract_type)
        structure['contract_elements'] = all_elements
        structure['contract_type'] = contract_type

        # 6. 从提取的要素中获取标题（向后兼容）
        title_elements = all_elements.get('文档结构要素', {}).get('合同标题', [])
        if title_elements:
            structure['title'] = {
                'text': title_elements[0].get('value', ''),
                'confidence': title_elements[0].get('confidence', 0.0),
                'position': title_elements[0].get('source_position', -1)
            }
        else:
            structure['title'] = {'text': '', 'confidence': 0.0, 'position': -1}

        return {
            'raw_content': processed_content,
            'structure': structure,
            'statistics': {
                'total_paragraphs': len(processed_content['paragraphs']),
                'total_tables': len(processed_content['tables']),
                'total_words': sum(p.get('word_count', 0) for p in processed_content['paragraphs']),
                'identified_clauses': len(structure['clauses']),
                'identified_parties': len(structure['parties']),
                'extraction_confidence': self._calculate_overall_confidence(structure)
            }
        }

    async def _identify_title(self, paragraphs: List[Dict]) -> Dict:
        """合同标题识别 - 配置化实现"""
        import re

        title_candidates = []

        # 从配置文件加载标题识别规则
        title_config = await self._load_title_config()

        for i, paragraph in enumerate(paragraphs[:title_config['search_range']]):
            text = paragraph['cleaned_text']

            # 长度过滤
            if not (title_config['min_length'] <= len(text) <= title_config['max_length']):
                continue

            confidence = 0.0

            # 关键词匹配
            for keyword_rule in title_config['keyword_patterns']:
                if re.search(keyword_rule['pattern'], text):
                    confidence += keyword_rule['weight']
                    break

            # 位置权重
            position_weight = (title_config['search_range'] - i) * title_config['position_weight']
            confidence += position_weight

            # 样式分析
            if paragraph.get('style') and any(style in paragraph.get('style', '').lower()
                                            for style in title_config['title_styles']):
                confidence += title_config['style_weight']

            # 对齐方式加分
            if paragraph.get('alignment') in title_config['preferred_alignments']:
                confidence += title_config['alignment_weight']

            title_candidates.append({
                'text': text,
                'confidence': confidence,
                'position': i
            })

        # 选择置信度最高的作为标题
        if title_candidates:
            best_title = max(title_candidates, key=lambda x: x['confidence'])
            return best_title

        return {'text': '', 'confidence': 0.0, 'position': -1}

    async def _load_title_config(self) -> Dict:
        """加载标题识别配置"""
        # 从配置文件或数据库加载，这里展示配置结构
        return {
            'search_range': 5,  # 搜索前5段
            'min_length': 5,
            'max_length': 100,
            'position_weight': 0.1,
            'style_weight': 0.3,
            'alignment_weight': 0.1,
            'keyword_patterns': [
                {'pattern': r'.*合同.*', 'weight': 0.4},
                {'pattern': r'.*协议.*', 'weight': 0.4},
                {'pattern': r'.*契约.*', 'weight': 0.3},
                {'pattern': r'.*协定.*', 'weight': 0.3},
                {'pattern': r'.*合作.*书.*', 'weight': 0.35},
                {'pattern': r'.*委托.*书.*', 'weight': 0.35},
                {'pattern': r'.*服务.*协议.*', 'weight': 0.4}
            ],
            'title_styles': ['title', 'heading', '标题'],
            'preferred_alignments': [1]  # 1表示居中对齐
        }

    async def _identify_parties(self, paragraphs: List[Dict]) -> List[Dict]:
        """合同当事方识别 - 配置化版本"""
        import re

        parties = []

        # 从配置加载当事方识别规则
        party_config = await self._load_party_config()

        for paragraph in paragraphs:
            text = paragraph['cleaned_text']

            for party_rule in party_config['party_patterns']:
                matches = re.finditer(party_rule['pattern'], text, re.MULTILINE | re.DOTALL)
                for match in matches:
                    party_info = match.group(1).strip()

                    # 进一步解析当事方信息
                    party_details = await self._parse_party_details(party_info, party_config['detail_patterns'])

                    parties.append({
                        'role': party_rule['role'],
                        'name': party_details.get('name', party_info),
                        'details': party_details,
                        'confidence': party_rule['confidence'],
                        'source_position': paragraphs.index(paragraph)
                    })

        return parties

    async def _load_party_config(self) -> Dict:
        """加载当事方识别配置"""
        return {
            'party_patterns': [
                {'pattern': r'甲\s*方[：:]\s*(.+?)(?=\n|乙方|丙方|$)', 'role': '甲方', 'confidence': 0.9},
                {'pattern': r'乙\s*方[：:]\s*(.+?)(?=\n|甲方|丙方|$)', 'role': '乙方', 'confidence': 0.9},
                {'pattern': r'丙\s*方[：:]\s*(.+?)(?=\n|甲方|乙方|$)', 'role': '丙方', 'confidence': 0.8},
                {'pattern': r'委托方[：:]\s*(.+?)(?=\n|受托方|$)', 'role': '委托方', 'confidence': 0.85},
                {'pattern': r'受托方[：:]\s*(.+?)(?=\n|委托方|$)', 'role': '受托方', 'confidence': 0.85},
                {'pattern': r'出租方[：:]\s*(.+?)(?=\n|承租方|$)', 'role': '出租方', 'confidence': 0.85},
                {'pattern': r'承租方[：:]\s*(.+?)(?=\n|出租方|$)', 'role': '承租方', 'confidence': 0.85},
                {'pattern': r'买方[：:]\s*(.+?)(?=\n|卖方|$)', 'role': '买方', 'confidence': 0.85},
                {'pattern': r'卖方[：:]\s*(.+?)(?=\n|买方|$)', 'role': '卖方', 'confidence': 0.85}
            ],
            'detail_patterns': {
                'name': [
                    {'pattern': r'^([^，,；;（(]+)', 'priority': 1}
                ],
                'address': [
                    {'pattern': r'地址[：:]?\s*([^，,；;电话手机传真邮箱]+)', 'priority': 1},
                    {'pattern': r'住所[：:]?\s*([^，,；;电话手机传真邮箱]+)', 'priority': 2},
                    {'pattern': r'注册地址[：:]?\s*([^，,；;电话手机传真邮箱]+)', 'priority': 3}
                ],
                'contact': [
                    {'pattern': r'电话[：:]?\s*([\d\-\s]+)', 'priority': 1},
                    {'pattern': r'手机[：:]?\s*([\d\-\s]+)', 'priority': 2}
                ],
                'legal_representative': [
                    {'pattern': r'法定代表人[：:]?\s*([^，,；;]+)', 'priority': 1}
                ]
            }
        }

    async def _parse_party_details(self, party_text: str, detail_patterns: Dict) -> Dict:
        """解析当事方详细信息 - 配置化版本"""
        import re

        details = {'name': '', 'address': '', 'contact': '', 'legal_representative': ''}

        # 按配置的模式提取各类信息
        for detail_type, patterns in detail_patterns.items():
            # 按优先级排序
            sorted_patterns = sorted(patterns, key=lambda x: x['priority'])

            for pattern_config in sorted_patterns:
                match = re.search(pattern_config['pattern'], party_text)
                if match:
                    details[detail_type] = match.group(1).strip()
                    break  # 找到第一个匹配就停止

        return details

    async def _identify_clauses(self, paragraphs: List[Dict]) -> List[Dict]:
        """条款章节识别 - 配置化版本"""
        import re

        clauses = []

        # 从配置加载条款识别规则
        clause_config = await self._load_clause_config()

        for i, paragraph in enumerate(paragraphs):
            text = paragraph['cleaned_text']

            for pattern_config in clause_config['clause_patterns']:
                match = re.match(pattern_config['pattern'], text)
                if match:
                    # 提取条款编号和内容
                    if len(match.groups()) >= 2:
                        clause_number = match.group(1)
                        clause_content = match.group(2) if match.group(2) else text
                    else:
                        clause_number = ''
                        clause_content = match.group(1) if match.group(1) else text

                    # 条款语义分类
                    semantic_type = await self._classify_clause_semantics(clause_content, clause_config['semantic_rules'])

                    clauses.append({
                        'index': i,
                        'number': clause_number,
                        'type': pattern_config['type'],
                        'semantic_type': semantic_type,
                        'title': clause_content[:50] + '...' if len(clause_content) > 50 else clause_content,
                        'content': text,
                        'confidence': pattern_config['confidence'],
                        'word_count': len(clause_content.split())
                    })
                    break

        # 条款层级关系分析
        clauses = self._analyze_clause_hierarchy(clauses)

        return clauses

    async def _load_clause_config(self) -> Dict:
        """加载条款识别配置"""
        return {
            'clause_patterns': [
                # 主条款模式
                {'pattern': r'^第([一二三四五六七八九十百千万\d]+)条\s*(.*)$', 'type': 'main_clause', 'confidence': 1.0},
                {'pattern': r'^([一二三四五六七八九十百千万]+)、\s*(.*)$', 'type': 'main_clause', 'confidence': 0.9},
                {'pattern': r'^(\d+)\.\s*(.*)$', 'type': 'main_clause', 'confidence': 0.8},

                # 子条款模式
                {'pattern': r'^第([一二三四五六七八九十百千万\d]+)款\s*(.*)$', 'type': 'sub_clause', 'confidence': 0.7},
                {'pattern': r'^（([一二三四五六七八九十\d]+)）\s*(.*)$', 'type': 'sub_clause', 'confidence': 0.6},
                {'pattern': r'^([1-9]\d*)\.\s*([1-9]\d*)\s*(.*)$', 'type': 'sub_clause', 'confidence': 0.5},

                # 项目模式
                {'pattern': r'^([1-9]\d*)\)\s*(.*)$', 'type': 'item', 'confidence': 0.4},
                {'pattern': r'^[①②③④⑤⑥⑦⑧⑨⑩]\s*(.*)$', 'type': 'item', 'confidence': 0.3}
            ],
            'semantic_rules': [
                {'keywords': ['权利', '权力', '享有', '有权'], 'type': 'rights'},
                {'keywords': ['义务', '责任', '应当', '必须', '负责'], 'type': 'obligations'},
                {'keywords': ['违约', '违反', '赔偿', '损失', '责任'], 'type': 'breach'},
                {'keywords': ['价格', '费用', '金额', '支付', '付款'], 'type': 'payment'},
                {'keywords': ['期限', '时间', '日期', '届满', '到期'], 'type': 'timeline'},
                {'keywords': ['终止', '解除', '中止', '取消'], 'type': 'termination'},
                {'keywords': ['争议', '纠纷', '仲裁', '诉讼', '管辖'], 'type': 'dispute'},
                {'keywords': ['保密', '机密', '泄露', '披露'], 'type': 'confidentiality'},
                {'keywords': ['知识产权', '专利', '商标', '著作权'], 'type': 'intellectual_property'},
                {'keywords': ['不可抗力', '天灾', '战争', '政策'], 'type': 'force_majeure'}
            ]
        }

    async def _classify_clause_semantics(self, content: str, semantic_rules: List[Dict]) -> str:
        """条款语义分类 - 配置化版本"""

        # 按配置的语义规则进行分类
        for rule in semantic_rules:
            if any(keyword in content for keyword in rule['keywords']):
                return rule['type']

        return 'general'

    def _analyze_clause_hierarchy(self, clauses: List[Dict]) -> List[Dict]:
        """分析条款层级关系"""
        for i, clause in enumerate(clauses):
            clause['parent_index'] = -1
            clause['children_indices'] = []

            # 寻找父条款
            if clause['type'] in ['sub_clause', 'item']:
                for j in range(i-1, -1, -1):
                    if clauses[j]['type'] == 'main_clause':
                        clause['parent_index'] = j
                        clauses[j]['children_indices'].append(i)
                        break

        return clauses

    async def _determine_contract_type(self, paragraphs: List[Dict]) -> str:
        """确定合同类型 - 基于内容分析"""
        import re

        # 从配置加载合同类型识别规则
        type_config = await self._load_contract_type_config()

        # 分析前20段内容（包含标题和主要条款）
        full_text = ' '.join([p['cleaned_text'] for p in paragraphs[:20]])

        # 按优先级和权重匹配合同类型
        type_scores = {}

        for contract_type in type_config['contract_types']:
            score = 0
            matched_keywords = []

            # 关键词匹配评分
            for keyword_config in contract_type['keywords']:
                keyword = keyword_config['keyword']
                weight = keyword_config.get('weight', 1.0)

                if keyword in full_text:
                    score += weight
                    matched_keywords.append(keyword)

            # 模式匹配评分
            for pattern_config in contract_type.get('patterns', []):
                pattern = pattern_config['pattern']
                weight = pattern_config.get('weight', 1.0)

                if re.search(pattern, full_text):
                    score += weight

            if score > 0:
                type_scores[contract_type['type']] = {
                    'score': score,
                    'matched_keywords': matched_keywords,
                    'confidence': min(score / contract_type.get('max_score', 10), 1.0)
                }

        # 返回得分最高的合同类型
        if type_scores:
            best_type = max(type_scores.keys(), key=lambda k: type_scores[k]['score'])
            return best_type

        return 'general'  # 默认通用合同

    async def _extract_all_elements(self, paragraphs: List[Dict], contract_type: str) -> Dict:
        """合同要素提取 - 完全动态配置化提取"""
        import re

        extracted_elements = {}
        full_text = ' '.join([p['cleaned_text'] for p in paragraphs])

        # 从数据库加载该合同类型的所有要素配置
        element_templates = await self._load_element_templates(contract_type)

        # 按要素配置进行提取
        for template in element_templates:
            element_name = template['element_name']
            element_config = template['element_config']
            element_category = template['element_category']

            # 动态创建分类结构（支持多级分类）
            category_path = element_category.split('/')  # 支持 "基础信息/当事方信息" 这样的层级
            current_level = extracted_elements

            # 创建多级分类结构
            for category in category_path:
                if category not in current_level:
                    current_level[category] = {}
                current_level = current_level[category]

            # 提取该要素
            element_results = await self._extract_single_element(
                full_text, element_config, template
            )

            if element_results:
                current_level[element_name] = element_results

        # 处理要素间的关系
        extracted_elements = await self._process_element_relationships(
            extracted_elements, element_templates
        )

        return extracted_elements

    async def _load_element_templates(self, contract_type: str) -> List[Dict]:
        """从数据库加载要素模板配置"""
        # 实际实现中从数据库查询
        query = """
        SELECT
            et.element_name,
            et.element_category,
            et.element_display_name,
            et.element_description,
            et.data_type,
            et.element_config,
            et.validation_rules,
            et.is_required,
            et.weight,
            et.sort_order,
            ec.category_name,
            ec.parent_category
        FROM contract_element_templates et
        LEFT JOIN element_categories ec ON et.element_category = ec.category_code
        WHERE et.contract_type IN (?, 'general')
        AND et.is_active = TRUE
        ORDER BY et.sort_order, et.element_name
        """

        # 这里模拟数据库查询结果
        templates = await self._execute_query(query, [contract_type])

        return templates

    async def _execute_query(self, query: str, params: List) -> List[Dict]:
        """执行数据库查询（模拟实现）"""
        # 实际实现中会连接数据库执行查询
        # 这里返回示例配置，实际使用时会从数据库获取真实数据
        return [
            # 文档结构要素
            {
                'element_name': '合同标题',
                'element_category': '文档结构要素',
                'element_config': {
                    'patterns': [
                        {'pattern': r'^(.{5,100}?)(合同|协议|契约|协定)', 'type': 'title_with_keyword', 'confidence': 0.9},
                        {'pattern': r'^(.{5,100})$', 'type': 'first_line_title', 'confidence': 0.6, 'position_limit': 3}
                    ],
                    'data_type': 'text',
                    'validation_rules': {
                        'min_length': 5,
                        'max_length': 100,
                        'required_keywords': ['合同', '协议', '契约', '协定']
                    }
                },
                'is_required': True,
                'weight': 1.0
            },
            # 通用条款要素
            {
                'element_name': '当事方条款',
                'element_category': '通用条款要素',
                'element_config': {
                    'patterns': [
                        {'pattern': r'第([一二三四五六七八九十\d]+)条.*?(甲方|乙方|委托方|受托方)', 'type': 'party_clause', 'confidence': 0.9},
                        {'pattern': r'([一二三四五六七八九十\d]+)、.*?(当事方|合同双方)', 'type': 'party_section', 'confidence': 0.8}
                    ],
                    'data_type': 'clause',
                    'clause_type': 'party_info'
                },
                'is_required': True,
                'weight': 1.0
            },
            {
                'element_name': '争议解决条款',
                'element_category': '通用条款要素',
                'element_config': {
                    'patterns': [
                        {'pattern': r'第([一二三四五六七八九十\d]+)条.*?(争议|纠纷|仲裁|诉讼)', 'type': 'dispute_clause', 'confidence': 0.9},
                        {'pattern': r'([一二三四五六七八九十\d]+)、.*?(管辖|法院|仲裁委)', 'type': 'jurisdiction_section', 'confidence': 0.8}
                    ],
                    'data_type': 'clause',
                    'clause_type': 'dispute_resolution'
                },
                'is_required': False,
                'weight': 0.8
            },
            # 销售合同专用条款
            {
                'element_name': '商品描述条款',
                'element_category': '销售合同专用条款',
                'element_config': {
                    'patterns': [
                        {'pattern': r'第([一二三四五六七八九十\d]+)条.*?(商品|货物|产品).*?(名称|规格|型号)', 'type': 'product_clause', 'confidence': 0.9},
                        {'pattern': r'([一二三四五六七八九十\d]+)、.*?(标的|商品描述)', 'type': 'product_section', 'confidence': 0.8}
                    ],
                    'data_type': 'clause',
                    'clause_type': 'product_description',
                    'contract_types': ['sales']
                },
                'is_required': True,
                'weight': 1.0
            },
            {
                'element_name': '价格条款',
                'element_category': '销售合同专用条款',
                'element_config': {
                    'patterns': [
                        {'pattern': r'第([一二三四五六七八九十\d]+)条.*?(价格|价款|金额)', 'type': 'price_clause', 'confidence': 0.9},
                        {'pattern': r'([一二三四五六七八九十\d]+)、.*?(合同总价|单价)', 'type': 'price_section', 'confidence': 0.8}
                    ],
                    'data_type': 'clause',
                    'clause_type': 'pricing',
                    'contract_types': ['sales']
                },
                'is_required': True,
                'weight': 1.0
            },
            # 服务合同专用条款
            {
                'element_name': '服务内容条款',
                'element_category': '服务合同专用条款',
                'element_config': {
                    'patterns': [
                        {'pattern': r'第([一二三四五六七八九十\d]+)条.*?服务.*?(内容|范围|标准)', 'type': 'service_clause', 'confidence': 0.9},
                        {'pattern': r'([一二三四五六七八九十\d]+)、.*?(工作内容|服务项目)', 'type': 'service_section', 'confidence': 0.8}
                    ],
                    'data_type': 'clause',
                    'clause_type': 'service_content',
                    'contract_types': ['service']
                },
                'is_required': True,
                'weight': 1.0
            },
            # 租赁合同专用条款
            {
                'element_name': '租金条款',
                'element_category': '租赁合同专用条款',
                'element_config': {
                    'patterns': [
                        {'pattern': r'第([一二三四五六七八九十\d]+)条.*?(租金|租费)', 'type': 'rent_clause', 'confidence': 0.9},
                        {'pattern': r'([一二三四五六七八九十\d]+)、.*?(租赁费用|租金标准)', 'type': 'rent_section', 'confidence': 0.8}
                    ],
                    'data_type': 'clause',
                    'clause_type': 'rental_fee',
                    'contract_types': ['lease']
                },
                'is_required': True,
                'weight': 1.0
            }
        ]

    async def _process_element_relationships(self, extracted_elements: Dict, templates: List[Dict]) -> Dict:
        """处理要素间的关系"""

        # 加载要素关系配置
        relationships = await self._load_element_relationships()

        for relationship in relationships:
            relationship_type = relationship['relationship_type']

            if relationship_type == 'dependency':
                await self._process_dependency_relationship(extracted_elements, relationship)
            elif relationship_type == 'mutual_exclusion':
                await self._process_mutual_exclusion_relationship(extracted_elements, relationship)
            elif relationship_type == 'composition':
                await self._process_composition_relationship(extracted_elements, relationship)
            elif relationship_type == 'calculation':
                await self._process_calculation_relationship(extracted_elements, relationship)

        return extracted_elements

    async def _load_element_relationships(self) -> List[Dict]:
        """加载要素关系配置"""
        query = """
        SELECT
            relationship_type,
            source_elements,
            target_elements,
            relationship_config
        FROM element_relationships
        WHERE is_active = TRUE
        """

        return await self._execute_query(query, [])

    async def _process_dependency_relationship(self, elements: Dict, relationship: Dict):
        """处理依赖关系"""
        # 如果依赖的要素不存在，则移除当前要素
        source_elements = relationship['source_elements']
        target_elements = relationship['target_elements']

        for target_path in target_elements:
            if not self._element_exists(elements, target_path):
                # 移除依赖的要素
                for source_path in source_elements:
                    self._remove_element(elements, source_path)

    async def _process_mutual_exclusion_relationship(self, elements: Dict, relationship: Dict):
        """处理互斥关系"""
        # 如果多个互斥要素同时存在，保留置信度最高的
        mutual_elements = relationship['source_elements']
        existing_elements = []

        for element_path in mutual_elements:
            if self._element_exists(elements, element_path):
                element_data = self._get_element(elements, element_path)
                existing_elements.append({
                    'path': element_path,
                    'confidence': element_data.get('confidence', 0.0),
                    'data': element_data
                })

        if len(existing_elements) > 1:
            # 保留置信度最高的，移除其他的
            best_element = max(existing_elements, key=lambda x: x['confidence'])
            for element in existing_elements:
                if element['path'] != best_element['path']:
                    self._remove_element(elements, element['path'])

    async def _process_composition_relationship(self, elements: Dict, relationship: Dict):
        """处理组合关系"""
        # 将多个要素组合成一个复合要素
        source_elements = relationship['source_elements']
        target_element = relationship['target_elements'][0]
        composition_config = relationship['relationship_config']

        composite_value = {}
        all_sources_exist = True

        for source_path in source_elements:
            if self._element_exists(elements, source_path):
                composite_value[source_path] = self._get_element(elements, source_path)
            else:
                all_sources_exist = False
                break

        if all_sources_exist:
            # 创建复合要素
            self._set_element(elements, target_element, {
                'type': 'composite',
                'components': composite_value,
                'composition_rule': composition_config.get('rule', 'default')
            })

    async def _process_calculation_relationship(self, elements: Dict, relationship: Dict):
        """处理计算关系"""
        # 根据其他要素计算新要素的值
        source_elements = relationship['source_elements']
        target_element = relationship['target_elements'][0]
        calculation_config = relationship['relationship_config']

        source_values = {}
        for source_path in source_elements:
            if self._element_exists(elements, source_path):
                source_values[source_path] = self._get_element(elements, source_path)

        if len(source_values) == len(source_elements):
            # 执行计算
            calculated_value = await self._execute_calculation(
                source_values, calculation_config
            )

            if calculated_value is not None:
                self._set_element(elements, target_element, calculated_value)

    def _element_exists(self, elements: Dict, element_path: str) -> bool:
        """检查要素是否存在"""
        path_parts = element_path.split('/')
        current = elements

        for part in path_parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return False

        return True

    def _get_element(self, elements: Dict, element_path: str):
        """获取要素值"""
        path_parts = element_path.split('/')
        current = elements

        for part in path_parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return None

        return current

    def _set_element(self, elements: Dict, element_path: str, value):
        """设置要素值"""
        path_parts = element_path.split('/')
        current = elements

        # 创建路径
        for part in path_parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]

        # 设置值
        current[path_parts[-1]] = value

    def _remove_element(self, elements: Dict, element_path: str):
        """移除要素"""
        path_parts = element_path.split('/')
        current = elements

        # 找到父级
        for part in path_parts[:-1]:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return  # 路径不存在

        # 移除要素
        if isinstance(current, dict) and path_parts[-1] in current:
            del current[path_parts[-1]]

    async def _execute_calculation(self, source_values: Dict, calculation_config: Dict):
        """执行计算"""
        calculation_type = calculation_config.get('type', 'sum')

        if calculation_type == 'sum':
            # 求和计算
            total = 0
            for value in source_values.values():
                if isinstance(value, (int, float)):
                    total += value
                elif isinstance(value, dict) and 'value' in value:
                    total += float(value['value'])
            return {'value': total, 'type': 'calculated', 'calculation_type': 'sum'}

        elif calculation_type == 'concat':
            # 字符串拼接
            result = []
            for value in source_values.values():
                if isinstance(value, str):
                    result.append(value)
                elif isinstance(value, dict) and 'value' in value:
                    result.append(str(value['value']))
            return {'value': ' '.join(result), 'type': 'calculated', 'calculation_type': 'concat'}

        # 可以扩展更多计算类型
        return None

    async def _extract_single_element(self, text: str, element_config: Dict, template: Dict) -> List[Dict]:
        """提取单个要素"""
        import re

        element_results = []
        data_type = element_config.get('data_type', 'text')

        for pattern_config in element_config['patterns']:
            matches = re.finditer(pattern_config['pattern'], text, re.MULTILINE | re.DOTALL)

            for match in matches:
                result = {
                    'element_name': template['element_name'],
                    'element_category': template['element_category'],
                    'pattern_type': pattern_config.get('type', 'default'),
                    'text': match.group(),
                    'confidence': pattern_config.get('confidence', 0.5),
                    'weight': template.get('weight', 1.0),
                    'is_required': template.get('is_required', False)
                }

                # 根据数据类型处理提取的值
                if data_type == 'amount':
                    result.update(await self._process_amount_value(match, pattern_config))
                elif data_type == 'party_info':
                    result.update(await self._process_party_info(match, pattern_config))
                elif data_type == 'date':
                    result.update(await self._process_date_value(match, pattern_config))
                elif data_type == 'clause':
                    # 需要传入段落索引来定位条款位置
                    paragraph_index = self._find_paragraph_index(text, match.start())
                    result.update(await self._process_clause_value(match, pattern_config, paragraph_index))
                else:  # text
                    result['value'] = match.group(2).strip() if len(match.groups()) >= 2 else match.group(1).strip()

                element_results.append(result)

        return element_results

    async def _process_amount_value(self, match, pattern_config: Dict) -> Dict:
        """处理金额类型的值"""
        try:
            amount_text = match.group(2) if len(match.groups()) >= 2 else match.group(1)
            amount_value = float(amount_text.replace(',', '').replace('，', ''))

            result = {
                'value': amount_value,
                'currency': '人民币',
                'unit': '元'
            }

            # 处理单位
            if '万元' in match.group():
                result['value'] *= 10000
                result['unit'] = '万元'

            return result
        except (ValueError, IndexError):
            return {'value': match.group(), 'currency': '未知', 'unit': '未知'}

    async def _process_party_info(self, match, pattern_config: Dict) -> Dict:
        """处理当事方信息"""
        party_text = match.group(1).strip() if len(match.groups()) >= 1 else match.group().strip()

        return {
            'role': pattern_config.get('role', '未知'),
            'value': party_text,
            'details': await self._parse_party_details_simple(party_text)
        }

    async def _process_date_value(self, match, pattern_config: Dict) -> Dict:
        """处理日期类型的值"""
        date_text = match.group(2).strip() if len(match.groups()) >= 2 else match.group(1).strip()

        return {
            'value': date_text,
            'date_type': pattern_config.get('type', 'general'),
            'parsed_date': await self._parse_date_string(date_text)
        }

    async def _process_clause_value(self, match, pattern_config: Dict, paragraph_index: int) -> Dict:
        """处理条款类型的值"""
        clause_text = match.group().strip()

        # 提取条款编号
        clause_number = ''
        if len(match.groups()) >= 1:
            clause_number = match.group(1)

        # 分析条款层级
        clause_level = await self._analyze_clause_level(clause_text, pattern_config)

        # 提取条款内容
        clause_content = await self._extract_clause_content(clause_text)

        return {
            'value': clause_content,
            'clause_info': {
                'clause_number': clause_number,
                'clause_level': clause_level,
                'clause_type': pattern_config.get('clause_type', 'general'),
                'full_text': clause_text
            },
            'source_position': {
                'paragraph': paragraph_index,
                'start': match.start(),
                'end': match.end()
            }
        }

    async def _analyze_clause_level(self, clause_text: str, pattern_config: Dict) -> str:
        """分析条款层级"""
        import re

        # 主条款模式
        if re.match(r'^第[一二三四五六七八九十\d]+条', clause_text):
            return 'main_clause'

        # 子条款模式
        if re.match(r'^第[一二三四五六七八九十\d]+款', clause_text) or re.match(r'^\([一二三四五六七八九十\d]+\)', clause_text):
            return 'sub_clause'

        # 项目模式
        if re.match(r'^\d+\)', clause_text) or re.match(r'^[①②③④⑤⑥⑦⑧⑨⑩]', clause_text):
            return 'item'

        return pattern_config.get('default_level', 'general')

    async def _extract_clause_content(self, clause_text: str) -> str:
        """提取条款核心内容（去除编号）"""
        import re

        # 去除条款编号，保留核心内容
        patterns = [
            r'^第[一二三四五六七八九十\d]+条[：:\s]*(.+)$',
            r'^第[一二三四五六七八九十\d]+款[：:\s]*(.+)$',
            r'^\([一二三四五六七八九十\d]+\)[：:\s]*(.+)$',
            r'^\d+\)[：:\s]*(.+)$',
            r'^[①②③④⑤⑥⑦⑧⑨⑩][：:\s]*(.+)$'
        ]

        for pattern in patterns:
            match = re.match(pattern, clause_text, re.DOTALL)
            if match:
                return match.group(1).strip()

        return clause_text.strip()

    def _find_paragraph_index(self, full_text: str, match_position: int) -> int:
        """根据匹配位置查找段落索引"""
        # 简化实现：通过换行符计算段落索引
        text_before_match = full_text[:match_position]
        paragraph_index = text_before_match.count('\n')
        return paragraph_index

    async def _parse_party_details_simple(self, party_text: str) -> Dict:
        """简化的当事方详细信息解析"""
        import re

        details = {}

        # 提取公司名称
        name_match = re.search(r'^([^，,；;（(]+)', party_text)
        if name_match:
            details['name'] = name_match.group(1).strip()

        # 提取地址
        address_match = re.search(r'(地址|住所)[：:]?\s*([^，,；;电话]+)', party_text)
        if address_match:
            details['address'] = address_match.group(2).strip()

        return details

    async def _parse_date_string(self, date_text: str) -> str:
        """解析日期字符串"""
        import re

        # 尝试解析标准日期格式
        date_patterns = [
            r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日',
            r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})'
        ]

        for pattern in date_patterns:
            match = re.search(pattern, date_text)
            if match:
                year, month, day = match.groups()
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"

        return date_text  # 无法解析时返回原文

# 要素配置管理服务
class ElementConfigService:
    """要素配置管理服务 - 完全动态配置化"""

    def __init__(self):
        self.db = None  # 数据库连接
        self.cache = {}  # 配置缓存

    async def create_element_template(self, template_data: Dict) -> Dict:
        """创建要素模板"""
        # 验证模板数据
        await self._validate_template_data(template_data)

        # 保存到数据库
        template_id = await self._save_template_to_db(template_data)

        # 清除相关缓存
        await self._clear_cache(template_data['contract_type'])

        return {'id': template_id, 'status': 'created'}

    async def update_element_template(self, template_id: int, template_data: Dict) -> Dict:
        """更新要素模板"""
        # 验证模板数据
        await self._validate_template_data(template_data)

        # 更新数据库
        await self._update_template_in_db(template_id, template_data)

        # 清除相关缓存
        await self._clear_cache(template_data['contract_type'])

        return {'id': template_id, 'status': 'updated'}

    async def get_contract_elements(self, contract_type: str) -> List[Dict]:
        """获取指定合同类型的所有要素配置"""
        cache_key = f"elements_{contract_type}"

        if cache_key in self.cache:
            return self.cache[cache_key]

        # 从数据库查询
        elements = await self._query_elements_from_db(contract_type)

        # 缓存结果
        self.cache[cache_key] = elements

        return elements

    async def create_element_category(self, category_data: Dict) -> Dict:
        """创建要素分类"""
        # 验证分类数据
        await self._validate_category_data(category_data)

        # 生成分类路径
        category_path = await self._generate_category_path(category_data)
        category_data['category_path'] = category_path

        # 保存到数据库
        category_id = await self._save_category_to_db(category_data)

        # 清除分类缓存
        await self._clear_category_cache()

        return {'id': category_id, 'status': 'created', 'category_path': category_path}

    async def create_element_relationship(self, relationship_data: Dict) -> Dict:
        """创建要素关系"""
        # 验证关系数据
        await self._validate_relationship_data(relationship_data)

        # 保存到数据库
        relationship_id = await self._save_relationship_to_db(relationship_data)

        # 清除关系缓存
        await self._clear_relationship_cache()

        return {'id': relationship_id, 'status': 'created'}

    async def get_category_tree(self) -> Dict:
        """获取分类树结构"""
        cache_key = "category_tree"

        if cache_key in self.cache:
            return self.cache[cache_key]

        # 从数据库查询所有分类
        categories = await self._query_categories_from_db()

        # 构建树结构
        category_tree = self._build_category_tree(categories)

        # 缓存结果
        self.cache[cache_key] = category_tree

        return category_tree

    async def get_element_relationships(self, contract_type: str = None) -> List[Dict]:
        """获取要素关系配置"""
        cache_key = f"relationships_{contract_type or 'all'}"

        if cache_key in self.cache:
            return self.cache[cache_key]

        # 从数据库查询关系
        relationships = await self._query_relationships_from_db(contract_type)

        # 缓存结果
        self.cache[cache_key] = relationships

        return relationships

    async def validate_element_configuration(self, contract_type: str) -> Dict:
        """验证要素配置的完整性和一致性"""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }

        # 获取要素配置
        elements = await self.get_contract_elements(contract_type)
        relationships = await self.get_element_relationships(contract_type)

        # 验证必需要素
        required_elements = [e for e in elements if e.get('is_required', False)]
        if not required_elements:
            validation_result['warnings'].append("该合同类型没有定义必需要素")

        # 验证关系一致性
        for relationship in relationships:
            source_elements = relationship['source_elements']
            target_elements = relationship['target_elements']

            # 检查关系中引用的要素是否存在
            for element_path in source_elements + target_elements:
                if not self._element_path_exists(elements, element_path):
                    validation_result['errors'].append(
                        f"关系 '{relationship['relationship_name']}' 引用了不存在的要素: {element_path}"
                    )
                    validation_result['is_valid'] = False

        # 验证循环依赖
        circular_dependencies = self._detect_circular_dependencies(relationships)
        if circular_dependencies:
            validation_result['errors'].extend([
                f"检测到循环依赖: {' -> '.join(cycle)}" for cycle in circular_dependencies
            ])
            validation_result['is_valid'] = False

        return validation_result

    def _build_category_tree(self, categories: List[Dict]) -> Dict:
        """构建分类树结构"""
        tree = {}
        category_map = {cat['category_code']: cat for cat in categories}

        # 按层级深度排序
        sorted_categories = sorted(categories, key=lambda x: x['level_depth'])

        for category in sorted_categories:
            category_code = category['category_code']
            parent_code = category.get('parent_category_code')

            if not parent_code:
                # 根分类
                tree[category_code] = {
                    'info': category,
                    'children': {}
                }
            else:
                # 子分类
                parent_node = self._find_category_node(tree, parent_code)
                if parent_node:
                    parent_node['children'][category_code] = {
                        'info': category,
                        'children': {}
                    }

        return tree

    def _find_category_node(self, tree: Dict, category_code: str) -> Dict:
        """在分类树中查找节点"""
        for code, node in tree.items():
            if code == category_code:
                return node

            # 递归查找子节点
            found = self._find_category_node(node['children'], category_code)
            if found:
                return found

        return None

    def _element_path_exists(self, elements: List[Dict], element_path: str) -> bool:
        """检查要素路径是否存在"""
        path_parts = element_path.split('/')

        for element in elements:
            element_category = element.get('element_category', '')
            element_name = element.get('element_name', '')

            # 构建完整路径
            full_path = f"{element_category}/{element_name}"

            if full_path == element_path or element_name == element_path:
                return True

        return False

    def _detect_circular_dependencies(self, relationships: List[Dict]) -> List[List[str]]:
        """检测循环依赖"""
        # 构建依赖图
        dependency_graph = {}

        for relationship in relationships:
            if relationship['relationship_type'] == 'dependency':
                source_elements = relationship['source_elements']
                target_elements = relationship['target_elements']

                for source in source_elements:
                    if source not in dependency_graph:
                        dependency_graph[source] = []
                    dependency_graph[source].extend(target_elements)

        # 使用DFS检测循环
        visited = set()
        rec_stack = set()
        cycles = []

        def dfs(node, path):
            if node in rec_stack:
                # 找到循环
                cycle_start = path.index(node)
                cycles.append(path[cycle_start:] + [node])
                return

            if node in visited:
                return

            visited.add(node)
            rec_stack.add(node)

            for neighbor in dependency_graph.get(node, []):
                dfs(neighbor, path + [node])

            rec_stack.remove(node)

        for node in dependency_graph:
            if node not in visited:
                dfs(node, [])

        return cycles

    async def _generate_category_path(self, category_data: Dict) -> str:
        """生成分类路径"""
        parent_code = category_data.get('parent_category_code')
        category_name = category_data['category_name']

        if not parent_code:
            return category_name

        # 查询父分类路径
        parent_category = await self._query_category_by_code(parent_code)
        if parent_category:
            return f"{parent_category['category_path']}/{category_name}"

        return category_name

    async def _validate_category_data(self, category_data: Dict):
        """验证分类数据"""
        required_fields = ['category_code', 'category_name']

        for field in required_fields:
            if field not in category_data:
                raise ValueError(f"缺少必需字段: {field}")

        # 验证分类代码唯一性
        existing_category = await self._query_category_by_code(category_data['category_code'])
        if existing_category:
            raise ValueError(f"分类代码已存在: {category_data['category_code']}")

    async def _validate_relationship_data(self, relationship_data: Dict):
        """验证关系数据"""
        required_fields = ['relationship_name', 'relationship_type', 'source_elements', 'target_elements']

        for field in required_fields:
            if field not in relationship_data:
                raise ValueError(f"缺少必需字段: {field}")

        # 验证关系类型
        valid_types = ['dependency', 'mutual_exclusion', 'composition', 'calculation']
        if relationship_data['relationship_type'] not in valid_types:
            raise ValueError(f"无效的关系类型: {relationship_data['relationship_type']}")

    async def _clear_category_cache(self):
        """清除分类缓存"""
        keys_to_remove = [key for key in self.cache.keys() if 'category' in key]
        for key in keys_to_remove:
            self.cache.pop(key, None)

    async def _clear_relationship_cache(self):
        """清除关系缓存"""
        keys_to_remove = [key for key in self.cache.keys() if 'relationship' in key]
        for key in keys_to_remove:
            self.cache.pop(key, None)

    async def test_element_extraction(self, test_data: Dict) -> Dict:
        """测试要素提取规则"""
        contract_text = test_data['contract_text']
        element_config = test_data['element_config']

        # 创建临时提取器
        extractor = DocumentProcessor()

        # 模拟提取过程
        results = await extractor._extract_single_element(
            contract_text,
            element_config,
            {'element_name': test_data.get('element_name', 'test')}
        )

        return {
            'extraction_results': results,
            'success_count': len(results),
            'test_status': 'completed'
        }

    async def _validate_template_data(self, template_data: Dict):
        """验证模板数据"""
        required_fields = ['contract_type', 'element_category', 'element_name', 'element_config']

        for field in required_fields:
            if field not in template_data:
                raise ValueError(f"缺少必需字段: {field}")

        # 验证element_config结构
        element_config = template_data['element_config']
        if 'patterns' not in element_config:
            raise ValueError("element_config必须包含patterns字段")

        # 验证正则表达式
        import re
        for pattern in element_config['patterns']:
            try:
                re.compile(pattern['pattern'])
            except re.error as e:
                raise ValueError(f"无效的正则表达式: {pattern['pattern']}, 错误: {e}")

    async def _save_template_to_db(self, template_data: Dict) -> int:
        """保存模板到数据库"""
        # 实际实现中执行SQL INSERT
        # INSERT INTO contract_element_templates (contract_type, element_category, ...) VALUES (...)
        pass

    async def _update_template_in_db(self, template_id: int, template_data: Dict):
        """更新数据库中的模板"""
        # 实际实现中执行SQL UPDATE
        # UPDATE contract_element_templates SET ... WHERE id = template_id
        pass

    async def _query_elements_from_db(self, contract_type: str) -> List[Dict]:
        """从数据库查询要素配置"""
        # 实际实现中执行SQL查询
        # SELECT * FROM contract_element_templates WHERE contract_type = ? AND is_active = TRUE
        pass

    async def _clear_cache(self, contract_type: str):
        """清除相关缓存"""
        cache_key = f"elements_{contract_type}"
        self.cache.pop(cache_key, None)

# 要素配置管理界面设计
class ElementConfigUI:
    """要素配置管理界面组件 - 完整的可视化配置系统"""

    def __init__(self):
        self.components = {
            'main_dashboard': self._create_main_dashboard(),
            'category_manager': self._create_category_manager(),
            'element_template_form': self._create_template_form(),
            'pattern_editor': self._create_pattern_editor(),
            'relationship_manager': self._create_relationship_manager(),
            'test_panel': self._create_test_panel(),
            'import_export': self._create_import_export_panel()
        }

    def _create_main_dashboard(self) -> Dict:
        """创建主控制面板"""
        return {
            'layout': 'grid',
            'sections': [
                {
                    'title': '合同类型管理',
                    'component': 'contract_type_grid',
                    'actions': ['add', 'edit', 'delete', 'clone'],
                    'columns': ['type_code', 'type_name', 'element_count', 'status', 'actions']
                },
                {
                    'title': '要素分类管理',
                    'component': 'category_tree',
                    'actions': ['add_category', 'edit_category', 'delete_category', 'move_category'],
                    'features': ['drag_drop', 'context_menu', 'search']
                },
                {
                    'title': '要素模板管理',
                    'component': 'element_template_grid',
                    'actions': ['add', 'edit', 'delete', 'test', 'clone'],
                    'filters': ['contract_type', 'category', 'data_type', 'is_required']
                },
                {
                    'title': '配置统计',
                    'component': 'statistics_panel',
                    'metrics': ['total_elements', 'total_patterns', 'test_success_rate', 'usage_frequency']
                }
            ]
        }

    def _create_category_manager(self) -> Dict:
        """创建分类管理器"""
        return {
            'component_type': 'tree_manager',
            'features': {
                'tree_view': {
                    'drag_drop': True,
                    'context_menu': True,
                    'search': True,
                    'filter': True,
                    'expand_collapse': True
                },
                'category_form': {
                    'fields': [
                        {'name': 'category_code', 'type': 'input', 'label': '分类代码', 'required': True, 'validation': r'^[a-zA-Z][a-zA-Z0-9_]*$'},
                        {'name': 'category_name', 'type': 'input', 'label': '分类名称', 'required': True},
                        {'name': 'parent_category', 'type': 'tree_select', 'label': '父分类', 'source': 'category_tree'},
                        {'name': 'category_description', 'type': 'textarea', 'label': '分类描述'},
                        {'name': 'sort_order', 'type': 'number', 'label': '排序', 'default': 0},
                        {'name': 'is_active', 'type': 'switch', 'label': '启用状态', 'default': True}
                    ]
                },
                'batch_operations': ['move', 'delete', 'activate', 'deactivate']
            }
        }

    def _create_template_form(self) -> Dict:
        """创建要素模板表单 - 增强版"""
        return {
            'form_layout': 'tabs',
            'tabs': [
                {
                    'name': 'basic_info',
                    'label': '基本信息',
                    'fields': [
                        {
                            'name': 'contract_type',
                            'type': 'multi_select',
                            'label': '适用合同类型',
                            'required': True,
                            'source': '/api/v1/contract-types',
                            'help': '选择此要素适用的合同类型'
                        },
                        {
                            'name': 'element_category',
                            'type': 'cascader',
                            'label': '要素分类',
                            'required': True,
                            'source': '/api/v1/element-categories/tree',
                            'help': '选择要素所属的分类'
                        },
                        {
                            'name': 'element_name',
                            'type': 'input',
                            'label': '要素名称',
                            'required': True,
                            'placeholder': '如：合同当事方、合同价款等',
                            'validation': {
                                'pattern': r'^[\u4e00-\u9fa5a-zA-Z][a-zA-Z0-9\u4e00-\u9fa5_\s]*$',
                                'message': '要素名称只能包含中文、英文、数字、下划线和空格，且不能以数字开头'
                            }
                        },
                        {
                            'name': 'element_display_name',
                            'type': 'input',
                            'label': '显示名称',
                            'placeholder': '用于界面显示的友好名称'
                        },
                        {
                            'name': 'element_description',
                            'type': 'textarea',
                            'label': '要素描述',
                            'placeholder': '详细描述此要素的业务含义和用途',
                            'rows': 3
                        },
                        {
                            'name': 'data_type',
                            'type': 'select',
                            'label': '数据类型',
                            'required': True,
                            'options': [
                                {'value': 'text', 'label': '文本', 'description': '普通文本内容'},
                                {'value': 'amount', 'label': '金额', 'description': '货币金额，支持单位转换'},
                                {'value': 'date', 'label': '日期', 'description': '日期时间，支持多种格式'},
                                {'value': 'party_info', 'label': '当事方信息', 'description': '包含姓名、地址、联系方式等'},
                                {'value': 'clause', 'label': '条款', 'description': '合同条款，包含编号、层级、内容等'},
                                {'value': 'boolean', 'label': '布尔值', 'description': '是/否类型'},
                                {'value': 'enum', 'label': '枚举', 'description': '预定义选项列表'},
                                {'value': 'list', 'label': '列表', 'description': '多个值的列表'}
                            ]
                        }
                    ]
                },
                {
                    'name': 'extraction_rules',
                    'label': '提取规则',
                    'fields': [
                        {
                            'name': 'patterns',
                            'type': 'pattern_editor',
                            'label': '正则表达式模式',
                            'required': True,
                            'component': 'advanced_pattern_editor'
                        }
                    ]
                },
                {
                    'name': 'validation_rules',
                    'label': '验证规则',
                    'fields': [
                        {
                            'name': 'validation_config',
                            'type': 'validation_editor',
                            'label': '验证配置',
                            'component': 'validation_rule_builder'
                        }
                    ]
                },
                {
                    'name': 'advanced_settings',
                    'label': '高级设置',
                    'fields': [
                        {
                            'name': 'is_required',
                            'type': 'switch',
                            'label': '必需要素',
                            'help': '标记为必需的要素在提取失败时会产生警告'
                        },
                        {
                            'name': 'weight',
                            'type': 'slider',
                            'label': '权重',
                            'min': 0.1,
                            'max': 10.0,
                            'step': 0.1,
                            'default': 1.0,
                            'help': '要素的重要性权重，影响整体置信度计算'
                        },
                        {
                            'name': 'sort_order',
                            'type': 'number',
                            'label': '排序',
                            'default': 0,
                            'help': '在界面中的显示顺序'
                        },
                        {
                            'name': 'tags',
                            'type': 'tag_input',
                            'label': '标签',
                            'help': '用于分类和搜索的标签'
                        }
                    ]
                }
            ],
            'form_actions': [
                {'name': 'save', 'label': '保存', 'type': 'primary'},
                {'name': 'save_and_test', 'label': '保存并测试', 'type': 'success'},
                {'name': 'preview', 'label': '预览', 'type': 'default'},
                {'name': 'cancel', 'label': '取消', 'type': 'default'}
            ]
        }

    def _create_pattern_editor(self) -> Dict:
        """创建高级正则表达式编辑器"""
        return {
            'component_type': 'advanced_pattern_editor',
            'layout': {
                'left_panel': {
                    'width': '60%',
                    'components': [
                        {
                            'name': 'pattern_list',
                            'type': 'editable_list',
                            'title': '提取模式列表',
                            'item_template': {
                                'pattern': {'type': 'code_editor', 'language': 'regex'},
                                'type': {'type': 'input', 'placeholder': '模式类型'},
                                'confidence': {'type': 'slider', 'min': 0.1, 'max': 1.0, 'step': 0.1},
                                'description': {'type': 'input', 'placeholder': '模式描述'}
                            },
                            'actions': ['add', 'delete', 'move_up', 'move_down', 'duplicate']
                        }
                    ]
                },
                'right_panel': {
                    'width': '40%',
                    'components': [
                        {
                            'name': 'pattern_library',
                            'type': 'collapsible_sections',
                            'sections': [
                                {
                                    'title': '常用模式',
                                    'patterns': {
                                        '金额模式': {
                                            'patterns': [
                                                {'pattern': r'(人民币|￥|¥)?\s*([\d,，.．]+)\s*(元|万元|千元)', 'desc': '标准金额格式'},
                                                {'pattern': r'(价格|费用|金额)[：:]?\s*([\d,，.．]+)\s*(元|万元)', 'desc': '带标识的金额'},
                                                {'pattern': r'(违约金|保证金|定金)[：:]?\s*([\d,，.．]+)', 'desc': '特定类型金额'}
                                            ]
                                        },
                                        '日期模式': {
                                            'patterns': [
                                                {'pattern': r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日', 'desc': '中文日期格式'},
                                                {'pattern': r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})', 'desc': '数字日期格式'},
                                                {'pattern': r'(自|从)\s*([^至到]+)\s*(至|到)\s*([^，,；;。.]+)', 'desc': '日期范围'}
                                            ]
                                        },
                                        '当事方模式': {
                                            'patterns': [
                                                {'pattern': r'甲\s*方[：:]\s*(.+?)(?=\n|乙方|丙方|$)', 'desc': '甲方信息'},
                                                {'pattern': r'乙\s*方[：:]\s*(.+?)(?=\n|甲方|丙方|$)', 'desc': '乙方信息'},
                                                {'pattern': r'(委托方|受托方)[：:]\s*(.+?)(?=\n|受托方|委托方|$)', 'desc': '委托关系当事方'}
                                            ]
                                        },
                                        '联系方式模式': {
                                            'patterns': [
                                                {'pattern': r'(电话|手机|联系电话)[：:]?\s*([\d\-\s\(\)（）]+)', 'desc': '电话号码'},
                                                {'pattern': r'(邮箱|电子邮箱|E-mail)[：:]?\s*([\w\.-]+@[\w\.-]+\.\w+)', 'desc': '电子邮箱'},
                                                {'pattern': r'(地址|住所|注册地址)[：:]?\s*([^，,；;电话手机传真邮箱]+)', 'desc': '地址信息'}
                                            ]
                                        }
                                    }
                                },
                                {
                                    'title': '条款模式',
                                    'patterns': {
                                        '条款编号': {
                                            'patterns': [
                                                {'pattern': r'^第([一二三四五六七八九十百千万\d]+)条\s*(.*)$', 'desc': '第X条格式'},
                                                {'pattern': r'^([一二三四五六七八九十百千万]+)、\s*(.*)$', 'desc': '中文序号'},
                                                {'pattern': r'^(\d+)\.\s*(.*)$', 'desc': '数字序号'}
                                            ]
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            'name': 'pattern_tools',
                            'type': 'tool_panel',
                            'tools': [
                                {
                                    'name': 'regex_validator',
                                    'label': '正则验证器',
                                    'description': '验证正则表达式语法'
                                },
                                {
                                    'name': 'pattern_generator',
                                    'label': '模式生成器',
                                    'description': '基于示例文本生成正则表达式'
                                },
                                {
                                    'name': 'pattern_optimizer',
                                    'label': '模式优化器',
                                    'description': '优化正则表达式性能'
                                }
                            ]
                        }
                    ]
                }
            },
            'editor_features': {
                'code_editor': {
                    'language': 'regex',
                    'theme': 'vs-dark',
                    'features': [
                        'syntax_highlighting',
                        'error_detection',
                        'auto_completion',
                        'bracket_matching',
                        'find_replace'
                    ]
                },
                'real_time_validation': {
                    'enabled': True,
                    'show_errors': True,
                    'show_warnings': True,
                    'performance_check': True
                },
                'test_integration': {
                    'live_testing': True,
                    'test_cases': 'editable',
                    'result_highlighting': True
                }
            },
            'pattern_actions': [
                {'name': 'insert_from_library', 'label': '从模式库插入', 'icon': 'library'},
                {'name': 'test_pattern', 'label': '测试模式', 'icon': 'test'},
                {'name': 'optimize_pattern', 'label': '优化模式', 'icon': 'optimize'},
                {'name': 'export_pattern', 'label': '导出模式', 'icon': 'export'}
            ]
        }

    def _create_relationship_manager(self) -> Dict:
        """创建要素关系管理器"""
        return {
            'component_type': 'relationship_manager',
            'layout': {
                'main_area': {
                    'component': 'relationship_graph',
                    'features': [
                        'visual_graph',      # 可视化关系图
                        'drag_drop_nodes',   # 拖拽节点
                        'connection_lines',  # 连接线
                        'relationship_types' # 关系类型标识
                    ]
                },
                'side_panel': {
                    'tabs': [
                        {
                            'name': 'relationship_form',
                            'label': '关系配置',
                            'fields': [
                                {
                                    'name': 'relationship_name',
                                    'type': 'input',
                                    'label': '关系名称',
                                    'required': True
                                },
                                {
                                    'name': 'relationship_type',
                                    'type': 'select',
                                    'label': '关系类型',
                                    'required': True,
                                    'options': [
                                        {'value': 'dependency', 'label': '依赖关系', 'description': '某要素依赖其他要素存在'},
                                        {'value': 'mutual_exclusion', 'label': '互斥关系', 'description': '某些要素不能同时存在'},
                                        {'value': 'composition', 'label': '组合关系', 'description': '多个要素组成复合要素'},
                                        {'value': 'calculation', 'label': '计算关系', 'description': '某要素值由其他要素计算得出'}
                                    ]
                                },
                                {
                                    'name': 'source_elements',
                                    'type': 'element_selector',
                                    'label': '源要素',
                                    'multiple': True,
                                    'required': True
                                },
                                {
                                    'name': 'target_elements',
                                    'type': 'element_selector',
                                    'label': '目标要素',
                                    'multiple': True,
                                    'required': True
                                },
                                {
                                    'name': 'relationship_config',
                                    'type': 'dynamic_config',
                                    'label': '关系配置',
                                    'depends_on': 'relationship_type'
                                }
                            ]
                        },
                        {
                            'name': 'relationship_list',
                            'label': '关系列表',
                            'component': 'relationship_grid',
                            'columns': ['name', 'type', 'source_count', 'target_count', 'status', 'actions']
                        }
                    ]
                }
            },
            'relationship_config_templates': {
                'dependency': {
                    'fields': [
                        {'name': 'strict_mode', 'type': 'switch', 'label': '严格模式', 'default': True}
                    ]
                },
                'mutual_exclusion': {
                    'fields': [
                        {'name': 'resolution_strategy', 'type': 'select', 'label': '冲突解决策略',
                         'options': ['highest_confidence', 'first_found', 'manual_review']}
                    ]
                },
                'composition': {
                    'fields': [
                        {'name': 'composition_rule', 'type': 'select', 'label': '组合规则',
                         'options': ['merge_all', 'weighted_merge', 'custom_rule']},
                        {'name': 'custom_rule', 'type': 'code_editor', 'label': '自定义规则', 'language': 'javascript'}
                    ]
                },
                'calculation': {
                    'fields': [
                        {'name': 'calculation_type', 'type': 'select', 'label': '计算类型',
                         'options': ['sum', 'average', 'concat', 'custom']},
                        {'name': 'custom_formula', 'type': 'formula_editor', 'label': '自定义公式'}
                    ]
                }
            }
        }

    def _create_test_panel(self) -> Dict:
        """创建增强测试面板"""
        return {
            'component_type': 'advanced_test_panel',
            'layout': {
                'test_input': {
                    'component': 'test_input_area',
                    'features': [
                        {
                            'name': 'text_input',
                            'type': 'code_editor',
                            'label': '测试文本',
                            'language': 'text',
                            'placeholder': '在此输入合同文本进行测试...',
                            'features': ['line_numbers', 'word_wrap', 'search']
                        },
                        {
                            'name': 'file_upload',
                            'type': 'file_uploader',
                            'label': '上传测试文件',
                            'accept': ['.txt', '.doc', '.docx'],
                            'max_size': '10MB'
                        },
                        {
                            'name': 'sample_selector',
                            'type': 'select',
                            'label': '选择示例文本',
                            'source': '/api/v1/test-samples'
                        }
                    ]
                },
                'test_controls': {
                    'component': 'test_control_panel',
                    'controls': [
                        {
                            'name': 'test_mode',
                            'type': 'radio_group',
                            'label': '测试模式',
                            'options': [
                                {'value': 'single_element', 'label': '单要素测试'},
                                {'value': 'all_elements', 'label': '全要素测试'},
                                {'value': 'contract_type', 'label': '按合同类型测试'}
                            ]
                        },
                        {
                            'name': 'element_selector',
                            'type': 'element_tree_select',
                            'label': '选择要素',
                            'visible_when': 'test_mode == single_element'
                        },
                        {
                            'name': 'contract_type_selector',
                            'type': 'select',
                            'label': '合同类型',
                            'visible_when': 'test_mode == contract_type'
                        }
                    ],
                    'actions': [
                        {'name': 'run_test', 'label': '运行测试', 'type': 'primary', 'icon': 'play'},
                        {'name': 'clear_results', 'label': '清除结果', 'type': 'default', 'icon': 'clear'},
                        {'name': 'save_test_case', 'label': '保存测试用例', 'type': 'default', 'icon': 'save'}
                    ]
                },
                'test_results': {
                    'component': 'test_results_panel',
                    'sections': [
                        {
                            'name': 'extraction_results',
                            'title': '提取结果',
                            'component': 'extraction_results_table',
                            'columns': ['element_name', 'extracted_value', 'confidence', 'pattern_matched', 'source_text']
                        },
                        {
                            'name': 'performance_metrics',
                            'title': '性能指标',
                            'component': 'metrics_dashboard',
                            'metrics': [
                                {'name': 'extraction_count', 'label': '提取数量', 'type': 'number'},
                                {'name': 'confidence_average', 'label': '平均置信度', 'type': 'percentage'},
                                {'name': 'processing_time', 'label': '处理时间', 'type': 'duration'},
                                {'name': 'pattern_coverage', 'label': '模式覆盖率', 'type': 'percentage'},
                                {'name': 'success_rate', 'label': '成功率', 'type': 'percentage'}
                            ]
                        },
                        {
                            'name': 'text_highlighting',
                            'title': '文本高亮',
                            'component': 'highlighted_text_viewer',
                            'features': ['color_coding', 'hover_details', 'click_to_edit']
                        }
                    ]
                }
            },
            'test_features': {
                'real_time_testing': {
                    'enabled': True,
                    'debounce_delay': 500
                },
                'batch_testing': {
                    'enabled': True,
                    'max_files': 10,
                    'parallel_processing': True
                },
                'result_export': {
                    'formats': ['json', 'csv', 'excel'],
                    'include_source_text': True
                },
                'test_history': {
                    'enabled': True,
                    'max_history': 50,
                    'auto_save': True
                }
            }
        }

    def _create_import_export_panel(self) -> Dict:
        """创建导入导出面板"""
        return {
            'component_type': 'import_export_panel',
            'sections': [
                {
                    'name': 'export_section',
                    'title': '配置导出',
                    'features': [
                        {
                            'name': 'export_scope',
                            'type': 'checkbox_group',
                            'label': '导出范围',
                            'options': [
                                {'value': 'contract_types', 'label': '合同类型'},
                                {'value': 'element_categories', 'label': '要素分类'},
                                {'value': 'element_templates', 'label': '要素模板'},
                                {'value': 'relationships', 'label': '要素关系'},
                                {'value': 'test_cases', 'label': '测试用例'}
                            ]
                        },
                        {
                            'name': 'export_format',
                            'type': 'select',
                            'label': '导出格式',
                            'options': [
                                {'value': 'json', 'label': 'JSON格式'},
                                {'value': 'yaml', 'label': 'YAML格式'},
                                {'value': 'excel', 'label': 'Excel格式'}
                            ]
                        }
                    ]
                },
                {
                    'name': 'import_section',
                    'title': '配置导入',
                    'features': [
                        {
                            'name': 'file_upload',
                            'type': 'file_uploader',
                            'label': '选择配置文件',
                            'accept': ['.json', '.yaml', '.yml', '.xlsx'],
                            'validation': True
                        },
                        {
                            'name': 'import_options',
                            'type': 'checkbox_group',
                            'label': '导入选项',
                            'options': [
                                {'value': 'overwrite_existing', 'label': '覆盖已存在的配置'},
                                {'value': 'validate_before_import', 'label': '导入前验证'},
                                {'value': 'create_backup', 'label': '创建备份'},
                                {'value': 'dry_run', 'label': '试运行（不实际导入）'}
                            ]
                        }
                    ]
                },
                {
                    'name': 'template_section',
                    'title': '配置模板',
                    'features': [
                        {
                            'name': 'template_library',
                            'type': 'template_grid',
                            'templates': [
                                {'name': '销售合同模板', 'description': '包含销售合同常用要素配置'},
                                {'name': '服务合同模板', 'description': '包含服务合同常用要素配置'},
                                {'name': '租赁合同模板', 'description': '包含租赁合同常用要素配置'}
                            ]
                        }
                    ]
                }
            ]
        }

# 前端技术实现详细说明
class FrontendImplementation:
    """前端配置界面技术实现"""

    def __init__(self):
        self.tech_stack = {
            'framework': 'Vue 3.x',
            'ui_library': 'Element Plus',
            'state_management': 'Pinia',
            'build_tool': 'Vite 7.1.3',
            'additional_libraries': [
                'Monaco Editor',      # 代码编辑器
                'D3.js',             # 关系图可视化
                'VueUse',            # Vue组合式工具库
                'Vue Router',        # 路由管理
                'Axios',             # HTTP客户端
                'Lodash',            # 工具函数库
                'Moment.js',         # 日期处理
                'JSZip',             # 文件压缩
                'FileSaver.js'       # 文件下载
            ]
        }

    def get_component_structure(self) -> Dict:
        """获取组件结构"""
        return {
            'pages': {
                'ConfigDashboard.vue': '配置主控制面板',
                'ElementTemplateManager.vue': '要素模板管理',
                'CategoryManager.vue': '分类管理',
                'RelationshipManager.vue': '关系管理',
                'TestPanel.vue': '测试面板',
                'ImportExport.vue': '导入导出'
            },
            'components': {
                'common': {
                    'PatternEditor.vue': '正则表达式编辑器',
                    'ElementSelector.vue': '要素选择器',
                    'CategoryTree.vue': '分类树组件',
                    'RelationshipGraph.vue': '关系图组件',
                    'TestResultsTable.vue': '测试结果表格',
                    'ConfigForm.vue': '配置表单组件'
                },
                'editors': {
                    'MonacoEditor.vue': 'Monaco编辑器封装',
                    'FormulaEditor.vue': '公式编辑器',
                    'ValidationRuleBuilder.vue': '验证规则构建器',
                    'PatternLibrary.vue': '模式库组件'
                },
                'visualizations': {
                    'RelationshipD3Graph.vue': 'D3关系图',
                    'MetricsDashboard.vue': '指标仪表板',
                    'HighlightedTextViewer.vue': '高亮文本查看器'
                }
            },
            'stores': {
                'configStore.js': '配置状态管理',
                'elementStore.js': '要素状态管理',
                'testStore.js': '测试状态管理',
                'userStore.js': '用户状态管理'
            },
            'utils': {
                'regexValidator.js': '正则表达式验证',
                'patternGenerator.js': '模式生成器',
                'configExporter.js': '配置导出工具',
                'testRunner.js': '测试运行器'
            }
        }

    def get_key_features_implementation(self) -> Dict:
        """获取关键功能实现"""
        return {
            'pattern_editor': {
                'technology': 'Monaco Editor + Custom Language Support',
                'features': [
                    '正则表达式语法高亮',
                    '实时语法验证',
                    '自动补全',
                    '错误提示',
                    '性能分析'
                ],
                'implementation': '''
                // PatternEditor.vue 核心实现
                <template>
                  <div class="pattern-editor">
                    <monaco-editor
                      v-model="pattern"
                      language="regex"
                      :options="editorOptions"
                      @change="validatePattern"
                    />
                    <div class="validation-panel">
                      <el-alert v-if="validationError" type="error" :title="validationError" />
                      <el-tag v-if="performanceScore" :type="getPerformanceType()">
                        性能评分: {{ performanceScore }}
                      </el-tag>
                    </div>
                  </div>
                </template>
                '''
            },
            'relationship_graph': {
                'technology': 'D3.js + Vue 3 Composition API',
                'features': [
                    '可视化关系图',
                    '拖拽节点',
                    '动态连接线',
                    '关系类型标识',
                    '缩放和平移'
                ],
                'implementation': '''
                // RelationshipGraph.vue 核心实现
                <template>
                  <div ref="graphContainer" class="relationship-graph"></div>
                </template>

                <script setup>
                import * as d3 from 'd3'
                import { ref, onMounted, watch } from 'vue'

                const graphContainer = ref(null)
                const props = defineProps(['relationships', 'elements'])

                const initGraph = () => {
                  const svg = d3.select(graphContainer.value)
                    .append('svg')
                    .attr('width', '100%')
                    .attr('height', '100%')

                  // 实现D3力导向图
                  const simulation = d3.forceSimulation()
                    .force('link', d3.forceLink().id(d => d.id))
                    .force('charge', d3.forceManyBody())
                    .force('center', d3.forceCenter())
                }
                </script>
                '''
            },
            'test_panel': {
                'technology': 'Vue 3 + Element Plus + Web Workers',
                'features': [
                    '实时测试',
                    '批量处理',
                    '结果高亮',
                    '性能监控',
                    '历史记录'
                ],
                'implementation': '''
                // TestPanel.vue 核心实现
                const runTest = async () => {
                  loading.value = true

                  try {
                    // 使用Web Worker进行后台处理
                    const worker = new Worker('/workers/testWorker.js')

                    worker.postMessage({
                      text: testText.value,
                      elements: selectedElements.value,
                      mode: testMode.value
                    })

                    worker.onmessage = (event) => {
                      testResults.value = event.data.results
                      performanceMetrics.value = event.data.metrics
                      highlightResults()
                    }
                  } catch (error) {
                    ElMessage.error('测试执行失败: ' + error.message)
                  } finally {
                    loading.value = false
                  }
                }
                '''
            },
            'form_validation': {
                'technology': 'Element Plus Form + Custom Validators',
                'features': [
                    '实时验证',
                    '自定义验证规则',
                    '异步验证',
                    '表单联动',
                    '错误提示'
                ],
                'implementation': '''
                // 自定义验证规则
                const validateElementName = (rule, value, callback) => {
                  if (!value) {
                    callback(new Error('要素名称不能为空'))
                  } else if (!/^[\u4e00-\u9fa5a-zA-Z][a-zA-Z0-9\u4e00-\u9fa5_\s]*$/.test(value)) {
                    callback(new Error('要素名称格式不正确'))
                  } else {
                    // 异步验证名称唯一性
                    checkElementNameUnique(value).then(isUnique => {
                      if (!isUnique) {
                        callback(new Error('要素名称已存在'))
                      } else {
                        callback()
                      }
                    })
                  }
                }
                '''
            }
        }

    def get_api_integration(self) -> Dict:
        """获取API集成方案"""
        return {
            'http_client': {
                'library': 'Axios',
                'configuration': '''
                // api/client.js
                import axios from 'axios'

                const apiClient = axios.create({
                  baseURL: '/api/v1',
                  timeout: 30000,
                  headers: {
                    'Content-Type': 'application/json'
                  }
                })

                // 请求拦截器
                apiClient.interceptors.request.use(config => {
                  const token = localStorage.getItem('token')
                  if (token) {
                    config.headers.Authorization = `Bearer ${token}`
                  }
                  return config
                })

                // 响应拦截器
                apiClient.interceptors.response.use(
                  response => response.data,
                  error => {
                    ElMessage.error(error.response?.data?.message || '请求失败')
                    return Promise.reject(error)
                  }
                )
                '''
            },
            'api_services': {
                'structure': '''
                // api/services/
                ├── elementService.js      # 要素相关API
                ├── categoryService.js     # 分类相关API
                ├── relationshipService.js # 关系相关API
                ├── testService.js         # 测试相关API
                └── configService.js       # 配置相关API
                ''',
                'example': '''
                // api/services/elementService.js
                export const elementService = {
                  // 获取要素模板列表
                  getElementTemplates: (params) =>
                    apiClient.get('/element-templates', { params }),

                  // 创建要素模板
                  createElementTemplate: (data) =>
                    apiClient.post('/element-templates', data),

                  // 测试要素提取
                  testElementExtraction: (data) =>
                    apiClient.post('/element-extraction/test', data),

                  // 获取要素分类树
                  getCategoryTree: () =>
                    apiClient.get('/element-categories/tree')
                }
                '''
            }
        }

    async def _load_contract_type_config(self) -> Dict:
        """加载合同类型识别配置 - 增强版"""
        return {
            'contract_types': [
                {
                    'type': 'sales',
                    'name': '销售合同',
                    'keywords': [
                        {'keyword': '销售合同', 'weight': 3.0},
                        {'keyword': '买卖合同', 'weight': 3.0},
                        {'keyword': '购销合同', 'weight': 3.0},
                        {'keyword': '商品销售', 'weight': 2.0},
                        {'keyword': '货物买卖', 'weight': 2.0},
                        {'keyword': '产品销售', 'weight': 2.0}
                    ],
                    'patterns': [
                        {'pattern': r'(商品|货物|产品).*?(规格|型号|品牌)', 'weight': 1.5},
                        {'pattern': r'(交货|交付|发货).*?(时间|期限|地点)', 'weight': 1.5},
                        {'pattern': r'(质量|品质).*?(标准|要求|保证)', 'weight': 1.0}
                    ],
                    'max_score': 10.0,
                    'priority': 1
                },
                {
                    'type': 'service',
                    'name': '服务合同',
                    'keywords': [
                        {'keyword': '服务合同', 'weight': 3.0},
                        {'keyword': '服务协议', 'weight': 3.0},
                        {'keyword': '技术服务', 'weight': 2.5},
                        {'keyword': '咨询服务', 'weight': 2.5},
                        {'keyword': '专业服务', 'weight': 2.0},
                        {'keyword': '外包服务', 'weight': 2.0}
                    ],
                    'patterns': [
                        {'pattern': r'服务.*?(内容|范围|标准)', 'weight': 1.5},
                        {'pattern': r'(验收|交付).*?(标准|条件)', 'weight': 1.5},
                        {'pattern': r'(知识产权|保密).*?(条款|协议)', 'weight': 1.0}
                    ],
                    'max_score': 10.0,
                    'priority': 2
                },
                {
                    'type': 'lease',
                    'name': '租赁合同',
                    'keywords': [
                        {'keyword': '租赁合同', 'weight': 3.0},
                        {'keyword': '租赁协议', 'weight': 3.0},
                        {'keyword': '房屋租赁', 'weight': 2.5},
                        {'keyword': '设备租赁', 'weight': 2.5},
                        {'keyword': '场地租赁', 'weight': 2.0},
                        {'keyword': '出租', 'weight': 1.5}
                    ],
                    'patterns': [
                        {'pattern': r'(租金|租费).*?(标准|金额)', 'weight': 2.0},
                        {'pattern': r'(租期|租赁期限)', 'weight': 2.0},
                        {'pattern': r'(押金|保证金)', 'weight': 1.5},
                        {'pattern': r'(维修|保养).*?责任', 'weight': 1.0}
                    ],
                    'max_score': 10.0,
                    'priority': 3
                },
                {
                    'type': 'employment',
                    'name': '劳动合同',
                    'keywords': [
                        {'keyword': '劳动合同', 'weight': 3.0},
                        {'keyword': '雇佣合同', 'weight': 3.0},
                        {'keyword': '聘用合同', 'weight': 3.0},
                        {'keyword': '工作合同', 'weight': 2.0},
                        {'keyword': '员工', 'weight': 1.0}
                    ],
                    'patterns': [
                        {'pattern': r'(工作|职位).*?(内容|职责)', 'weight': 2.0},
                        {'pattern': r'(薪酬|工资|报酬)', 'weight': 2.0},
                        {'pattern': r'(工作时间|上班时间)', 'weight': 1.5},
                        {'pattern': r'(社会保险|社保|公积金)', 'weight': 1.5}
                    ],
                    'max_score': 10.0,
                    'priority': 4
                },
                {
                    'type': 'commission',
                    'name': '委托合同',
                    'keywords': [
                        {'keyword': '委托合同', 'weight': 3.0},
                        {'keyword': '委托协议', 'weight': 3.0},
                        {'keyword': '代理合同', 'weight': 2.5},
                        {'keyword': '代理协议', 'weight': 2.5},
                        {'keyword': '委托代理', 'weight': 2.0}
                    ],
                    'patterns': [
                        {'pattern': r'委托.*?(事项|内容|范围)', 'weight': 2.0},
                        {'pattern': r'(代理|代办).*?(权限|范围)', 'weight': 1.5},
                        {'pattern': r'(报酬|佣金|费用)', 'weight': 1.5}
                    ],
                    'max_score': 10.0,
                    'priority': 5
                }
            ]
        }

    async def _load_elements_config(self, contract_type: str) -> Dict:
        """加载合同要素提取配置"""
        return {
            'basic_elements': {
                'parties': {
                    'patterns': [
                        {'pattern': r'甲\s*方[：:]\s*(.+?)(?=\n|乙方|丙方|$)', 'role': '甲方'},
                        {'pattern': r'乙\s*方[：:]\s*(.+?)(?=\n|甲方|丙方|$)', 'role': '乙方'}
                    ]
                },
                'subject_matter': {
                    'patterns': [
                        {'pattern': r'(标的|合同标的)[：:]?\s*([^，,；;。.]+)', 'type': 'subject'},
                        {'pattern': r'(商品|货物|产品)[：:]?\s*([^，,；;。.]+)', 'type': 'goods'},
                        {'pattern': r'(服务内容|服务项目)[：:]?\s*([^，,；;。.]+)', 'type': 'service'}
                    ]
                },
                'performance_period': {
                    'patterns': [
                        {'pattern': r'(合同期限|履行期限)[：:]?\s*([^，,；;。.]+)', 'type': 'period'},
                        {'pattern': r'(自|从)\s*([^至到]+)\s*(至|到)\s*([^，,；;。.]+)', 'type': 'duration'}
                    ]
                },
                'performance_location': {
                    'patterns': [
                        {'pattern': r'(履行地点|交付地点|服务地点)[：:]?\s*([^，,；;。.]+)', 'type': 'location'}
                    ]
                }
            },
            'financial_elements': {
                'contract_price': {
                    'patterns': [
                        {'pattern': r'(合同总价|总价款|合同金额)[：:]?\s*([\d,，.．]+)\s*(元|万元)', 'type': 'total_price'},
                        {'pattern': r'(单价|价格)[：:]?\s*([\d,，.．]+)\s*(元|万元)', 'type': 'unit_price'}
                    ]
                },
                'payment_terms': {
                    'patterns': [
                        {'pattern': r'(付款方式|支付方式)[：:]?\s*([^，,；;。.]+)', 'type': 'payment_method'},
                        {'pattern': r'(付款期限|支付期限)[：:]?\s*([^，,；;。.]+)', 'type': 'payment_deadline'}
                    ]
                },
                'penalty': {
                    'patterns': [
                        {'pattern': r'(违约金)[：:]?\s*([\d,，.．]+)\s*(元|万元|%)', 'type': 'penalty_amount'},
                        {'pattern': r'(赔偿|损失赔偿)[：:]?\s*([^，,；;。.]+)', 'type': 'compensation'}
                    ]
                }
            },
            'specific_elements': {
                'sales': {
                    'product_specs': {
                        'patterns': [
                            {'pattern': r'(规格|型号|品牌)[：:]?\s*([^，,；;。.]+)', 'type': 'specification'},
                            {'pattern': r'(质量标准|技术标准)[：:]?\s*([^，,；;。.]+)', 'type': 'quality_standard'}
                        ]
                    },
                    'delivery': {
                        'patterns': [
                            {'pattern': r'(交货期|交付时间)[：:]?\s*([^，,；;。.]+)', 'type': 'delivery_time'},
                            {'pattern': r'(验收标准|验收方式)[：:]?\s*([^，,；;。.]+)', 'type': 'acceptance_criteria'}
                        ]
                    }
                },
                'lease': {
                    'rental_object': {
                        'patterns': [
                            {'pattern': r'(租赁物|出租物)[：:]?\s*([^，,；;。.]+)', 'type': 'rental_object'},
                            {'pattern': r'(房屋地址|物业地址)[：:]?\s*([^，,；;。.]+)', 'type': 'property_address'}
                        ]
                    },
                    'rental_terms': {
                        'patterns': [
                            {'pattern': r'(租金)[：:]?\s*([\d,，.．]+)\s*(元|万元)', 'type': 'rent_amount'},
                            {'pattern': r'(租期|租赁期限)[：:]?\s*([^，,；;。.]+)', 'type': 'lease_term'},
                            {'pattern': r'(押金|保证金)[：:]?\s*([\d,，.．]+)\s*(元|万元)', 'type': 'deposit'}
                        ]
                    }
                }
            },
            'risk_elements': {
                'force_majeure': {
                    'patterns': [
                        {'pattern': r'(不可抗力)([^。.]+)', 'type': 'force_majeure_clause'}
                    ]
                },
                'confidentiality': {
                    'patterns': [
                        {'pattern': r'(保密|机密)([^。.]+)', 'type': 'confidentiality_clause'}
                    ]
                },
                'intellectual_property': {
                    'patterns': [
                        {'pattern': r'(知识产权|专利|商标)([^。.]+)', 'type': 'ip_clause'}
                    ]
                }
            }
        }

    async def _extract_basic_elements(self, text: str, config: Dict) -> Dict:
        """提取基础要素"""
        import re

        basic_elements = {}

        for element_name, element_config in config.items():
            element_results = []

            for pattern_config in element_config['patterns']:
                matches = re.finditer(pattern_config['pattern'], text, re.MULTILINE | re.DOTALL)
                for match in matches:
                    if len(match.groups()) >= 2:
                        element_results.append({
                            'type': pattern_config.get('type', element_name),
                            'role': pattern_config.get('role', ''),
                            'text': match.group(),
                            'value': match.group(2).strip() if len(match.groups()) >= 2 else match.group(1).strip(),
                            'confidence': 0.8
                        })

            basic_elements[element_name] = element_results

        return basic_elements

    async def _extract_financial_elements(self, text: str, config: Dict) -> Dict:
        """提取财务要素"""
        import re

        financial_elements = {}

        for element_name, element_config in config.items():
            element_results = []

            for pattern_config in element_config['patterns']:
                matches = re.finditer(pattern_config['pattern'], text)
                for match in matches:
                    result = {
                        'type': pattern_config['type'],
                        'text': match.group(),
                        'confidence': 0.8
                    }

                    # 如果是金额，提取数值
                    if '金额' in element_name or '价' in element_name or 'penalty' in pattern_config['type']:
                        try:
                            amount_text = match.group(2) if len(match.groups()) >= 2 else match.group(1)
                            amount_value = float(amount_text.replace(',', '').replace('，', ''))
                            result['value'] = amount_value
                            result['currency'] = '人民币'

                            # 判断单位
                            if '万元' in match.group():
                                result['value'] *= 10000
                                result['unit'] = '万元'
                            else:
                                result['unit'] = '元'
                        except (ValueError, IndexError):
                            result['value'] = amount_text
                    else:
                        result['value'] = match.group(2).strip() if len(match.groups()) >= 2 else match.group(1).strip()

                    element_results.append(result)

            financial_elements[element_name] = element_results

        return financial_elements

    async def _extract_specific_elements(self, text: str, config: Dict) -> Dict:
        """提取特定要素（根据合同类型）"""
        import re

        specific_elements = {}

        for element_category, category_config in config.items():
            category_results = {}

            for element_name, element_config in category_config.items():
                element_results = []

                for pattern_config in element_config['patterns']:
                    matches = re.finditer(pattern_config['pattern'], text)
                    for match in matches:
                        element_results.append({
                            'type': pattern_config['type'],
                            'text': match.group(),
                            'value': match.group(2).strip() if len(match.groups()) >= 2 else match.group(1).strip(),
                            'confidence': 0.7
                        })

                category_results[element_name] = element_results

            specific_elements[element_category] = category_results

        return specific_elements

    async def _extract_risk_elements(self, text: str, config: Dict) -> Dict:
        """提取风险要素"""
        import re

        risk_elements = {}

        for element_name, element_config in config.items():
            element_results = []

            for pattern_config in element_config['patterns']:
                matches = re.finditer(pattern_config['pattern'], text)
                for match in matches:
                    # 风险要素通常需要提取整个条款内容
                    clause_content = match.group() if len(match.groups()) == 1 else match.group(1) + match.group(2)

                    element_results.append({
                        'type': pattern_config['type'],
                        'text': match.group(),
                        'clause_content': clause_content,
                        'risk_level': await self._assess_risk_level(clause_content, element_name),
                        'confidence': 0.6
                    })

            risk_elements[element_name] = element_results

        return risk_elements

    async def _assess_risk_level(self, clause_content: str, risk_type: str) -> str:
        """评估风险等级"""
        # 基于条款内容和风险类型评估风险等级
        risk_keywords = {
            'high': ['严重', '重大', '立即', '全部责任', '无限责任'],
            'medium': ['一般', '部分', '有限责任', '协商'],
            'low': ['轻微', '免责', '不承担']
        }

        for level, keywords in risk_keywords.items():
            if any(keyword in clause_content for keyword in keywords):
                return level

        return 'medium'  # 默认中等风险

    async def _load_extraction_config(self) -> Dict:
        """加载关键信息提取配置"""
        return {
            'amount_patterns': [
                {'pattern': r'(人民币|￥|¥)\s*([\d,，.．]+)\s*(元|万元|千元)', 'currency': '人民币'},
                {'pattern': r'([\d,，.．]+)\s*(元|万元|千元)', 'currency': '人民币'},
                {'pattern': r'(USD|美元|$)\s*([\d,，.．]+)', 'currency': '美元'},
                {'pattern': r'(违约金|保证金|定金|押金)[：:]?\s*([\d,，.．]+)\s*(元|万元)', 'currency': '人民币'}
            ],
            'amount_types': [
                {'keywords': ['违约金', '赔偿'], 'type': 'penalty'},
                {'keywords': ['保证金', '押金'], 'type': 'deposit'},
                {'keywords': ['定金'], 'type': 'earnest_money'},
                {'keywords': ['价格', '费用', '金额'], 'type': 'price'}
            ],
            'date_patterns': [
                {'pattern': r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日'},
                {'pattern': r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})'},
                {'pattern': r'(合同期限|有效期|履行期限)[：:]?\s*([^，,；;。.]+)'},
                {'pattern': r'(自|从)\s*([^至到]+)\s*(至|到)\s*([^，,；;。.]+)'}
            ],
            'date_types': [
                {'keywords': ['合同期限', '有效期'], 'type': 'contract_period'},
                {'keywords': ['履行期限', '交付'], 'type': 'performance_deadline'},
                {'keywords': ['签署', '签订'], 'type': 'signing_date'}
            ],
            'address_patterns': [
                {'pattern': r'(地址|住所|注册地址|办公地址)[：:]?\s*([^，,；;电话手机传真邮箱]+)'},
                {'pattern': r'(履行地点|交付地点|服务地点)[：:]?\s*([^，,；;。.]+)'}
            ],
            'contact_patterns': [
                {'pattern': r'(电话|手机|联系电话)[：:]?\s*([\d\-\s\(\)（）]+)'},
                {'pattern': r'(传真|传真号码)[：:]?\s*([\d\-\s\(\)（）]+)'},
                {'pattern': r'(邮箱|电子邮箱|E-mail)[：:]?\s*([\w\.-]+@[\w\.-]+\.\w+)'},
                {'pattern': r'(邮编|邮政编码)[：:]?\s*(\d{6})'}
            ]
        }

    async def _classify_amount_type(self, amount_text: str, amount_types: List[Dict]) -> str:
        """金额类型分类 - 配置化版本"""
        for type_rule in amount_types:
            if any(keyword in amount_text for keyword in type_rule['keywords']):
                return type_rule['type']
        return 'other'

    async def _classify_date_type(self, date_text: str, date_types: List[Dict]) -> str:
        """日期类型分类 - 配置化版本"""
        for type_rule in date_types:
            if any(keyword in date_text for keyword in type_rule['keywords']):
                return type_rule['type']
        return 'other'

    def _calculate_overall_confidence(self, structure: Dict) -> float:
        """计算整体提取置信度"""
        confidence_scores = []

        # 标题置信度
        if structure['title']['confidence'] > 0:
            confidence_scores.append(structure['title']['confidence'])

        # 当事方置信度
        if structure['parties']:
            party_confidence = sum(p['confidence'] for p in structure['parties']) / len(structure['parties'])
            confidence_scores.append(party_confidence)

        # 条款置信度
        if structure['clauses']:
            clause_confidence = sum(c['confidence'] for c in structure['clauses']) / len(structure['clauses'])
            confidence_scores.append(clause_confidence)

        # 合同要素置信度 (基于提取的要素数量和质量)
        if structure.get('contract_elements'):
            elements = structure['contract_elements']
            total_elements = 0
            total_confidence = 0.0

            for category, category_elements in elements.items():
                if isinstance(category_elements, dict):
                    for element_name, element_list in category_elements.items():
                        if isinstance(element_list, list):
                            for element in element_list:
                                total_elements += 1
                                total_confidence += element.get('confidence', 0.0)

            if total_elements > 0:
                elements_score = total_confidence / total_elements
                confidence_scores.append(elements_score)

        return sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0

# 文本提取服务接口
class TextExtractionService:
    def __init__(self):
        self.processor = DocumentProcessor()
        self.cache = {}  # 简单缓存机制

    async def extract_contract_text(self, file_path: str, contract_id: str) -> Dict:
        """合同文本提取主接口"""
        try:
            # 检查缓存
            if contract_id in self.cache:
                return self.cache[contract_id]

            # 执行文本提取
            result = await self.processor.extract_text(file_path)

            # 缓存结果
            self.cache[contract_id] = result

            return result

        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"文本提取失败: {str(e)}"
            )
```

**配置管理架构**：
```python
# 配置管理器
class ExtractionConfigManager:
    def __init__(self):
        self.config_cache = {}
        self.config_version = {}

    async def load_config(self, config_type: str) -> Dict:
        """从数据库或配置文件加载配置"""
        if config_type not in self.config_cache:
            # 从数据库加载配置
            config_data = await self._load_from_database(config_type)
            self.config_cache[config_type] = config_data

        return self.config_cache[config_type]

    async def update_config(self, config_type: str, new_config: Dict):
        """更新配置并清除缓存"""
        await self._save_to_database(config_type, new_config)
        self.config_cache.pop(config_type, None)

    async def _load_from_database(self, config_type: str) -> Dict:
        """从数据库加载配置的具体实现"""
        # 实际实现中会从数据库读取
        pass

# 配置数据表设计
CREATE TABLE extraction_configs (
    id BIGINT PRIMARY KEY,
    config_type VARCHAR(50) NOT NULL,  -- title, party, clause, elements
    config_name VARCHAR(100) NOT NULL,
    config_data JSON NOT NULL,
    version INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

# 合同类型配置表
CREATE TABLE contract_types (
    id BIGINT PRIMARY KEY,
    type_code VARCHAR(50) NOT NULL,
    type_name VARCHAR(100) NOT NULL,
    keywords JSON NOT NULL,  -- 识别关键词
    priority INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP
);

# 合同要素模板表（完全动态配置）
CREATE TABLE contract_element_templates (
    id BIGINT PRIMARY KEY,
    contract_type VARCHAR(50) NOT NULL,
    element_category VARCHAR(100) NOT NULL,  -- 要素分类（可自定义）
    element_name VARCHAR(200) NOT NULL,      -- 要素名称（可自定义）
    element_display_name VARCHAR(200),       -- 显示名称
    element_description TEXT,                -- 要素描述
    data_type VARCHAR(50) DEFAULT 'text',    -- text, amount, date, party_info, boolean
    element_config JSON NOT NULL,            -- 提取规则配置
    validation_rules JSON,                   -- 验证规则
    is_required BOOLEAN DEFAULT FALSE,       -- 是否必需要素
    weight DECIMAL(3,2) DEFAULT 1.0,        -- 权重
    sort_order INT DEFAULT 0,               -- 排序
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,

    UNIQUE KEY uk_contract_element (contract_type, element_category, element_name)
);

# 要素分类定义表
CREATE TABLE element_categories (
    id BIGINT PRIMARY KEY,
    category_code VARCHAR(100) NOT NULL,
    category_name VARCHAR(200) NOT NULL,
    category_description TEXT,
    parent_category VARCHAR(100),           -- 支持层级分类
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,

    UNIQUE KEY uk_category_code (category_code)
);

# 要素数据类型定义表
CREATE TABLE element_data_types (
    id BIGINT PRIMARY KEY,
    type_code VARCHAR(50) NOT NULL,
    type_name VARCHAR(100) NOT NULL,
    type_description TEXT,
    validation_schema JSON,                 -- JSON Schema验证规则
    processing_rules JSON,                  -- 数据处理规则
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,

    UNIQUE KEY uk_type_code (type_code)
);

# 要素关系定义表
CREATE TABLE element_relationships (
    id BIGINT PRIMARY KEY,
    relationship_name VARCHAR(200) NOT NULL,
    relationship_type VARCHAR(50) NOT NULL,    -- dependency, mutual_exclusion, composition, calculation
    source_elements JSON NOT NULL,             -- 源要素路径数组
    target_elements JSON NOT NULL,             -- 目标要素路径数组
    relationship_config JSON,                  -- 关系配置（如计算规则、组合规则等）
    contract_type VARCHAR(50),                 -- 适用的合同类型，NULL表示通用
    priority INT DEFAULT 0,                    -- 处理优先级
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

# 要素分类层级表（支持无限层级）
CREATE TABLE element_category_hierarchy (
    id BIGINT PRIMARY KEY,
    category_code VARCHAR(100) NOT NULL,
    category_name VARCHAR(200) NOT NULL,
    category_path VARCHAR(500) NOT NULL,       -- 完整路径，如 "基础信息/当事方信息/甲方信息"
    parent_category_code VARCHAR(100),         -- 父分类代码
    level_depth INT DEFAULT 1,                 -- 层级深度
    sort_order INT DEFAULT 0,
    category_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,

    UNIQUE KEY uk_category_code (category_code),
    INDEX idx_parent_category (parent_category_code),
    INDEX idx_category_path (category_path)
);

# 合同类型要素映射表
CREATE TABLE contract_type_element_mapping (
    id BIGINT PRIMARY KEY,
    contract_type VARCHAR(50) NOT NULL,
    element_template_id BIGINT NOT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    weight DECIMAL(3,2) DEFAULT 1.0,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,

    FOREIGN KEY (element_template_id) REFERENCES contract_element_templates(id),
    UNIQUE KEY uk_contract_element (contract_type, element_template_id)
);

# 要素提取结果表
CREATE TABLE contract_element_extractions (
    id BIGINT PRIMARY KEY,
    contract_id BIGINT NOT NULL,
    contract_type VARCHAR(50),
    element_category VARCHAR(100),
    element_name VARCHAR(200),
    element_type VARCHAR(50),
    extracted_value TEXT,
    confidence DECIMAL(3,2),
    risk_level VARCHAR(20),
    source_text TEXT,
    source_position JSON,                    -- 源文本位置信息 {paragraph: 1, start: 0, end: 100}
    clause_info JSON,                        -- 条款特有信息 {clause_number: "第一条", clause_level: "main", clause_type: "party_info"}
    validation_result JSON,                  -- 验证结果 {is_valid: true, errors: [], warnings: []}
    created_at TIMESTAMP,
    FOREIGN KEY (contract_id) REFERENCES contracts(id)
);

# 条款层级关系表
CREATE TABLE clause_relationships (
    id BIGINT PRIMARY KEY,
    contract_id BIGINT NOT NULL,
    parent_clause_id BIGINT,                 -- 父条款ID
    child_clause_id BIGINT NOT NULL,         -- 子条款ID
    relationship_type VARCHAR(50),           -- main_sub, parallel, reference
    relationship_order INT DEFAULT 0,        -- 关系顺序
    created_at TIMESTAMP,

    FOREIGN KEY (contract_id) REFERENCES contracts(id),
    FOREIGN KEY (parent_clause_id) REFERENCES contract_element_extractions(id),
    FOREIGN KEY (child_clause_id) REFERENCES contract_element_extractions(id)
);

# 条款内容分析表
CREATE TABLE clause_content_analysis (
    id BIGINT PRIMARY KEY,
    clause_extraction_id BIGINT NOT NULL,
    clause_keywords JSON,                    -- 条款关键词
    clause_entities JSON,                    -- 条款实体信息
    clause_sentiment VARCHAR(20),            -- 条款情感倾向 positive/negative/neutral
    clause_complexity_score DECIMAL(3,2),   -- 条款复杂度评分
    missing_elements JSON,                   -- 缺失的标准要素
    risk_indicators JSON,                    -- 风险指标
    created_at TIMESTAMP,

    FOREIGN KEY (clause_extraction_id) REFERENCES contract_element_extractions(id)
);
```

**配置化的优势**：
- **灵活性**：无需修改代码即可调整识别规则
- **可维护性**：配置集中管理，便于维护和更新
- **可扩展性**：轻松添加新的识别模式和规则
- **版本控制**：支持配置版本管理和回滚
- **A/B测试**：支持不同配置的效果对比

**性能优化策略**：
- **异步处理**：使用asyncio处理大文件解析
- **内存管理**：分块处理大型文档，避免内存溢出
- **缓存机制**：缓存已处理的文档结果和配置数据
- **并发控制**：限制同时处理的文档数量
- **错误恢复**：文档损坏时的降级处理方案
```

### 2.3 AI分析引擎
```
AI分析引擎
├── API调用管理
├── Prompt模板管理
├── 文本预处理
├── 缺失检测服务
├── 风险识别服务
└── 结果后处理
```

**技术实现**：
- 基于Qwen3 API的文本理解和分析
- 规则引擎 + API调用混合方法
- 知识库辅助的Prompt构建
- API响应缓存和优化
- 结果验证和后处理逻辑

### 2.4 外部服务集成
```
外部服务集成
├── RAG知识库服务客户端
├── 知识库查询接口
├── 服务状态监控
└── 降级处理机制
```

**技术实现**：
- RESTful API客户端封装
- 查询结果缓存机制
- 服务熔断和重试策略
- 降级到本地规则引擎

### 2.5 API管理模块
```
API管理模块
├── Qwen3 API客户端
├── 请求限流控制
├── 响应缓存管理
├── 错误处理和重试
└── 成本监控统计
```

**核心功能**：
- API密钥管理和轮换
- 请求频率限制和队列
- 智能缓存策略
- 降级和容错机制
- 调用成本统计和预警

## 3. 数据库设计

### 3.1 核心数据表

#### 3.1.1 用户相关表
```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role_id BIGINT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- 角色表
CREATE TABLE roles (
    id BIGINT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    permissions JSON
);
```

#### 3.1.2 合同相关表
```sql
-- 合同表
CREATE TABLE contracts (
    id BIGINT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    file_path VARCHAR(500),
    file_size BIGINT,
    file_type VARCHAR(20) CHECK (file_type IN ('doc', 'docx')),
    contract_type VARCHAR(50),
    status VARCHAR(20),
    user_id BIGINT,
    created_at TIMESTAMP
);

-- 文本提取记录表
CREATE TABLE text_extraction_records (
    id BIGINT PRIMARY KEY,
    contract_id BIGINT,
    extraction_status VARCHAR(20),
    raw_text TEXT,
    structured_data JSON,
    statistics JSON,
    extraction_time_ms INT,
    error_message TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- 审核记录表
CREATE TABLE audit_records (
    id BIGINT PRIMARY KEY,
    contract_id BIGINT,
    text_extraction_id BIGINT,
    audit_result JSON,
    risk_score DECIMAL(3,2),
    missing_clauses JSON,
    risk_points JSON,
    created_at TIMESTAMP,
    FOREIGN KEY (text_extraction_id) REFERENCES text_extraction_records(id)
);
```

#### 3.1.3 外部服务配置表
```sql
-- 外部服务配置表
CREATE TABLE external_services (
    id BIGINT PRIMARY KEY,
    service_name VARCHAR(50) NOT NULL,
    service_url VARCHAR(500) NOT NULL,
    api_key VARCHAR(255),
    timeout_seconds INT DEFAULT 30,
    retry_count INT DEFAULT 3,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- 服务调用日志表
CREATE TABLE service_call_logs (
    id BIGINT PRIMARY KEY,
    service_name VARCHAR(50),
    request_data TEXT,
    response_data TEXT,
    response_time_ms INT,
    status VARCHAR(20),
    error_message TEXT,
    created_at TIMESTAMP
);
```

### 3.2 索引设计
```sql
-- 性能优化索引
CREATE INDEX idx_contracts_user_id ON contracts(user_id);
CREATE INDEX idx_contracts_type_status ON contracts(contract_type, status);
CREATE INDEX idx_text_extraction_contract_id ON text_extraction_records(contract_id);
CREATE INDEX idx_text_extraction_status ON text_extraction_records(extraction_status);
CREATE INDEX idx_audit_records_contract_id ON audit_records(contract_id);
CREATE INDEX idx_audit_records_text_extraction_id ON audit_records(text_extraction_id);
CREATE INDEX idx_external_services_name ON external_services(service_name);
CREATE INDEX idx_service_call_logs_service_time ON service_call_logs(service_name, created_at);
```

## 4. API设计

### 4.1 RESTful API规范

#### 4.1.1 用户认证API
```
POST /api/v1/auth/login          # 用户登录
POST /api/v1/auth/logout         # 用户登出
POST /api/v1/auth/refresh        # 刷新令牌
GET  /api/v1/auth/profile        # 获取用户信息
```

#### 4.1.2 合同管理API
```
POST /api/v1/contracts           # 上传合同
GET  /api/v1/contracts           # 获取合同列表
GET  /api/v1/contracts/{id}      # 获取合同详情
PUT  /api/v1/contracts/{id}      # 更新合同信息
DELETE /api/v1/contracts/{id}    # 删除合同
```

#### 4.1.3 文本提取API
```
POST /api/v1/contracts/{id}/extract  # 提取合同文本
GET  /api/v1/contracts/{id}/text     # 获取提取结果
POST /api/v1/text/validate          # 验证文本提取质量
GET  /api/v1/text/statistics        # 获取文本统计信息
```

#### 4.1.4 要素配置管理API
```
# 要素模板管理
GET    /api/v1/element-templates                    # 获取要素模板列表
POST   /api/v1/element-templates                    # 创建要素模板
GET    /api/v1/element-templates/{id}               # 获取要素模板详情
PUT    /api/v1/element-templates/{id}               # 更新要素模板
DELETE /api/v1/element-templates/{id}               # 删除要素模板

# 按合同类型获取要素配置
GET    /api/v1/contract-types/{type}/elements       # 获取指定合同类型的所有要素
POST   /api/v1/contract-types/{type}/elements       # 为合同类型添加要素
PUT    /api/v1/contract-types/{type}/elements/{id}  # 更新要素配置

# 要素分类管理
GET    /api/v1/element-categories                   # 获取要素分类列表
POST   /api/v1/element-categories                   # 创建要素分类
PUT    /api/v1/element-categories/{id}              # 更新要素分类

# 数据类型管理
GET    /api/v1/element-data-types                   # 获取数据类型列表
POST   /api/v1/element-data-types                   # 创建数据类型
PUT    /api/v1/element-data-types/{id}              # 更新数据类型

# 要素提取测试
POST   /api/v1/element-extraction/test              # 测试要素提取规则
```

#### 4.1.5 审核相关API
```
POST /api/v1/contracts/{id}/audit    # 提交审核
GET  /api/v1/contracts/{id}/report   # 获取审核报告
GET  /api/v1/audits                  # 获取审核历史
POST /api/v1/audits/{id}/confirm     # 确认审核结果
```

### 4.2 API响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 5. AI算法设计

### 5.1 文本预处理流程
```
原始文本 → 清洗 → 分词 → 词性标注 → 命名实体识别 → 结构化数据
```

### 5.2 缺失条款检测算法
```python
import httpx
import json
from typing import List, Dict
from fastapi import HTTPException
import asyncio

class Qwen3APIClient:
    def __init__(self, api_key: str, base_url: str):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

    async def call_api(self, prompt: str) -> str:
        """异步调用Qwen3 API"""
        payload = {
            'model': 'qwen3',
            'messages': [{'role': 'user', 'content': prompt}],
            'temperature': 0.1,
            'max_tokens': 2000
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f'{self.base_url}/chat/completions',
                headers=self.headers,
                json=payload
            )

            if response.status_code == 200:
                return response.json()['choices'][0]['message']['content']
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"API调用失败: {response.status_code}"
                )

async def detect_missing_clauses(contract_text: str, contract_type: str) -> List[Dict]:
    """
    基于Qwen3 API的缺失条款检测算法
    """
    # 1. 获取标准条款模板
    standard_clauses = await get_standard_clauses(contract_type)

    # 2. 构建检测Prompt
    prompt = f"""
    请分析以下{contract_type}合同，检查是否包含以下标准条款：
    标准条款列表：{standard_clauses}

    合同内容：{contract_text}

    请返回缺失的条款列表，格式为JSON数组：
    [
        {{
            "clause_name": "条款名称",
            "importance": "重要程度(高/中/低)",
            "description": "条款描述"
        }}
    ]
    """

    # 3. 异步调用Qwen3 API
    client = Qwen3APIClient(
        api_key=settings.QWEN3_API_KEY,
        base_url=settings.QWEN3_BASE_URL
    )
    response = await client.call_api(prompt)
    missing_clauses = parse_json_response(response)

    return missing_clauses
```

### 5.3 风险识别算法
```python
async def identify_risks(contract_text: str) -> List[Dict]:
    """
    基于Qwen3 API的风险识别算法
    """
    risks = []

    # 1. 规则引擎检测
    rule_risks = await rule_engine.detect(contract_text)

    # 2. Qwen3 API分析
    risk_prompt = f"""
    请分析以下合同中的潜在风险点，包括法律风险、商业风险、财务风险和操作风险：

    合同内容：{contract_text}

    请按以下JSON格式返回风险分析结果：
    {{
        "risks": [
            {{
                "type": "风险类型",
                "level": "风险等级(高/中/低)",
                "description": "风险描述",
                "location": "风险位置",
                "suggestion": "建议措施"
            }}
        ]
    }}
    """

    # 3. 异步调用API并处理响应
    try:
        client = Qwen3APIClient(
            api_key=settings.QWEN3_API_KEY,
            base_url=settings.QWEN3_BASE_URL
        )
        api_response = await client.call_api(risk_prompt)
        llm_risks = parse_json_response(api_response)

        # 4. 结果融合和去重
        risks = merge_and_deduplicate_risks(rule_risks, llm_risks)

    except Exception as e:
        logger.error(f"Qwen3 API调用失败: {e}")
        # 降级到仅使用规则引擎
        risks = rule_risks

    return risks
```

## 6. 安全设计

### 6.1 认证与授权
- **JWT令牌**：无状态认证机制
- **RBAC权限**：基于角色的访问控制
- **API限流**：防止恶意请求
- **HTTPS加密**：数据传输加密

### 6.2 数据安全
- **敏感数据加密**：AES-256加密存储
- **数据脱敏**：日志中敏感信息脱敏
- **访问审计**：完整的操作日志
- **数据备份**：定期备份和恢复测试

### 6.3 系统安全
- **输入验证**：防止SQL注入、XSS攻击
- **文件上传安全**：文件类型和大小限制
- **错误处理**：避免敏感信息泄露
- **安全头设置**：CSP、HSTS等安全头

## 7. 性能优化

### 7.1 缓存策略
- **Redis缓存**：热点数据缓存
- **CDN加速**：静态资源分发
- **数据库缓存**：查询结果缓存
- **应用缓存**：业务数据缓存

### 7.2 数据库优化
- **索引优化**：合理创建索引
- **查询优化**：SQL语句优化
- **分库分表**：大数据量处理
- **读写分离**：提高并发能力

### 7.3 系统优化
- **异步处理**：耗时操作异步化
- **连接池**：数据库连接池
- **负载均衡**：多实例部署
- **监控告警**：性能监控和告警

## 8. 部署架构

### 8.1 容器化部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  frontend:
    image: contract-audit-vue-frontend:latest
    ports:
      - "80:80"
  
  backend:
    image: contract-audit-python-backend:latest
    ports:
      - "8000:8000"
    depends_on:
      - database
      - redis
    environment:
      - PYTHONPATH=/app
  
  database:
    image: postgres:13
    environment:
      POSTGRES_DB: contract_audit
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password
  
  redis:
    image: redis:6-alpine
```

### 8.2 Kubernetes部署
- **Pod管理**：应用容器编排
- **Service发现**：服务间通信
- **配置管理**：ConfigMap和Secret
- **自动扩缩容**：HPA水平扩展

## 9. 监控与运维

### 9.1 监控指标
- **系统指标**：CPU、内存、磁盘、网络
- **应用指标**：响应时间、吞吐量、错误率
- **业务指标**：审核成功率、用户活跃度

### 9.2 日志管理
- **结构化日志**：JSON格式日志
- **日志聚合**：ELK Stack
- **日志分析**：Kibana可视化
- **告警机制**：异常日志告警

### 9.3 运维自动化
- **CI/CD流水线**：自动化部署
- **健康检查**：服务健康监控
- **故障恢复**：自动故障转移
- **备份恢复**：数据备份策略
