# AI合同审核系统 - 数据库设计文档

## 1. 数据库设计概述

### 1.1 设计原则
- **配置化优先**：支持完全配置化的要素管理，无需修改代码
- **扩展性设计**：支持新合同类型和要素的快速配置
- **性能优化**：合理的索引设计和查询优化
- **数据完整性**：完整的约束和关系设计
- **版本控制**：支持配置的版本管理和变更追踪

### 1.2 核心表结构
系统包含10个核心数据库表，支持完整的配置化要素管理：

```
1. contract_types                    - 合同类型管理
2. contract_type_recognition         - 合同类型识别配置
3. element_categories               - 要素分类管理
4. contract_element_templates       - 要素模板配置
5. element_extraction_rules         - 要素提取规则
6. element_validation_rules         - 要素验证规则
7. element_relationships           - 要素关系配置
8. contract_element_instances      - 要素实例数据
9. element_extraction_logs         - 提取日志
10. config_change_logs             - 配置变更日志
```

## 2. 详细表结构设计

### 2.1 合同类型管理表 (contract_types)
```sql
CREATE TABLE contract_types (
    id SERIAL PRIMARY KEY,
    type_code VARCHAR(50) UNIQUE NOT NULL,           -- 类型代码
    type_name VARCHAR(100) NOT NULL,                 -- 类型名称
    type_description TEXT,                           -- 类型描述
    parent_type_id INTEGER REFERENCES contract_types(id), -- 支持类型继承
    is_active BOOLEAN DEFAULT TRUE,                  -- 是否启用
    sort_order INTEGER DEFAULT 0,                   -- 排序
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);
```

**设计说明**：
- 支持合同类型的层级管理（parent_type_id）
- 类型代码唯一，便于程序引用
- 软删除设计（is_active字段）

### 2.2 合同类型识别配置表 (contract_type_recognition)
```sql
CREATE TABLE contract_type_recognition (
    id SERIAL PRIMARY KEY,
    contract_type_id INTEGER NOT NULL REFERENCES contract_types(id),
    recognition_type VARCHAR(20) NOT NULL,           -- 'keyword', 'pattern', 'ai_prompt'
    recognition_config JSONB NOT NULL,               -- 识别配置
    weight DECIMAL(3,2) DEFAULT 1.0,                -- 权重
    confidence_threshold DECIMAL(3,2) DEFAULT 0.7,  -- 置信度阈值
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**：
- 支持多种识别方式：关键词、正则模式、AI提示词
- JSONB字段存储灵活的配置信息
- 权重和置信度支持智能匹配

### 2.3 要素分类表 (element_categories)
```sql
CREATE TABLE element_categories (
    id SERIAL PRIMARY KEY,
    category_code VARCHAR(100) UNIQUE NOT NULL,      -- 分类代码
    category_name VARCHAR(200) NOT NULL,             -- 分类名称
    category_description TEXT,                       -- 分类描述
    parent_category_id INTEGER REFERENCES element_categories(id), -- 父分类
    category_level INTEGER DEFAULT 1,               -- 分类层级
    category_path VARCHAR(500),                      -- 分类路径
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);
```

**设计说明**：
- 支持无限层级的分类结构
- category_path字段便于快速查询和显示
- 支持分类的排序和状态管理