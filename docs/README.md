# AI合同审核系统 - 文档中心

## 📚 文档导航

### 需求文档 (requirements/)
- [业务需求](requirements/business_requirements.md) - 业务功能需求和用户故事
- [技术需求](requirements/technical_requirements.md) - 技术架构和性能要求
- [用户故事](requirements/user_stories.md) - 用户使用场景和需求

### 设计文档 (design/)
- [系统架构](design/system_architecture.md) - 整体系统架构设计
- [数据库设计](design/database_design.md) - 数据模型和数据库结构
- [API设计](design/api_design.md) - RESTful API接口设计
- [UI设计](design/ui_design.md) - 用户界面设计规范

### 开发文档 (development/)
- [环境搭建](development/setup_guide.md) - 开发环境配置指南
- [编码规范](development/coding_standards.md) - 代码风格和开发规范
- [测试指南](development/testing_guide.md) - 测试策略和测试用例
- [部署指南](development/deployment_guide.md) - 部署流程和配置

### 用户手册 (user_manual/)
- [安装说明](user_manual/installation.md) - 系统安装和配置
- [使用指南](user_manual/user_guide.md) - 功能使用说明
- [API参考](user_manual/api_reference.md) - API接口文档

### 项目管理 (project_management/)
- [项目计划](project_management/project_plan.md) - 项目规划和里程碑
- [进度报告](project_management/progress_reports.md) - 开发进度和状态
- [技术验证](project_management/technical_validation.md) - 技术验证报告

## 📖 快速开始

1. **新用户**: 从[安装说明](user_manual/installation.md)开始
2. **开发者**: 查看[环境搭建](development/setup_guide.md)
3. **项目经理**: 参考[项目计划](project_management/project_plan.md)
4. **系统管理员**: 阅读[部署指南](development/deployment_guide.md)

## 🔄 文档更新

- 文档版本: v1.0
- 最后更新: 2025-01-01
- 维护者: AI合同审核系统开发团队

## 📝 贡献指南

如需更新文档，请遵循以下规范：
1. 使用Markdown格式
2. 保持文档结构清晰
3. 及时更新版本信息
4. 添加必要的示例和截图