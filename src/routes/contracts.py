"""
合同管理API路由
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Depends
from typing import List, Optional
from pydantic import BaseModel
import logging
import uuid
from datetime import datetime
import os
import aiofiles

router = APIRouter()
logger = logging.getLogger(__name__)

# 数据模型
class DocumentInfo(BaseModel):
    id: str
    filename: str
    size: int
    status: str  # uploading, processing, completed, error
    uploadTime: str
    processTime: Optional[str] = None

class BatchRequest(BaseModel):
    ids: List[str]

# 模拟数据存储（实际项目中应使用数据库）
documents_db = {}

@router.post("/upload")
async def upload_contract(file: UploadFile = File(...)):
    """上传合同文件"""
    try:
        # 验证文件类型
        allowed_extensions = ('.doc', '.docx', '.pdf')
        if not file.filename.lower().endswith(allowed_extensions):
            raise HTTPException(status_code=400, detail="仅支持 Word 文档和 PDF 格式")
        
        # 验证文件大小 (10MB)
        content = await file.read()
        if len(content) > 10 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="文件大小不能超过 10MB")
        
        # 生成文件ID和保存路径
        file_id = str(uuid.uuid4())
        upload_dir = "uploads"
        os.makedirs(upload_dir, exist_ok=True)
        file_path = os.path.join(upload_dir, f"{file_id}_{file.filename}")
        
        # 保存文件
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(content)
        
        # 保存文档信息到数据库
        doc_info = DocumentInfo(
            id=file_id,
            filename=file.filename,
            size=len(content),
            status="uploading",
            uploadTime=datetime.now().isoformat()
        )
        documents_db[file_id] = doc_info.dict()
        
        return {
            "success": True,
            "file_id": file_id,
            "filename": file.filename,
            "size": len(content),
            "message": "文件上传成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/list")
async def list_contracts():
    """获取合同列表"""
    try:
        # 添加一些示例数据（如果数据库为空）
        if not documents_db:
            sample_docs = [
                {
                    "id": "sample_1",
                    "filename": "销售合同模板.docx",
                    "size": 245760,
                    "status": "completed",
                    "uploadTime": "2025-01-01T09:30:00",
                    "processTime": "2025-01-01T09:32:15"
                },
                {
                    "id": "sample_2", 
                    "filename": "服务协议.pdf",
                    "size": 512000,
                    "status": "processing",
                    "uploadTime": "2025-01-01T10:15:00"
                },
                {
                    "id": "sample_3",
                    "filename": "采购合同.docx", 
                    "size": 189440,
                    "status": "error",
                    "uploadTime": "2025-01-01T08:45:00"
                }
            ]
            for doc in sample_docs:
                documents_db[doc["id"]] = doc
        
        contracts = list(documents_db.values())
        # 按上传时间倒序排列
        contracts.sort(key=lambda x: x["uploadTime"], reverse=True)
        
        return {
            "success": True,
            "contracts": contracts
        }
    except Exception as e:
        logger.error(f"获取合同列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{document_id}")
async def get_document_detail(document_id: str):
    """获取文档详情"""
    try:
        if document_id not in documents_db:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        doc = documents_db[document_id]
        
        # 模拟文档内容（实际项目中应从文件中读取）
        content = f"""
合同文档：{doc['filename']}

这是一个示例合同文档的内容预览。

主要条款：
1. 合同双方：甲方和乙方
2. 合同标的：商品或服务
3. 合同金额：人民币 XXX 元
4. 履行期限：XXXX年XX月XX日至XXXX年XX月XX日
5. 违约责任：按照相关法律法规执行

注：这是模拟内容，实际项目中会解析真实文档内容。
        """.strip()
        
        return {
            "success": True,
            "document": doc,
            "content": content
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{document_id}")
async def delete_document(document_id: str):
    """删除文档"""
    try:
        if document_id not in documents_db:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        # 删除文件（如果存在）
        doc = documents_db[document_id]
        file_path = f"uploads/{document_id}_{doc['filename']}"
        if os.path.exists(file_path):
            os.remove(file_path)
        
        # 从数据库删除
        del documents_db[document_id]
        
        return {
            "success": True,
            "message": "文档删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文档失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/batch-delete")
async def batch_delete_documents(request: BatchRequest):
    """批量删除文档"""
    try:
        deleted_count = 0
        errors = []
        
        for doc_id in request.ids:
            try:
                if doc_id in documents_db:
                    # 删除文件
                    doc = documents_db[doc_id]
                    file_path = f"uploads/{doc_id}_{doc['filename']}"
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    
                    # 从数据库删除
                    del documents_db[doc_id]
                    deleted_count += 1
                else:
                    errors.append(f"文档 {doc_id} 不存在")
            except Exception as e:
                errors.append(f"删除文档 {doc_id} 失败: {str(e)}")
        
        return {
            "success": True,
            "deleted_count": deleted_count,
            "errors": errors,
            "message": f"成功删除 {deleted_count} 个文档"
        }
    except Exception as e:
        logger.error(f"批量删除失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{document_id}/process")
async def process_document(document_id: str):
    """开始处理文档"""
    try:
        if document_id not in documents_db:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        doc = documents_db[document_id]
        
        # 更新状态为处理中
        doc["status"] = "processing"
        documents_db[document_id] = doc
        
        # 这里应该启动异步处理任务
        # 暂时模拟处理完成
        import asyncio
        asyncio.create_task(simulate_processing(document_id))
        
        return {
            "success": True,
            "message": "文档处理任务已启动"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动文档处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/batch-process")
async def batch_process_documents(request: BatchRequest):
    """批量处理文档"""
    try:
        processed_count = 0
        errors = []
        
        for doc_id in request.ids:
            try:
                if doc_id in documents_db:
                    doc = documents_db[doc_id]
                    doc["status"] = "processing"
                    documents_db[doc_id] = doc
                    
                    # 启动异步处理
                    import asyncio
                    asyncio.create_task(simulate_processing(doc_id))
                    processed_count += 1
                else:
                    errors.append(f"文档 {doc_id} 不存在")
            except Exception as e:
                errors.append(f"处理文档 {doc_id} 失败: {str(e)}")
        
        return {
            "success": True,
            "processed_count": processed_count,
            "errors": errors,
            "message": f"成功启动 {processed_count} 个文档的处理任务"
        }
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def simulate_processing(document_id: str):
    """模拟文档处理过程"""
    import asyncio
    import random
    
    # 模拟处理时间 (5-15秒)
    await asyncio.sleep(random.randint(5, 15))
    
    if document_id in documents_db:
        doc = documents_db[document_id]
        # 90% 成功率
        if random.random() < 0.9:
            doc["status"] = "completed"
            doc["processTime"] = datetime.now().isoformat()
        else:
            doc["status"] = "error"
        
        documents_db[document_id] = doc
