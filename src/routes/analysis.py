"""
合同分析API路由
"""

from fastapi import APIRouter, HTTPException, UploadFile, File
from typing import Optional
import logging

from src.clause_detection.detector import ClauseDetectionEngine
from src.risk_analysis.analyzer import RiskAnalyzer
from src.core.ai_client import QwenAPIClient

router = APIRouter()
logger = logging.getLogger(__name__)

# 初始化组件
qwen_client = QwenAPIClient()
clause_detector = ClauseDetectionEngine(qwen_client)
risk_analyzer = RiskAnalyzer(qwen_client)

@router.post("/clause-detection")
async def detect_clauses(
    contract_text: str,
    contract_type: Optional[str] = "general",
    enable_ai: bool = True
):
    """条款检测API"""
    try:
        detection_results, recommendations = await clause_detector.detect_clauses(
            contract_id="temp",
            contract_content=contract_text,
            contract_type=contract_type,
            enable_ai=enable_ai
        )
        
        return {
            "success": True,
            "detection_results": [
                {
                    "clause_id": r.clause_id,
                    "clause_name": r.clause_name,
                    "status": r.status,
                    "confidence": r.confidence,
                    "recommendations": r.recommendations
                }
                for r in detection_results
            ],
            "recommendations": [
                {
                    "clause_id": r.missing_clause_id,
                    "content": r.recommended_content,
                    "reason": r.reason,
                    "priority": r.priority
                }
                for r in recommendations
            ]
        }
    except Exception as e:
        logger.error(f"条款检测失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/risk-analysis")
async def analyze_risks(
    contract_text: str,
    contract_type: str = "general"
):
    """风险分析API"""
    try:
        result = await risk_analyzer.analyze_risks(
            contract_text=contract_text,
            contract_type=contract_type
        )
        
        return {
            "success": True,
            "result": {
                "overall_risk_level": result.risk_assessment.overall_risk_level,
                "total_risk_score": result.risk_assessment.total_risk_score,
                "risk_points": [
                    {
                        "risk_name": rp.risk_name,
                        "risk_level": rp.risk_level,
                        "risk_score": rp.risk_score,
                        "description": rp.description
                    }
                    for rp in result.risk_points
                ],
                "processing_time": result.processing_duration
            }
        }
    except Exception as e:
        logger.error(f"风险分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
