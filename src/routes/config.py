"""
配置管理API路由
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/element-templates")
async def get_element_templates():
    """获取要素模板配置"""
    # 模拟返回配置数据
    return {
        "success": True,
        "templates": [
            {
                "id": "party_template",
                "name": "当事方要素",
                "category": "party",
                "extraction_rules": {
                    "keywords": ["甲方", "乙方"],
                    "regex": r"[甲乙]方[：:]\s*([^\n]+)"
                }
            }
        ]
    }

@router.post("/element-templates")
async def create_element_template(template: Dict[str, Any]):
    """创建要素模板"""
    try:
        # 这里应该保存到数据库
        return {
            "success": True,
            "template_id": "new_template_id",
            "message": "模板创建成功"
        }
    except Exception as e:
        logger.error(f"创建模板失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
