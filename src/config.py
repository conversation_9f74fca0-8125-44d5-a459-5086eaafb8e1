"""
应用配置管理
"""

import os
from typing import List
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = "AI合同审核系统"
    app_version: str = "1.0.0"
    debug: bool = True
    environment: str = "development"
    
    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 1
    
    # 数据库配置
    database_url: str = "sqlite:///./data/contract_audit.db"
    database_echo: bool = False
    
    # Redis配置
    redis_url: str = "redis://localhost:6379/0"
    redis_password: str = ""
    
    # AI服务配置
    qwen_api_key: str = "your-qwen-api-key-here"
    qwen_api_base: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    qwen_model: str = "qwen-plus"
    qwen_max_tokens: int = 2000
    qwen_temperature: float = 0.7
    
    # 文件存储配置
    upload_dir: str = "./uploads"
    temp_dir: str = "./temp"
    max_file_size: int = 52428800  # 50MB
    allowed_extensions: List[str] = [".docx", ".doc"]
    
    # 安全配置
    secret_key: str = "your-super-secret-jwt-key-change-this-in-production"
    access_token_expire_minutes: int = 30
    algorithm: str = "HS256"
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "./logs/app.log"
    log_max_size: int = 10485760  # 10MB
    log_backup_count: int = 5
    
    # 缓存配置
    cache_ttl: int = 3600  # 1小时
    cache_max_size: int = 1000
    
    # API配置
    api_prefix: str = "/api/v1"
    cors_origins: List[str] = [
        "http://localhost:3000", 
        "http://localhost:8080", 
        "http://127.0.0.1:3000"
    ]
    
    # 业务配置
    min_confidence: float = 0.6
    max_concurrent_analyses: int = 10
    analysis_timeout: int = 300  # 5分钟
    
    # 开发配置
    enable_docs: bool = True
    enable_debug_toolbar: bool = True
    
    # 测试数据库
    test_database_url: str = "sqlite:///:memory:"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

# 全局配置实例
_settings = None

def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings