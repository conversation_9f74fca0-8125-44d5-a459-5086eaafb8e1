"""
要素提取模块 - 数据模型定义

定义要素提取相关的数据结构
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum

class ElementCategory(str, Enum):
    """要素分类"""
    PARTY = "party"           # 当事方
    FINANCIAL = "financial"   # 财务信息
    TEMPORAL = "temporal"     # 时间信息
    LEGAL = "legal"          # 法律条款
    BUSINESS = "business"    # 业务条款

class ElementTemplate(BaseModel):
    """要素模板"""
    id: str = Field(..., description="模板ID")
    name: str = Field(..., description="要素名称")
    category: ElementCategory = Field(..., description="要素分类")
    description: str = Field(..., description="要素描述")
    
    # 提取规则
    keywords: List[str] = Field(default=[], description="关键词列表")
    regex_patterns: List[str] = Field(default=[], description="正则表达式")
    
    # 验证规则
    required: bool = Field(False, description="是否必需")
    data_type: str = Field("string", description="数据类型")
    
    # 元数据
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    is_active: bool = Field(True, description="是否启用")

class ExtractionRule(BaseModel):
    """提取规则"""
    id: str = Field(..., description="规则ID")
    element_id: str = Field(..., description="要素ID")
    rule_type: str = Field(..., description="规则类型")
    pattern: str = Field(..., description="匹配模式")
    priority: int = Field(1, description="优先级")

class ValidationRule(BaseModel):
    """验证规则"""
    id: str = Field(..., description="规则ID")
    element_id: str = Field(..., description="要素ID")
    rule_type: str = Field(..., description="验证类型")
    parameters: Dict[str, Any] = Field(default={}, description="验证参数")

class ElementRelation(BaseModel):
    """要素关系"""
    id: str = Field(..., description="关系ID")
    source_element: str = Field(..., description="源要素")
    target_element: str = Field(..., description="目标要素")
    relation_type: str = Field(..., description="关系类型")

class ExtractedElement(BaseModel):
    """提取的要素"""
    element_id: str = Field(..., description="要素ID")
    element_name: str = Field(..., description="要素名称")
    value: str = Field(..., description="要素值")
    confidence: float = Field(..., ge=0.0, le=1.0, description="置信度")
    
    # 位置信息
    start_position: Optional[int] = Field(None, description="开始位置")
    end_position: Optional[int] = Field(None, description="结束位置")
    
    # 提取信息
    extraction_method: str = Field(..., description="提取方法")
    extracted_at: datetime = Field(default_factory=datetime.now, description="提取时间")