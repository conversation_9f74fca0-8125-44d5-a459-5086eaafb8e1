"""
要素提取模块

配置化的合同要素提取系统，支持：
- 多层级要素分类
- 灵活的提取规则配置
- 动态验证和关系管理
- 热更新和缓存机制

主要组件：
- models: 数据模型定义
- extractor: 要素提取引擎
- config_manager: 配置管理器
- validators: 验证器
"""

from .models import (
    ElementTemplate,
    ElementCategory, 
    ExtractionRule,
    ValidationRule,
    ElementRelation,
    ExtractedElement
)

from .extractor import ElementExtractor
from .config_manager import ElementConfigManager
from .validators import ElementValidator

__all__ = [
    # 数据模型
    "ElementTemplate",
    "ElementCategory",
    "ExtractionRule", 
    "ValidationRule",
    "ElementRelation",
    "ExtractedElement",
    
    # 核心组件
    "ElementExtractor",
    "ElementConfigManager", 
    "ElementValidator"
]