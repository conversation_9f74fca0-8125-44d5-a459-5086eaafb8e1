"""
数据验证工具函数
"""

import re
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, ValidationError
import logging

logger = logging.getLogger(__name__)

def validate_contract_content(content: str) -> Dict[str, Any]:
    """
    验证合同内容
    
    Args:
        content: 合同文本内容
        
    Returns:
        验证结果字典
    """
    result = {
        'is_valid': True,
        'errors': [],
        'warnings': [],
        'stats': {}
    }
    
    try:
        # 基本检查
        if not content or not content.strip():
            result['is_valid'] = False
            result['errors'].append('合同内容不能为空')
            return result
        
        # 长度检查
        content_length = len(content.strip())
        if content_length < 100:
            result['warnings'].append('合同内容过短，可能不完整')
        elif content_length > 100000:
            result['warnings'].append('合同内容过长，处理可能较慢')
        
        # 统计信息
        result['stats'] = {
            'character_count': content_length,
            'word_count': len(content.split()),
            'paragraph_count': len([p for p in content.split('\n') if p.strip()])
        }
        
        # 基本格式检查
        if not re.search(r'[甲乙丙丁]方', content):
            result['warnings'].append('未检测到标准的当事方表述（甲方、乙方等）')
        
        if not re.search(r'合同|协议', content):
            result['warnings'].append('文档可能不是标准合同格式')
        
    except Exception as e:
        logger.error(f"验证合同内容失败: {e}")
        result['is_valid'] = False
        result['errors'].append(f'验证过程出错: {str(e)}')
    
    return result

def validate_element_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证要素配置
    
    Args:
        config: 要素配置字典
        
    Returns:
        验证结果
    """
    result = {
        'is_valid': True,
        'errors': [],
        'warnings': []
    }
    
    try:
        # 必需字段检查
        required_fields = ['element_name', 'element_type', 'extraction_rules']
        for field in required_fields:
            if field not in config:
                result['is_valid'] = False
                result['errors'].append(f'缺少必需字段: {field}')
        
        # 字段类型检查
        if 'element_name' in config and not isinstance(config['element_name'], str):
            result['is_valid'] = False
            result['errors'].append('element_name 必须是字符串')
        
        if 'extraction_rules' in config and not isinstance(config['extraction_rules'], list):
            result['is_valid'] = False
            result['errors'].append('extraction_rules 必须是列表')
        
        # 规则有效性检查
        if 'extraction_rules' in config:
            for i, rule in enumerate(config['extraction_rules']):
                if not isinstance(rule, dict):
                    result['errors'].append(f'提取规则 {i} 必须是字典格式')
                    continue
                
                if 'rule_type' not in rule:
                    result['errors'].append(f'提取规则 {i} 缺少 rule_type 字段')
                
                if rule.get('rule_type') == 'regex' and 'pattern' not in rule:
                    result['errors'].append(f'正则规则 {i} 缺少 pattern 字段')
        
    except Exception as e:
        logger.error(f"验证要素配置失败: {e}")
        result['is_valid'] = False
        result['errors'].append(f'验证过程出错: {str(e)}')
    
    return result

def validate_api_request(data: Dict[str, Any], required_fields: List[str]) -> Dict[str, Any]:
    """
    验证API请求数据
    
    Args:
        data: 请求数据
        required_fields: 必需字段列表
        
    Returns:
        验证结果
    """
    result = {
        'is_valid': True,
        'errors': [],
        'warnings': []
    }
    
    try:
        # 检查必需字段
        for field in required_fields:
            if field not in data:
                result['is_valid'] = False
                result['errors'].append(f'缺少必需字段: {field}')
            elif data[field] is None or data[field] == '':
                result['is_valid'] = False
                result['errors'].append(f'字段 {field} 不能为空')
        
        # 数据类型基本检查
        for key, value in data.items():
            if isinstance(value, str) and len(value.strip()) == 0:
                result['warnings'].append(f'字段 {key} 为空字符串')
        
    except Exception as e:
        logger.error(f"验证API请求失败: {e}")
        result['is_valid'] = False
        result['errors'].append(f'验证过程出错: {str(e)}')
    
    return result