"""
文件处理工具函数
"""

import os
import hashlib
import shutil
import tempfile
from pathlib import Path
from typing import Optional, List
import logging

logger = logging.getLogger(__name__)

ALLOWED_EXTENSIONS = {'.docx', '.doc', '.pdf', '.txt'}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

def save_uploaded_file(file_content: bytes, filename: str, upload_dir: str = "./uploads") -> str:
    """
    保存上传的文件
    
    Args:
        file_content: 文件内容
        filename: 文件名
        upload_dir: 上传目录
        
    Returns:
        保存的文件路径
    """
    try:
        # 确保上传目录存在
        Path(upload_dir).mkdir(parents=True, exist_ok=True)
        
        # 生成唯一文件名
        file_hash = get_file_hash(file_content)
        file_ext = Path(filename).suffix
        unique_filename = f"{file_hash}{file_ext}"
        
        file_path = Path(upload_dir) / unique_filename
        
        # 保存文件
        with open(file_path, 'wb') as f:
            f.write(file_content)
        
        logger.info(f"文件保存成功: {file_path}")
        return str(file_path)
        
    except Exception as e:
        logger.error(f"保存文件失败: {e}")
        raise

def validate_file_type(filename: str) -> bool:
    """
    验证文件类型
    
    Args:
        filename: 文件名
        
    Returns:
        是否为允许的文件类型
    """
    file_ext = Path(filename).suffix.lower()
    return file_ext in ALLOWED_EXTENSIONS

def get_file_hash(file_content: bytes) -> str:
    """
    计算文件哈希值
    
    Args:
        file_content: 文件内容
        
    Returns:
        文件的MD5哈希值
    """
    return hashlib.md5(file_content).hexdigest()

def cleanup_temp_files(temp_dir: str = "./temp", max_age_hours: int = 24) -> None:
    """
    清理临时文件
    
    Args:
        temp_dir: 临时文件目录
        max_age_hours: 文件最大保留时间（小时）
    """
    try:
        temp_path = Path(temp_dir)
        if not temp_path.exists():
            return
        
        import time
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        for file_path in temp_path.iterdir():
            if file_path.is_file():
                file_age = current_time - file_path.stat().st_mtime
                if file_age > max_age_seconds:
                    file_path.unlink()
                    logger.info(f"清理临时文件: {file_path}")
                    
    except Exception as e:
        logger.error(f"清理临时文件失败: {e}")