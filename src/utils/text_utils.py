"""
文本处理工具函数
"""

import re
import jieba
from typing import List, Dict, Optional
import logging

# 可选依赖
try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    import numpy as np
    HAS_SKLEARN = True
except ImportError:
    HAS_SKLEARN = False

logger = logging.getLogger(__name__)

def clean_text(text: str) -> str:
    """
    清理文本内容
    
    Args:
        text: 原始文本
        
    Returns:
        清理后的文本
    """
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    # 移除特殊字符（保留中文、英文、数字、基本标点）
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()（）【】""''、。，；：！？]', '', text)
    
    # 去除首尾空白
    text = text.strip()
    
    return text

def extract_keywords(text: str, top_k: int = 10) -> List[str]:
    """
    提取文本关键词
    
    Args:
        text: 输入文本
        top_k: 返回关键词数量
        
    Returns:
        关键词列表
    """
    try:
        # 使用jieba进行分词
        words = jieba.lcut(text)
        
        # 过滤停用词和短词
        stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
        filtered_words = [word for word in words if len(word) > 1 and word not in stop_words]
        
        # 统计词频
        word_freq = {}
        for word in filtered_words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # 按频率排序
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        
        return [word for word, freq in sorted_words[:top_k]]
        
    except Exception as e:
        logger.error(f"提取关键词失败: {e}")
        return []

def calculate_similarity(text1: str, text2: str) -> float:
    """
    计算两个文本的相似度
    
    Args:
        text1: 文本1
        text2: 文本2
        
    Returns:
        相似度分数 (0-1)
    """
    try:
        if not text1 or not text2:
            return 0.0
        
        if HAS_SKLEARN:
            # 使用TF-IDF向量化
            vectorizer = TfidfVectorizer()
            tfidf_matrix = vectorizer.fit_transform([text1, text2])
            
            # 计算余弦相似度
            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
            
            return float(similarity)
        else:
            # 简单的基于词汇重叠的相似度计算
            words1 = set(jieba.lcut(text1.lower()))
            words2 = set(jieba.lcut(text2.lower()))
            
            if not words1 or not words2:
                return 0.0
            
            intersection = words1.intersection(words2)
            union = words1.union(words2)
            
            return len(intersection) / len(union) if union else 0.0
        
    except Exception as e:
        logger.error(f"计算相似度失败: {e}")
        return 0.0

def segment_text(text: str, max_length: int = 1000) -> List[str]:
    """
    将长文本分段
    
    Args:
        text: 输入文本
        max_length: 每段最大长度
        
    Returns:
        文本段落列表
    """
    if not text or len(text) <= max_length:
        return [text] if text else []
    
    segments = []
    current_pos = 0
    
    while current_pos < len(text):
        # 找到合适的分割点（优先在句号、换行符处分割）
        end_pos = current_pos + max_length
        
        if end_pos >= len(text):
            segments.append(text[current_pos:])
            break
        
        # 寻找最近的句号或换行符
        best_split = end_pos
        for i in range(end_pos, current_pos, -1):
            if text[i] in '。\n':
                best_split = i + 1
                break
        
        segments.append(text[current_pos:best_split])
        current_pos = best_split
    
    return segments