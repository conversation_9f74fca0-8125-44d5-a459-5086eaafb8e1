"""
标准条款库管理系统
负责标准条款的存储、管理和查询功能
"""

import json
import logging
from typing import List, Dict, Optional, Any
from pathlib import Path
from datetime import datetime

from .models import (
    StandardClause, ClauseCategory, ClauseImportance, 
    MatchMethod, ClauseLibraryStats
)

def datetime_serializer(obj):
    """JSON 序列化时处理 datetime 对象"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

logger = logging.getLogger(__name__)

class ClauseLibrary:
    """标准条款库管理器"""
    
    def __init__(self, data_file: str = "clause_detection/data/standard_clauses.json"):
        self.data_file = Path(data_file)
        self.clauses: Dict[int, StandardClause] = {}
        self.next_id = 1
        
        # 确保数据目录存在
        self.data_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 加载现有数据
        self.load_clauses()
        
        # 如果没有数据，初始化默认条款
        if not self.clauses:
            self._initialize_default_clauses()
    
    def load_clauses(self) -> None:
        """从文件加载条款数据"""
        try:
            if self.data_file.exists():
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.clauses = {}
                for clause_data in data.get('clauses', []):
                    clause = StandardClause(**clause_data)
                    self.clauses[clause.id] = clause
                
                self.next_id = data.get('next_id', 1)
                logger.info(f"加载了 {len(self.clauses)} 个标准条款")
            else:
                logger.info("条款库文件不存在，将创建新的条款库")
        except Exception as e:
            logger.error(f"加载条款库失败: {e}")
            self.clauses = {}
            self.next_id = 1
    
    def save_clauses(self) -> None:
        """保存条款数据到文件"""
        try:
            data = {
                'clauses': [clause.dict() for clause in self.clauses.values()],
                'next_id': self.next_id,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=datetime_serializer)
            
            logger.info(f"保存了 {len(self.clauses)} 个标准条款")
        except Exception as e:
            logger.error(f"保存条款库失败: {e}")
    
    def add_clause(self, clause: StandardClause) -> int:
        """添加新条款"""
        if clause.id is None:
            clause.id = self.next_id
            self.next_id += 1
        
        clause.created_at = datetime.now()
        clause.updated_at = datetime.now()
        
        self.clauses[clause.id] = clause
        self.save_clauses()
        
        logger.info(f"添加新条款: {clause.clause_name} (ID: {clause.id})")
        return clause.id
    
    def update_clause(self, clause_id: int, clause: StandardClause) -> bool:
        """更新条款"""
        if clause_id not in self.clauses:
            return False
        
        clause.id = clause_id
        clause.updated_at = datetime.now()
        
        self.clauses[clause_id] = clause
        self.save_clauses()
        
        logger.info(f"更新条款: {clause.clause_name} (ID: {clause_id})")
        return True
    
    def delete_clause(self, clause_id: int) -> bool:
        """删除条款"""
        if clause_id not in self.clauses:
            return False
        
        clause_name = self.clauses[clause_id].clause_name
        del self.clauses[clause_id]
        self.save_clauses()
        
        logger.info(f"删除条款: {clause_name} (ID: {clause_id})")
        return True
    
    def get_clause(self, clause_id: int) -> Optional[StandardClause]:
        """获取单个条款"""
        return self.clauses.get(clause_id)
    
    def get_clauses_by_contract_type(self, contract_type: str) -> List[StandardClause]:
        """根据合同类型获取条款"""
        return [
            clause for clause in self.clauses.values()
            if contract_type in clause.contract_types
        ]
    
    def get_clauses_by_category(self, category: ClauseCategory) -> List[StandardClause]:
        """根据分类获取条款"""
        return [
            clause for clause in self.clauses.values()
            if clause.clause_category == category
        ]
    
    def get_clauses_by_importance(self, importance: ClauseImportance) -> List[StandardClause]:
        """根据重要性获取条款"""
        return [
            clause for clause in self.clauses.values()
            if clause.importance_level == importance
        ]
    
    def search_clauses(self, keyword: str) -> List[StandardClause]:
        """搜索条款"""
        keyword_lower = keyword.lower()
        results = []
        
        for clause in self.clauses.values():
            # 搜索条款名称、描述、标准文本
            if (keyword_lower in clause.clause_name.lower() or
                keyword_lower in clause.description.lower() or
                keyword_lower in clause.standard_text.lower() or
                any(keyword_lower in kw.lower() for kw in clause.keywords)):
                results.append(clause)
        
        return results
    
    def get_statistics(self) -> ClauseLibraryStats:
        """获取条款库统计信息"""
        total_clauses = len(self.clauses)
        
        # 按分类统计
        clauses_by_category = {}
        for category in ClauseCategory:
            count = len(self.get_clauses_by_category(category))
            if count > 0:
                clauses_by_category[category.value] = count
        
        # 按重要性统计
        clauses_by_importance = {}
        for importance in ClauseImportance:
            count = len(self.get_clauses_by_importance(importance))
            if count > 0:
                clauses_by_importance[importance.value] = count
        
        # 按合同类型统计
        clauses_by_contract_type = {}
        for clause in self.clauses.values():
            for contract_type in clause.contract_types:
                clauses_by_contract_type[contract_type] = clauses_by_contract_type.get(contract_type, 0) + 1
        
        return ClauseLibraryStats(
            total_clauses=total_clauses,
            clauses_by_category=clauses_by_category,
            clauses_by_importance=clauses_by_importance,
            clauses_by_contract_type=clauses_by_contract_type,
            last_updated=datetime.now(),
            version="1.0"
        )
    
    def _initialize_default_clauses(self) -> None:
        """初始化默认标准条款"""
        logger.info("初始化默认标准条款...")
        
        default_clauses = [
            # 基础信息条款
            StandardClause(
                clause_name="合同当事方",
                clause_category=ClauseCategory.BASIC_INFO,
                contract_types=["sales", "service", "lease", "employment", "general"],
                importance_level=ClauseImportance.CRITICAL,
                standard_text="本合同由以下当事方签署：甲方（委托方）：[公司名称]，乙方（受托方）：[公司名称]",
                description="明确合同双方的身份信息",
                purpose="确定合同主体，明确权利义务承担者",
                keywords=["甲方", "乙方", "当事方", "委托方", "受托方"],
                semantic_keywords=["合同主体", "签约方", "合同双方"],
                recommended_text="甲方：[公司全称]\n地址：[详细地址]\n法定代表人：[姓名]\n联系电话：[电话号码]\n\n乙方：[公司全称]\n地址：[详细地址]\n法定代表人：[姓名]\n联系电话：[电话号码]"
            ),
            
            # 付款条款
            StandardClause(
                clause_name="付款方式",
                clause_category=ClauseCategory.PAYMENT_TERMS,
                contract_types=["sales", "service", "lease"],
                importance_level=ClauseImportance.CRITICAL,
                standard_text="甲方应按照约定的付款方式和期限向乙方支付合同款项",
                description="规定付款的方式、时间和条件",
                purpose="确保款项按时足额支付，避免付款纠纷",
                keywords=["付款", "支付", "款项", "费用", "价款"],
                regex_patterns=[r"付款.*?方式", r"支付.*?期限", r".*?元.*?支付"],
                semantic_keywords=["付款条件", "支付安排", "结算方式"],
                recommended_text="付款方式：[银行转账/现金/支票等]\n付款期限：[具体时间]\n付款账户：[银行账户信息]\n逾期付款责任：[违约金或利息计算方式]"
            ),
            
            # 违约责任条款
            StandardClause(
                clause_name="违约责任",
                clause_category=ClauseCategory.LIABILITY,
                contract_types=["sales", "service", "lease", "employment", "general"],
                importance_level=ClauseImportance.CRITICAL,
                standard_text="任何一方违反本合同约定的，应承担相应的违约责任",
                description="规定违约行为及其法律后果",
                purpose="约束当事方履行合同义务，保护守约方利益",
                keywords=["违约", "责任", "赔偿", "损失", "违约金"],
                semantic_keywords=["违约后果", "法律责任", "损害赔偿"],
                recommended_text="1. 违约方应向守约方支付违约金，违约金为合同总金额的[百分比]%\n2. 违约金不足以弥补损失的，违约方还应赔偿超出部分\n3. 因违约导致合同解除的，违约方还应承担相应的损害赔偿责任"
            ),
            
            # 争议解决条款
            StandardClause(
                clause_name="争议解决",
                clause_category=ClauseCategory.DISPUTE_RESOLUTION,
                contract_types=["sales", "service", "lease", "employment", "general"],
                importance_level=ClauseImportance.IMPORTANT,
                standard_text="因本合同引起的争议，双方应协商解决；协商不成的，可向有管辖权的人民法院起诉",
                description="规定解决合同争议的方式和程序",
                purpose="为争议解决提供明确的途径和程序",
                keywords=["争议", "纠纷", "仲裁", "诉讼", "协商"],
                semantic_keywords=["争议处理", "纠纷解决", "法律途径"],
                recommended_text="1. 双方应首先通过友好协商解决争议\n2. 协商不成的，可以向[仲裁委员会]申请仲裁\n3. 也可以直接向[法院名称]提起诉讼\n4. 适用中华人民共和国法律"
            ),
            
            # 合同生效条款
            StandardClause(
                clause_name="合同生效",
                clause_category=ClauseCategory.BASIC_INFO,
                contract_types=["sales", "service", "lease", "employment", "general"],
                importance_level=ClauseImportance.IMPORTANT,
                standard_text="本合同自双方签字盖章之日起生效",
                description="规定合同生效的条件和时间",
                purpose="明确合同何时开始产生法律效力",
                keywords=["生效", "签字", "盖章", "签署"],
                semantic_keywords=["合同效力", "生效条件", "签约生效"],
                recommended_text="本合同自双方法定代表人或授权代表签字并加盖公章之日起生效，有效期至[具体日期]"
            ),
            
            # 保密条款
            StandardClause(
                clause_name="保密义务",
                clause_category=ClauseCategory.CONFIDENTIALITY,
                contract_types=["service", "employment", "general"],
                importance_level=ClauseImportance.IMPORTANT,
                standard_text="双方应对在合同履行过程中知悉的对方商业秘密承担保密义务",
                description="规定保密信息的范围和保密义务",
                purpose="保护商业秘密和敏感信息不被泄露",
                keywords=["保密", "商业秘密", "机密", "泄露"],
                semantic_keywords=["保密责任", "信息保护", "商业机密"],
                recommended_text="1. 保密信息包括但不限于：技术资料、商业计划、客户信息等\n2. 保密期限：[具体期限]\n3. 违反保密义务的，应承担相应的法律责任和经济赔偿"
            ),
            
            # 不可抗力条款
            StandardClause(
                clause_name="不可抗力",
                clause_category=ClauseCategory.FORCE_MAJEURE,
                contract_types=["sales", "service", "lease", "general"],
                importance_level=ClauseImportance.OPTIONAL,
                standard_text="因不可抗力导致合同无法履行的，受影响方不承担违约责任",
                description="规定不可抗力事件的处理方式",
                purpose="为不可预见的重大事件提供免责条款",
                keywords=["不可抗力", "天灾", "战争", "政府行为"],
                semantic_keywords=["免责事由", "意外事件", "客观原因"],
                recommended_text="1. 不可抗力包括：自然灾害、战争、政府行为、法律变更等\n2. 发生不可抗力应及时通知对方并提供证明\n3. 可根据影响程度延期履行、部分履行或解除合同"
            ),
            
            # 知识产权条款（服务合同专用）
            StandardClause(
                clause_name="知识产权",
                clause_category=ClauseCategory.INTELLECTUAL_PROPERTY,
                contract_types=["service"],
                importance_level=ClauseImportance.IMPORTANT,
                standard_text="合同履行过程中产生的知识产权归属应予明确约定",
                description="规定知识产权的归属和使用权",
                purpose="避免知识产权纠纷，保护各方合法权益",
                keywords=["知识产权", "专利", "著作权", "商标"],
                semantic_keywords=["产权归属", "版权", "专有权利"],
                recommended_text="1. 乙方在履行本合同过程中开发的技术成果，知识产权归[甲方/乙方/双方共有]\n2. 使用第三方知识产权的，应确保合法授权\n3. 侵犯第三方知识产权的责任由[责任方]承担"
            )
        ]
        
        # 添加所有默认条款
        for clause in default_clauses:
            self.add_clause(clause)
        
        logger.info(f"初始化完成，添加了 {len(default_clauses)} 个默认条款")

# 条款库管理器类（别名）
ClauseLibraryManager = ClauseLibrary

# 全局条款库实例
clause_library = ClauseLibrary()