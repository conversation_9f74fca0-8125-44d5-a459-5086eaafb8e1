"""
语义匹配器 - 基于千问3 API的智能条款匹配
实现语义理解的条款匹配算法
"""

import asyncio
import logging
import re
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime
import json

from .models import (
    StandardClause, ClauseMatch, MatchMethod, 
    ClauseCategory, ClauseImportance
)

class SemanticMatchResult:
    """语义匹配结果类"""
    def __init__(self, matches: List[ClauseMatch], processing_time: float = 0.0):
        self.matches = matches
        self.processing_time = processing_time
        self.total_matches = len(matches)
        self.high_confidence_matches = [m for m in matches if m.confidence > 0.8]
        
    def get_best_match(self) -> Optional[ClauseMatch]:
        """获取最佳匹配"""
        if not self.matches:
            return None
        return max(self.matches, key=lambda x: x.match_score)
    
    def get_matches_by_clause(self, clause_id: int) -> List[ClauseMatch]:
        """获取指定条款的所有匹配"""
        return [m for m in self.matches if m.standard_clause_id == clause_id]

logger = logging.getLogger(__name__)

class SemanticMatcher:
    """语义匹配器 - 基于AI的智能条款匹配"""
    
    def __init__(self, qwen_client=None):
        """
        初始化语义匹配器
        
        Args:
            qwen_client: 千问3 API客户端实例
        """
        self.qwen_client = qwen_client
        self.cache = {}  # 简单的内存缓存
        self.cache_ttl = 3600  # 缓存1小时
        
    async def match_clauses(
        self, 
        contract_text: str, 
        standard_clauses: List[StandardClause],
        contract_type: str = "general"
    ) -> List[ClauseMatch]:
        """
        匹配合同文本中的条款
        
        Args:
            contract_text: 合同文本
            standard_clauses: 标准条款列表
            contract_type: 合同类型
            
        Returns:
            匹配结果列表
        """
        logger.info(f"开始条款匹配，标准条款数量: {len(standard_clauses)}")
        
        matches = []
        
        for clause in standard_clauses:
            # 检查条款是否适用于当前合同类型
            if contract_type not in clause.contract_types:
                continue
            
            # 执行多种匹配方法
            clause_matches = await self._match_single_clause(
                contract_text, clause, contract_type
            )
            
            matches.extend(clause_matches)
        
        # 去重和排序
        matches = self._deduplicate_matches(matches)
        matches.sort(key=lambda x: x.match_score, reverse=True)
        
        logger.info(f"条款匹配完成，找到 {len(matches)} 个匹配")
        return matches
    
    async def _match_single_clause(
        self, 
        contract_text: str, 
        clause: StandardClause,
        contract_type: str
    ) -> List[ClauseMatch]:
        """匹配单个条款"""
        matches = []
        
        for method in clause.match_methods:
            if method == MatchMethod.SEMANTIC:
                semantic_matches = await self._semantic_match(contract_text, clause)
                matches.extend(semantic_matches)
            elif method == MatchMethod.KEYWORD:
                keyword_matches = self._keyword_match(contract_text, clause)
                matches.extend(keyword_matches)
            elif method == MatchMethod.REGEX:
                regex_matches = self._regex_match(contract_text, clause)
                matches.extend(regex_matches)
            elif method == MatchMethod.HYBRID:
                hybrid_matches = await self._hybrid_match(contract_text, clause)
                matches.extend(hybrid_matches)
        
        return matches
    
    async def _semantic_match(
        self, 
        contract_text: str, 
        clause: StandardClause
    ) -> List[ClauseMatch]:
        """基于语义理解的匹配"""
        if not self.qwen_client:
            logger.warning("千问3客户端未配置，跳过语义匹配")
            return []
        
        try:
            # 构建语义匹配的提示词
            prompt = self._build_semantic_prompt(contract_text, clause)
            
            # 检查缓存
            cache_key = f"semantic_{hash(prompt)}"
            if cache_key in self.cache:
                cached_result = self.cache[cache_key]
                if datetime.now().timestamp() - cached_result['timestamp'] < self.cache_ttl:
                    logger.debug(f"使用缓存结果: {clause.clause_name}")
                    return self._parse_semantic_response(cached_result['response'], clause)
            
            # 调用AI API
            response = await self.qwen_client._call_api(prompt, max_tokens=800)
            
            # 缓存结果
            self.cache[cache_key] = {
                'response': response,
                'timestamp': datetime.now().timestamp()
            }
            
            # 解析响应
            matches = self._parse_semantic_response(response, clause)
            
            logger.debug(f"语义匹配 {clause.clause_name}: 找到 {len(matches)} 个匹配")
            return matches
            
        except Exception as e:
            logger.error(f"语义匹配失败 {clause.clause_name}: {e}")
            return []
    
    def _build_semantic_prompt(self, contract_text: str, clause: StandardClause) -> str:
        """构建语义匹配的提示词"""
        return f"""
请分析以下合同文本，找出与指定条款相关的内容。

目标条款信息：
- 条款名称：{clause.clause_name}
- 条款描述：{clause.description}
- 条款目的：{clause.purpose}
- 标准文本：{clause.standard_text}
- 语义关键词：{', '.join(clause.semantic_keywords)}

合同文本（前3000字符）：
{contract_text[:3000]}

请仔细分析合同文本，找出与目标条款语义相关的段落或句子。
注意：
1. 不要求文字完全匹配，重点关注语义和意图的相似性
2. 考虑同义词、近义词和不同的表达方式
3. 关注条款的实质内容而非表面文字

请以JSON格式回复：
{{
    "matches": [
        {{
            "matched_text": "匹配到的具体文本",
            "start_position": 开始位置,
            "end_position": 结束位置,
            "match_score": 0.85,
            "confidence": 0.90,
            "match_reason": "匹配原因说明",
            "semantic_similarity": "语义相似性描述"
        }}
    ]
}}

如果没有找到相关内容，请返回空的matches数组。
"""
    
    def _parse_semantic_response(
        self, 
        response: str, 
        clause: StandardClause
    ) -> List[ClauseMatch]:
        """解析语义匹配的AI响应"""
        try:
            # 提取JSON部分
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                result = json.loads(json_str)
                
                matches = []
                for match_data in result.get('matches', []):
                    # 验证匹配分数
                    match_score = match_data.get('match_score', 0.0)
                    if match_score < clause.match_threshold:
                        continue
                    
                    match = ClauseMatch(
                        standard_clause_id=clause.id,
                        clause_name=clause.clause_name,
                        matched_text=match_data.get('matched_text', ''),
                        match_score=match_score,
                        match_method=MatchMethod.SEMANTIC,
                        start_position=match_data.get('start_position', 0),
                        end_position=match_data.get('end_position', 0),
                        confidence=match_data.get('confidence', match_score),
                        match_reason=match_data.get('match_reason', '语义匹配')
                    )
                    matches.append(match)
                
                return matches
        except Exception as e:
            logger.error(f"解析语义匹配响应失败: {e}")
        
        return []
    
    def _keyword_match(
        self, 
        contract_text: str, 
        clause: StandardClause
    ) -> List[ClauseMatch]:
        """基于关键词的匹配"""
        matches = []
        
        if not clause.keywords:
            return matches
        
        text_lower = contract_text.lower()
        
        for keyword in clause.keywords:
            keyword_lower = keyword.lower()
            
            # 查找所有匹配位置
            start = 0
            while True:
                pos = text_lower.find(keyword_lower, start)
                if pos == -1:
                    break
                
                # 提取上下文
                context_start = max(0, pos - 50)
                context_end = min(len(contract_text), pos + len(keyword) + 50)
                matched_text = contract_text[context_start:context_end]
                
                # 计算匹配分数
                match_score = self._calculate_keyword_score(
                    contract_text, clause, pos, keyword
                )
                
                if match_score >= clause.match_threshold:
                    match = ClauseMatch(
                        standard_clause_id=clause.id,
                        clause_name=clause.clause_name,
                        matched_text=matched_text,
                        match_score=match_score,
                        match_method=MatchMethod.KEYWORD,
                        start_position=pos,
                        end_position=pos + len(keyword),
                        matched_keywords=[keyword],
                        confidence=match_score,
                        match_reason=f"关键词匹配: {keyword}"
                    )
                    matches.append(match)
                
                start = pos + 1
        
        return matches
    
    def _regex_match(
        self, 
        contract_text: str, 
        clause: StandardClause
    ) -> List[ClauseMatch]:
        """基于正则表达式的匹配"""
        matches = []
        
        if not clause.regex_patterns:
            return matches
        
        for pattern in clause.regex_patterns:
            try:
                for match in re.finditer(pattern, contract_text, re.IGNORECASE | re.MULTILINE):
                    # 计算匹配分数
                    match_score = self._calculate_regex_score(
                        contract_text, clause, match
                    )
                    
                    if match_score >= clause.match_threshold:
                        clause_match = ClauseMatch(
                            standard_clause_id=clause.id,
                            clause_name=clause.clause_name,
                            matched_text=match.group(),
                            match_score=match_score,
                            match_method=MatchMethod.REGEX,
                            start_position=match.start(),
                            end_position=match.end(),
                            confidence=match_score,
                            match_reason=f"正则匹配: {pattern}"
                        )
                        matches.append(clause_match)
            except re.error as e:
                logger.error(f"正则表达式错误 {pattern}: {e}")
        
        return matches
    
    async def _hybrid_match(
        self, 
        contract_text: str, 
        clause: StandardClause
    ) -> List[ClauseMatch]:
        """混合匹配方法"""
        # 先进行关键词和正则匹配
        keyword_matches = self._keyword_match(contract_text, clause)
        regex_matches = self._regex_match(contract_text, clause)
        
        # 如果基础匹配有结果，再进行语义验证
        basic_matches = keyword_matches + regex_matches
        if basic_matches and self.qwen_client:
            # 对基础匹配结果进行语义验证
            verified_matches = await self._verify_matches_semantically(
                contract_text, clause, basic_matches
            )
            return verified_matches
        
        # 如果没有基础匹配，尝试语义匹配
        if not basic_matches:
            semantic_matches = await self._semantic_match(contract_text, clause)
            return semantic_matches
        
        return basic_matches
    
    async def _verify_matches_semantically(
        self, 
        contract_text: str, 
        clause: StandardClause, 
        matches: List[ClauseMatch]
    ) -> List[ClauseMatch]:
        """语义验证匹配结果"""
        if not self.qwen_client or not matches:
            return matches
        
        try:
            # 构建验证提示词
            match_texts = [match.matched_text for match in matches]
            prompt = f"""
请验证以下文本片段是否真正符合指定条款的语义要求：

目标条款：
- 名称：{clause.clause_name}
- 描述：{clause.description}
- 目的：{clause.purpose}

待验证的文本片段：
{json.dumps(match_texts, ensure_ascii=False, indent=2)}

请对每个文本片段进行语义验证，判断是否真正符合条款要求。

请以JSON格式回复：
{{
    "verifications": [
        {{
            "text_index": 0,
            "is_valid": true,
            "confidence": 0.85,
            "reason": "验证理由"
        }}
    ]
}}
"""
            
            response = await self.qwen_client._call_api(prompt, max_tokens=500)
            
            # 解析验证结果
            verified_matches = self._parse_verification_response(response, matches)
            return verified_matches
            
        except Exception as e:
            logger.error(f"语义验证失败: {e}")
            return matches
    
    def _parse_verification_response(
        self, 
        response: str, 
        matches: List[ClauseMatch]
    ) -> List[ClauseMatch]:
        """解析验证响应"""
        try:
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                result = json.loads(json_str)
                
                verified_matches = []
                verifications = result.get('verifications', [])
                
                for i, verification in enumerate(verifications):
                    if i < len(matches) and verification.get('is_valid', False):
                        match = matches[i]
                        # 更新置信度
                        match.confidence = verification.get('confidence', match.confidence)
                        match.match_reason += f" (语义验证: {verification.get('reason', '通过')})"
                        verified_matches.append(match)
                
                return verified_matches
        except Exception as e:
            logger.error(f"解析验证响应失败: {e}")
        
        return matches
    
    def _calculate_keyword_score(
        self, 
        contract_text: str, 
        clause: StandardClause, 
        position: int, 
        keyword: str
    ) -> float:
        """计算关键词匹配分数"""
        base_score = 0.6
        
        # 关键词重要性权重
        if keyword in clause.semantic_keywords:
            base_score += 0.2
        
        # 上下文相关性
        context_start = max(0, position - 100)
        context_end = min(len(contract_text), position + len(keyword) + 100)
        context = contract_text[context_start:context_end].lower()
        
        # 检查上下文中是否有其他相关关键词
        related_keywords = 0
        for other_keyword in clause.keywords:
            if other_keyword.lower() != keyword.lower() and other_keyword.lower() in context:
                related_keywords += 1
        
        # 相关关键词越多，分数越高
        context_bonus = min(related_keywords * 0.05, 0.2)
        
        return min(base_score + context_bonus, 1.0)
    
    def _calculate_regex_score(
        self, 
        contract_text: str, 
        clause: StandardClause, 
        match: re.Match
    ) -> float:
        """计算正则匹配分数"""
        base_score = 0.7
        
        # 匹配长度权重
        match_length = len(match.group())
        if match_length > 20:
            base_score += 0.1
        
        # 检查匹配文本中是否包含关键词
        matched_text_lower = match.group().lower()
        keyword_count = 0
        for keyword in clause.keywords:
            if keyword.lower() in matched_text_lower:
                keyword_count += 1
        
        keyword_bonus = min(keyword_count * 0.05, 0.2)
        
        return min(base_score + keyword_bonus, 1.0)
    
    def _deduplicate_matches(self, matches: List[ClauseMatch]) -> List[ClauseMatch]:
        """去除重复匹配"""
        if not matches:
            return matches
        
        # 按条款ID和位置排序
        matches.sort(key=lambda x: (x.standard_clause_id, x.start_position))
        
        deduplicated = []
        for match in matches:
            # 检查是否与已有匹配重叠
            is_duplicate = False
            for existing in deduplicated:
                if (existing.standard_clause_id == match.standard_clause_id and
                    self._is_overlapping(existing, match)):
                    # 保留分数更高的匹配
                    if match.match_score > existing.match_score:
                        deduplicated.remove(existing)
                        deduplicated.append(match)
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                deduplicated.append(match)
        
        return deduplicated
    
    def _is_overlapping(self, match1: ClauseMatch, match2: ClauseMatch) -> bool:
        """检查两个匹配是否重叠"""
        # 计算重叠长度
        overlap_start = max(match1.start_position, match2.start_position)
        overlap_end = min(match1.end_position, match2.end_position)
        overlap_length = max(0, overlap_end - overlap_start)
        
        # 计算重叠比例
        match1_length = match1.end_position - match1.start_position
        match2_length = match2.end_position - match2.start_position
        min_length = min(match1_length, match2_length)
        
        if min_length == 0:
            return False
        
        overlap_ratio = overlap_length / min_length
        return overlap_ratio > 0.5  # 重叠超过50%认为是重复