"""
智能条款检测算法 - 核心实现
简化版本，用于测试项目重组后的功能
"""

import asyncio
import json
import uuid
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import logging
from dataclasses import dataclass
from enum import Enum

# 导入核心模块
from src.core.ai_client import QwenAPIClient
from src.core.models import ContractAnalysisResult, ExtractedElement
import jieba
import re

# 避免循环导入，直接在这里定义需要的类型

# 配置日志
logger = logging.getLogger(__name__)

class ClauseDetectionMethod(Enum):
    """条款检测方法"""
    KEYWORD = "keyword"
    SEMANTIC = "semantic" 
    AI = "ai"
    PATTERN = "pattern"
    HYBRID = "hybrid"

class ClauseImportanceLevel(Enum):
    """条款重要性级别"""
    CRITICAL = "critical"  # 关键条款
    HIGH = "high"         # 高重要性
    MEDIUM = "medium"     # 中等重要性
    LOW = "low"           # 低重要性

@dataclass
class StandardClause:
    """标准条款数据结构"""
    id: str
    code: str
    name: str
    category: str
    content: str
    importance_level: ClauseImportanceLevel
    risk_level: str
    keywords: List[str]
    semantic_tags: List[str]
    ai_prompt: Optional[str] = None
    legal_requirement: str = "optional"
    business_impact: str = "medium"

@dataclass
class ClauseDetectionResult:
    """条款检测结果"""
    clause_id: str
    clause_name: str
    status: str  # found, missing, partial
    confidence: float
    matched_content: Optional[str] = None
    position: Optional[Dict[str, Any]] = None
    method: Optional[ClauseDetectionMethod] = None
    risk_assessment: Optional[Dict[str, Any]] = None
    recommendations: Optional[List[str]] = None

@dataclass
class ClauseRecommendation:
    """条款推荐"""
    missing_clause_id: str
    recommended_content: str
    reason: str
    confidence: float
    priority: int
    customization_notes: Optional[str] = None

class ClauseDetectionEngine:
    """智能条款检测引擎"""
    
    def __init__(self, qwen_client: Optional[QwenAPIClient] = None):
        """初始化检测引擎"""
        self.qwen_client = qwen_client
        self.standard_clauses: Dict[str, StandardClause] = {}
        self.detection_cache: Dict[str, Any] = {}
        self.confidence_thresholds = {
            ClauseDetectionMethod.KEYWORD: 0.6,
            ClauseDetectionMethod.SEMANTIC: 0.7,
            ClauseDetectionMethod.AI: 0.8,
            ClauseDetectionMethod.PATTERN: 0.65
        }
        self._load_standard_clauses()
    
    def _load_standard_clauses(self):
        """加载标准条款库"""
        # 使用示例数据
        self.standard_clauses = {
            "payment_001": StandardClause(
                id="payment_001",
                code="PAYMENT_TERMS_BASIC",
                name="基本付款条款",
                category="payment",
                content="甲方应在收到乙方开具的正式发票后30日内支付相应款项",
                importance_level=ClauseImportanceLevel.HIGH,
                risk_level="medium",
                keywords=["付款", "支付", "发票", "30日", "款项"],
                semantic_tags=["payment", "invoice", "deadline", "money_transfer"],
                ai_prompt="识别合同中关于付款时间和方式的条款",
                legal_requirement="recommended",
                business_impact="high"
            ),
            "liability_001": StandardClause(
                id="liability_001", 
                code="LIABILITY_LIMITATION",
                name="责任限制条款",
                category="liability",
                content="除故意或重大过失外，任何一方的赔偿责任不超过合同总金额的50%",
                importance_level=ClauseImportanceLevel.CRITICAL,
                risk_level="high",
                keywords=["责任", "赔偿", "故意", "过失", "合同总金额", "50%"],
                semantic_tags=["liability", "damages", "limitation", "percentage"],
                ai_prompt="识别合同中关于责任限制和赔偿上限的条款",
                legal_requirement="mandatory",
                business_impact="high"
            )
        }
        logger.info(f"加载了 {len(self.standard_clauses)} 个标准条款")
    
    def _keyword_detection(self, content: str, clause: StandardClause) -> Dict[str, Any]:
        """关键词检测"""
        keywords = clause.keywords
        if not keywords:
            return {"confidence": 0.0, "method": ClauseDetectionMethod.KEYWORD}
        
        # 使用jieba进行中文分词
        words = list(jieba.cut(content))
        content_words = set(word.strip() for word in words if len(word.strip()) > 1)
        
        # 计算关键词匹配度
        matched_keywords = []
        for keyword in keywords:
            if keyword in content or any(keyword in word for word in content_words):
                matched_keywords.append(keyword)
        
        confidence = len(matched_keywords) / len(keywords) if keywords else 0.0
        
        return {
            "confidence": confidence,
            "method": ClauseDetectionMethod.KEYWORD,
            "matched_keywords": matched_keywords,
            "match_ratio": confidence
        }
    
    async def detect_clauses(self, 
                           contract_id: str, 
                           contract_content: str,
                           contract_type: Optional[str] = None,
                           enable_ai: bool = True) -> Tuple[List[ClauseDetectionResult], List[ClauseRecommendation]]:
        """执行条款检测"""
        logger.info(f"开始检测合同 {contract_id} 的条款")
        
        detection_results = []
        recommendations = []
        
        # 为每个标准条款执行检测
        for clause_id, standard_clause in self.standard_clauses.items():
            # 执行关键词检测
            keyword_result = self._keyword_detection(contract_content, standard_clause)
            
            # 判断状态
            if keyword_result["confidence"] >= 0.6:
                status = "found"
            elif keyword_result["confidence"] >= 0.3:
                status = "partial"
            else:
                status = "missing"
            
            # 创建检测结果
            detection_result = ClauseDetectionResult(
                clause_id=clause_id,
                clause_name=standard_clause.name,
                status=status,
                confidence=keyword_result["confidence"],
                matched_content=None,
                position=None,
                method=ClauseDetectionMethod.KEYWORD,
                risk_assessment=None,
                recommendations=[]
            )
            
            detection_results.append(detection_result)
            
            # 如果条款缺失且重要性高，生成推荐
            if (status == "missing" and 
                standard_clause.importance_level in [ClauseImportanceLevel.CRITICAL, ClauseImportanceLevel.HIGH]):
                
                recommendation = ClauseRecommendation(
                    missing_clause_id=clause_id,
                    recommended_content=standard_clause.content,
                    reason=f"建议添加{standard_clause.name}",
                    confidence=0.8,
                    priority=8 if standard_clause.importance_level == ClauseImportanceLevel.HIGH else 10
                )
                recommendations.append(recommendation)
        
        logger.info(f"检测完成：发现 {len([r for r in detection_results if r.status == 'found'])} 个条款，"
                   f"缺失 {len([r for r in detection_results if r.status == 'missing'])} 个条款")
        
        return detection_results, recommendations