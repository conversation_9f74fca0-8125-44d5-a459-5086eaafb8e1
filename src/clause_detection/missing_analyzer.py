"""
缺失条款识别与分析模块
实现智能化的缺失条款检测和风险评估
"""

import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass
from enum import Enum
from datetime import datetime
import json
import re

from semantic_matcher import <PERSON>man<PERSON><PERSON><PERSON><PERSON>, SemanticMatchResult
from clause_detection_engine import StandardClause, ClauseImportanceLevel

logger = logging.getLogger(__name__)

class MissingClauseRiskLevel(Enum):
    """缺失条款风险级别"""
    CRITICAL = "critical"  # 严重风险
    HIGH = "high"         # 高风险
    MEDIUM = "medium"     # 中等风险
    LOW = "low"           # 低风险

class BusinessImpactLevel(Enum):
    """业务影响级别"""
    SEVERE = "severe"      # 严重影响
    MAJOR = "major"        # 重大影响
    MODERATE = "moderate"  # 中等影响
    MINOR = "minor"        # 轻微影响

@dataclass
class MissingClauseAnalysis:
    """缺失条款分析结果"""
    clause_id: str
    clause_name: str
    clause_category: str
    missing_reason: str
    risk_level: MissingClauseRiskLevel
    business_impact: BusinessImpactLevel
    legal_implications: str
    financial_risk: Optional[str]
    operational_risk: Optional[str]
    compliance_risk: Optional[str]
    urgency_score: float  # 0-1, 紧急程度
    impact_score: float   # 0-1, 影响程度
    overall_priority: int  # 1-10, 整体优先级
    recommendations: List[str]
    mitigation_strategies: List[str]
    detection_confidence: float

@dataclass
class ContractRiskProfile:
    """合同风险画像"""
    contract_id: str
    overall_risk_score: float  # 0-1, 整体风险分数
    risk_distribution: Dict[str, int]  # 各风险级别的数量分布
    category_risks: Dict[str, float]   # 各类别的风险分数
    critical_missing_clauses: List[str]
    high_priority_actions: List[str]
    compliance_status: Dict[str, bool]
    business_continuity_risk: float
    legal_exposure_risk: float
    financial_loss_risk: float

class MissingClauseAnalyzer:
    """缺失条款分析器"""
    
    def __init__(self, semantic_matcher: SemanticMatcher):
        """初始化分析器"""
        self.semantic_matcher = semantic_matcher
        self.risk_assessment_rules = self._build_risk_assessment_rules()
        self.business_impact_rules = self._build_business_impact_rules()
        self.legal_requirement_mapping = self._build_legal_requirement_mapping()
        self.industry_standards = self._build_industry_standards()
        
    def _build_risk_assessment_rules(self) -> Dict[str, Dict[str, Any]]:
        """构建风险评估规则"""
        return {
            # 付款条款风险规则
            "payment": {
                "critical_indicators": ["无付款期限", "无付款方式", "无违约处理"],
                "high_indicators": ["付款期限过长", "付款条件不明确", "无发票要求"],
                "medium_indicators": ["付款方式单一", "无利息约定"],
                "financial_impact_multiplier": 1.5,
                "legal_requirement_level": "mandatory"
            },
            
            # 责任条款风险规则
            "liability": {
                "critical_indicators": ["无责任限制", "无免责条款", "无赔偿上限"],
                "high_indicators": ["责任范围过大", "赔偿标准不明", "无过错认定"],
                "medium_indicators": ["责任分工不清", "举证责任未约定"],
                "financial_impact_multiplier": 2.0,
                "legal_requirement_level": "mandatory"
            },
            
            # 终止条款风险规则
            "termination": {
                "critical_indicators": ["无终止条件", "无通知要求"],
                "high_indicators": ["终止程序不明", "无后果处理", "通知期过短"],
                "medium_indicators": ["终止条件过严", "无协商机制"],
                "financial_impact_multiplier": 1.2,
                "legal_requirement_level": "recommended"
            },
            
            # 争议解决条款风险规则
            "dispute_resolution": {
                "critical_indicators": ["无争议解决机制", "管辖权不明"],
                "high_indicators": ["仲裁条款缺失", "适用法律不明"],
                "medium_indicators": ["协商程序缺失", "执行机制不明"],
                "financial_impact_multiplier": 1.8,
                "legal_requirement_level": "highly_recommended"
            },
            
            # 保密条款风险规则
            "confidentiality": {
                "critical_indicators": ["无保密义务", "保密范围不明"],
                "high_indicators": ["保密期限过短", "违约后果轻微"],
                "medium_indicators": ["保密例外不明", "返还义务缺失"],
                "financial_impact_multiplier": 1.3,
                "legal_requirement_level": "recommended"
            }
        }
    
    def _build_business_impact_rules(self) -> Dict[str, Dict[str, Any]]:
        """构建业务影响评估规则"""
        return {
            "payment": {
                "severe_impact": ["现金流中断", "收款风险", "坏账风险"],
                "major_impact": ["付款延迟", "资金占用", "财务成本"],
                "moderate_impact": ["管理复杂", "核算困难"],
                "minor_impact": ["流程不便", "效率降低"]
            },
            
            "liability": {
                "severe_impact": ["无限责任", "重大赔偿", "声誉损失"],
                "major_impact": ["法律诉讼", "赔偿损失", "业务中断"],
                "moderate_impact": ["合规成本", "管理负担"],
                "minor_impact": ["沟通成本", "协调困难"]
            },
            
            "termination": {
                "severe_impact": ["合同无法终止", "业务被锁定"],
                "major_impact": ["终止成本高", "业务转换难"],
                "moderate_impact": ["程序复杂", "时间成本"],
                "minor_impact": ["手续繁琐", "协调工作"]
            },
            
            "confidentiality": {
                "severe_impact": ["商业秘密泄露", "竞争优势丧失"],
                "major_impact": ["客户信息泄露", "技术秘密外泄"],
                "moderate_impact": ["内部信息外流", "管理混乱"],
                "minor_impact": ["信息安全隐患", "流程不规范"]
            }
        }
    
    def _build_legal_requirement_mapping(self) -> Dict[str, str]:
        """构建法律要求映射"""
        return {
            "payment": "《合同法》、《民法典》要求明确付款义务",
            "liability": "《民法典》侵权责任编要求明确责任承担",
            "termination": "《合同法》要求约定合同终止条件",
            "dispute_resolution": "《仲裁法》、《民事诉讼法》相关规定",
            "confidentiality": "《反不正当竞争法》、《数据安全法》要求",
            "intellectual_property": "《著作权法》、《专利法》、《商标法》要求",
            "force_majeure": "《民法典》不可抗力相关规定",
            "warranty": "《产品质量法》、《消费者权益保护法》要求"
        }
    
    def _build_industry_standards(self) -> Dict[str, List[str]]:
        """构建行业标准映射"""
        return {
            "technology": [
                "知识产权条款", "技术保密条款", "源代码管理条款", "数据安全条款"
            ],
            "manufacturing": [
                "质量保证条款", "产品责任条款", "交付时间条款", "验收标准条款"
            ],
            "service": [
                "服务水平协议", "响应时间条款", "服务质量条款", "客户数据保护条款"
            ],
            "financial": [
                "合规性条款", "资金安全条款", "审计条款", "监管报告条款"
            ],
            "construction": [
                "工程质量条款", "安全责任条款", "工期条款", "材料标准条款"
            ]
        }
    
    async def analyze_missing_clauses(self, 
                                    contract_content: str,
                                    found_clauses: List[str],
                                    all_standard_clauses: Dict[str, StandardClause],
                                    contract_type: Optional[str] = None,
                                    industry: Optional[str] = None) -> Tuple[List[MissingClauseAnalysis], ContractRiskProfile]:
        """
        分析缺失条款并生成风险评估
        
        Args:
            contract_content: 合同内容
            found_clauses: 已发现的条款ID列表
            all_standard_clauses: 所有标准条款
            contract_type: 合同类型
            industry: 行业类型
            
        Returns:
            缺失条款分析结果和合同风险画像
        """
        logger.info("开始缺失条款分析")
        
        # 1. 识别缺失条款
        missing_clause_ids = set(all_standard_clauses.keys()) - set(found_clauses)
        
        # 2. 过滤适用条款（基于合同类型和行业）
        applicable_missing_clauses = self._filter_applicable_clauses(
            missing_clause_ids, all_standard_clauses, contract_type, industry
        )
        
        # 3. 逐个分析缺失条款
        missing_analyses = []
        for clause_id in applicable_missing_clauses:
            standard_clause = all_standard_clauses[clause_id]
            analysis = await self._analyze_single_missing_clause(
                contract_content, standard_clause, contract_type, industry
            )
            missing_analyses.append(analysis)
        
        # 4. 生成合同风险画像
        risk_profile = self._generate_contract_risk_profile(
            contract_content, missing_analyses, found_clauses, all_standard_clauses
        )
        
        # 5. 按优先级排序
        missing_analyses.sort(key=lambda x: (-x.overall_priority, -x.urgency_score))
        
        logger.info(f"缺失条款分析完成：发现 {len(missing_analyses)} 个缺失条款，"
                   f"整体风险分数: {risk_profile.overall_risk_score:.3f}")
        
        return missing_analyses, risk_profile
    
    def _filter_applicable_clauses(self, 
                                 missing_clause_ids: Set[str],
                                 all_clauses: Dict[str, StandardClause],
                                 contract_type: Optional[str],
                                 industry: Optional[str]) -> Set[str]:
        """过滤适用的条款"""
        applicable = set()
        
        for clause_id in missing_clause_ids:
            clause = all_clauses[clause_id]
            
            # 基于合同类型过滤
            if contract_type:
                # 这里应该从数据库查询，暂时用简化逻辑
                if clause.category in ["payment", "liability", "termination"]:  # 通用条款
                    applicable.add(clause_id)
                elif contract_type == "service" and clause.category in ["service_level", "confidentiality"]:
                    applicable.add(clause_id)
                elif contract_type == "sales" and clause.category in ["delivery", "warranty"]:
                    applicable.add(clause_id)
            else:
                # 没有指定合同类型时，包含所有重要条款
                if clause.importance_level in [ClauseImportanceLevel.CRITICAL, ClauseImportanceLevel.HIGH]:
                    applicable.add(clause_id)
        
        # 基于行业标准过滤
        if industry and industry in self.industry_standards:
            industry_required_categories = self.industry_standards[industry]
            for clause_id in missing_clause_ids:
                clause = all_clauses[clause_id]
                if any(req in clause.name for req in industry_required_categories):
                    applicable.add(clause_id)
        
        return applicable
    
    async def _analyze_single_missing_clause(self,
                                           contract_content: str,
                                           standard_clause: StandardClause,
                                           contract_type: Optional[str],
                                           industry: Optional[str]) -> MissingClauseAnalysis:
        """分析单个缺失条款"""
        
        # 1. 确认条款确实缺失（深度检测）
        missing_confidence = await self._confirm_clause_missing(contract_content, standard_clause)
        
        # 2. 分析缺失原因
        missing_reason = self._analyze_missing_reason(contract_content, standard_clause)
        
        # 3. 评估风险级别
        risk_level = self._assess_risk_level(standard_clause, contract_type, industry)
        
        # 4. 评估业务影响
        business_impact = self._assess_business_impact(standard_clause, contract_type, industry)
        
        # 5. 分析法律影响
        legal_implications = self._analyze_legal_implications(standard_clause)
        
        # 6. 评估各类风险
        financial_risk = self._assess_financial_risk(standard_clause)
        operational_risk = self._assess_operational_risk(standard_clause)
        compliance_risk = self._assess_compliance_risk(standard_clause)
        
        # 7. 计算紧急程度和影响程度
        urgency_score = self._calculate_urgency_score(standard_clause, risk_level)
        impact_score = self._calculate_impact_score(standard_clause, business_impact)
        
        # 8. 计算整体优先级
        overall_priority = self._calculate_overall_priority(
            standard_clause.importance_level, risk_level, business_impact, urgency_score, impact_score
        )
        
        # 9. 生成建议和缓解策略
        recommendations = self._generate_recommendations(standard_clause, risk_level)
        mitigation_strategies = self._generate_mitigation_strategies(standard_clause, risk_level)
        
        return MissingClauseAnalysis(
            clause_id=standard_clause.id,
            clause_name=standard_clause.name,
            clause_category=standard_clause.category,
            missing_reason=missing_reason,
            risk_level=risk_level,
            business_impact=business_impact,
            legal_implications=legal_implications,
            financial_risk=financial_risk,
            operational_risk=operational_risk,
            compliance_risk=compliance_risk,
            urgency_score=urgency_score,
            impact_score=impact_score,
            overall_priority=overall_priority,
            recommendations=recommendations,
            mitigation_strategies=mitigation_strategies,
            detection_confidence=missing_confidence
        )
    
    async def _confirm_clause_missing(self, content: str, clause: StandardClause) -> float:
        """确认条款确实缺失的置信度"""
        # 使用语义匹配器进行深度检测
        semantic_result = await self.semantic_matcher.calculate_semantic_similarity(
            content, clause.content, clause.keywords, clause.semantic_tags
        )
        
        # 置信度越高说明条款越可能存在，返回1-置信度表示缺失的置信度
        missing_confidence = 1.0 - semantic_result.similarity_score
        
        # 如果语义相似度很高但关键词匹配度很低，可能是表述不同但意思相同
        if (semantic_result.similarity_score > 0.7 and 
            semantic_result.semantic_features.get("keyword_similarity", 0) < 0.3):
            missing_confidence = 0.3  # 降低缺失的置信度
        
        return missing_confidence
    
    def _analyze_missing_reason(self, content: str, clause: StandardClause) -> str:
        """分析条款缺失原因"""
        reasons = []
        
        # 检查是否有相关但不完整的表述
        keywords_found = sum(1 for keyword in clause.keywords if keyword in content)
        if keywords_found > 0:
            reasons.append(f"存在相关表述但不完整（找到{keywords_found}/{len(clause.keywords)}个关键词）")
        else:
            reasons.append("完全缺失相关条款")
        
        # 检查条款类别的整体情况
        category_keywords = {
            "payment": ["付款", "支付", "费用"],
            "liability": ["责任", "赔偿", "损失"],
            "termination": ["终止", "解除", "取消"]
        }
        
        if clause.category in category_keywords:
            category_words = category_keywords[clause.category]
            category_mentions = sum(1 for word in category_words if word in content)
            if category_mentions == 0:
                reasons.append(f"合同中缺少{clause.category}相关的整体规定")
        
        return "; ".join(reasons)
    
    def _assess_risk_level(self, 
                          clause: StandardClause, 
                          contract_type: Optional[str],
                          industry: Optional[str]) -> MissingClauseRiskLevel:
        """评估缺失条款的风险级别"""
        
        # 基于条款重要性的基础风险级别
        base_risk = {
            ClauseImportanceLevel.CRITICAL: MissingClauseRiskLevel.CRITICAL,
            ClauseImportanceLevel.HIGH: MissingClauseRiskLevel.HIGH,
            ClauseImportanceLevel.MEDIUM: MissingClauseRiskLevel.MEDIUM,
            ClauseImportanceLevel.LOW: MissingClauseRiskLevel.LOW
        }.get(clause.importance_level, MissingClauseRiskLevel.MEDIUM)
        
        # 基于条款类别的风险调整
        category_risk_adjustment = {
            "liability": 1,      # 责任条款风险上调
            "payment": 1,        # 付款条款风险上调
            "termination": 0,    # 终止条款风险维持
            "confidentiality": -1 if industry != "technology" else 1,  # 技术行业保密条款风险上调
            "warranty": 1 if contract_type == "sales" else 0  # 销售合同质保条款风险上调
        }
        
        risk_levels = [
            MissingClauseRiskLevel.LOW,
            MissingClauseRiskLevel.MEDIUM, 
            MissingClauseRiskLevel.HIGH,
            MissingClauseRiskLevel.CRITICAL
        ]
        
        current_index = risk_levels.index(base_risk)
        adjustment = category_risk_adjustment.get(clause.category, 0)
        new_index = max(0, min(len(risk_levels) - 1, current_index + adjustment))
        
        return risk_levels[new_index]
    
    def _assess_business_impact(self,
                              clause: StandardClause,
                              contract_type: Optional[str],
                              industry: Optional[str]) -> BusinessImpactLevel:
        """评估业务影响级别"""
        
        # 基于条款类别评估业务影响
        category_impact = {
            "payment": BusinessImpactLevel.SEVERE,      # 付款条款影响现金流
            "liability": BusinessImpactLevel.MAJOR,     # 责任条款影响赔偿风险
            "termination": BusinessImpactLevel.MODERATE, # 终止条款影响合同管理
            "confidentiality": BusinessImpactLevel.MAJOR if industry == "technology" else BusinessImpactLevel.MODERATE,
            "warranty": BusinessImpactLevel.MAJOR if contract_type == "sales" else BusinessImpactLevel.MODERATE
        }
        
        return category_impact.get(clause.category, BusinessImpactLevel.MODERATE)
    
    def _analyze_legal_implications(self, clause: StandardClause) -> str:
        """分析法律影响"""
        legal_basis = self.legal_requirement_mapping.get(clause.category, "")
        
        implications = []
        
        if clause.legal_requirement == "mandatory":
            implications.append("违反法定要求，可能面临法律责任")
        elif clause.legal_requirement == "highly_recommended":
            implications.append("不符合行业最佳实践，增加法律风险")
        else:
            implications.append("存在潜在法律风险，建议完善")
        
        if legal_basis:
            implications.append(f"相关法律依据：{legal_basis}")
        
        # 基于条款类别添加具体法律风险
        category_legal_risks = {
            "payment": "可能导致付款纠纷、资金损失、合同履行困难",
            "liability": "可能承担过度责任、面临重大赔偿、缺乏法律保护",
            "termination": "可能无法正常终止合同、被动承担义务、程序违法风险",
            "confidentiality": "可能面临商业秘密泄露、不正当竞争指控",
            "dispute_resolution": "争议处理程序不明，增加诉讼成本和时间"
        }
        
        if clause.category in category_legal_risks:
            implications.append(category_legal_risks[clause.category])
        
        return "；".join(implications)
    
    def _assess_financial_risk(self, clause: StandardClause) -> Optional[str]:
        """评估财务风险"""
        financial_risks = {
            "payment": "现金流风险、坏账风险、资金占用成本增加",
            "liability": "潜在赔偿损失、诉讼费用、保险成本上升",
            "warranty": "产品责任赔偿、退换货成本、品牌价值损失",
            "penalty": "违约金风险、合同执行成本上升"
        }
        
        return financial_risks.get(clause.category)
    
    def _assess_operational_risk(self, clause: StandardClause) -> Optional[str]:
        """评估运营风险"""
        operational_risks = {
            "termination": "合同管理困难、业务连续性风险、供应链中断",
            "service_level": "服务质量下降、客户满意度降低、业务流程混乱",
            "confidentiality": "信息安全风险、内部管理混乱、员工行为风险",
            "delivery": "交付延迟风险、库存管理困难、客户关系恶化"
        }
        
        return operational_risks.get(clause.category)
    
    def _assess_compliance_risk(self, clause: StandardClause) -> Optional[str]:
        """评估合规风险"""
        compliance_risks = {
            "data_protection": "数据保护法规违规、隐私泄露风险、监管处罚",
            "anti_corruption": "反腐败法规违规、合规调查风险、声誉损失",
            "labor": "劳动法规违规、员工权益受损、劳动争议风险",
            "environmental": "环保法规违规、环境责任风险、可持续发展影响"
        }
        
        return compliance_risks.get(clause.category)
    
    def _calculate_urgency_score(self, clause: StandardClause, risk_level: MissingClauseRiskLevel) -> float:
        """计算紧急程度分数"""
        base_urgency = {
            ClauseImportanceLevel.CRITICAL: 0.9,
            ClauseImportanceLevel.HIGH: 0.7,
            ClauseImportanceLevel.MEDIUM: 0.5,
            ClauseImportanceLevel.LOW: 0.3
        }.get(clause.importance_level, 0.5)
        
        risk_multiplier = {
            MissingClauseRiskLevel.CRITICAL: 1.2,
            MissingClauseRiskLevel.HIGH: 1.0,
            MissingClauseRiskLevel.MEDIUM: 0.8,
            MissingClauseRiskLevel.LOW: 0.6
        }.get(risk_level, 1.0)
        
        # 法律强制要求的条款紧急程度更高
        legal_urgency_bonus = 0.2 if clause.legal_requirement == "mandatory" else 0.0
        
        urgency = min(1.0, base_urgency * risk_multiplier + legal_urgency_bonus)
        return urgency
    
    def _calculate_impact_score(self, clause: StandardClause, business_impact: BusinessImpactLevel) -> float:
        """计算影响程度分数"""
        impact_scores = {
            BusinessImpactLevel.SEVERE: 1.0,
            BusinessImpactLevel.MAJOR: 0.8,
            BusinessImpactLevel.MODERATE: 0.6,
            BusinessImpactLevel.MINOR: 0.4
        }
        
        return impact_scores.get(business_impact, 0.6)
    
    def _calculate_overall_priority(self,
                                  importance_level: ClauseImportanceLevel,
                                  risk_level: MissingClauseRiskLevel,
                                  business_impact: BusinessImpactLevel,
                                  urgency_score: float,
                                  impact_score: float) -> int:
        """计算整体优先级（1-10）"""
        
        # 重要性权重
        importance_weight = {
            ClauseImportanceLevel.CRITICAL: 4,
            ClauseImportanceLevel.HIGH: 3,
            ClauseImportanceLevel.MEDIUM: 2,
            ClauseImportanceLevel.LOW: 1
        }.get(importance_level, 2)
        
        # 风险权重
        risk_weight = {
            MissingClauseRiskLevel.CRITICAL: 3,
            MissingClauseRiskLevel.HIGH: 2,
            MissingClauseRiskLevel.MEDIUM: 1,
            MissingClauseRiskLevel.LOW: 0
        }.get(risk_level, 1)
        
        # 业务影响权重
        impact_weight = {
            BusinessImpactLevel.SEVERE: 3,
            BusinessImpactLevel.MAJOR: 2,
            BusinessImpactLevel.MODERATE: 1,
            BusinessImpactLevel.MINOR: 0
        }.get(business_impact, 1)
        
        # 综合计算优先级
        priority = (importance_weight + risk_weight + impact_weight + 
                   urgency_score * 2 + impact_score * 2)
        
        # 归一化到1-10
        normalized_priority = int(min(10, max(1, round(priority))))
        
        return normalized_priority
    
    def _generate_recommendations(self, clause: StandardClause, risk_level: MissingClauseRiskLevel) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 基础建议
        recommendations.append(f"建议添加{clause.name}：{clause.content}")
        
        # 基于风险级别的建议
        if risk_level == MissingClauseRiskLevel.CRITICAL:
            recommendations.append("此条款缺失存在严重风险，建议立即补充")
            recommendations.append("建议咨询专业法律顾问进行条款设计")
        elif risk_level == MissingClauseRiskLevel.HIGH:
            recommendations.append("此条款缺失存在较高风险，建议尽快补充")
            recommendations.append("可参考行业标准条款进行补充")
        
        # 基于条款类别的具体建议
        category_recommendations = {
            "payment": [
                "明确付款时间、方式和条件",
                "约定逾期付款的违约责任",
                "规定发票开具和送达要求"
            ],
            "liability": [
                "合理限制赔偿责任范围和上限",
                "明确免责条件和例外情形",
                "约定过错认定和举证责任"
            ],
            "termination": [
                "规定合同终止的条件和程序",
                "约定提前通知的时间要求",
                "明确终止后的权利义务处理"
            ]
        }
        
        if clause.category in category_recommendations:
            recommendations.extend(category_recommendations[clause.category])
        
        return recommendations
    
    def _generate_mitigation_strategies(self, clause: StandardClause, risk_level: MissingClauseRiskLevel) -> List[str]:
        """生成缓解策略"""
        strategies = []
        
        # 基于风险级别的缓解策略
        if risk_level in [MissingClauseRiskLevel.CRITICAL, MissingClauseRiskLevel.HIGH]:
            strategies.append("建议暂缓签署合同，先补充必要条款")
            strategies.append("如急需签署，可考虑签订补充协议")
        else:
            strategies.append("可在合同履行过程中通过补充协议完善")
        
        # 基于条款类别的缓解策略
        category_strategies = {
            "payment": [
                "建立付款跟踪机制，及时催收款项",
                "考虑要求对方提供付款担保",
                "购买应收账款保险降低坏账风险"
            ],
            "liability": [
                "购买相应的责任保险",
                "建立风险预警和应急处理机制",
                "加强合同履行过程中的风险管控"
            ],
            "confidentiality": [
                "建立内部信息安全管理制度",
                "与相关人员签署保密协议",
                "限制敏感信息的接触范围"
            ]
        }
        
        if clause.category in category_strategies:
            strategies.extend(category_strategies[clause.category])
        
        return strategies
    
    def _generate_contract_risk_profile(self,
                                      content: str,
                                      missing_analyses: List[MissingClauseAnalysis],
                                      found_clauses: List[str],
                                      all_clauses: Dict[str, StandardClause]) -> ContractRiskProfile:
        """生成合同风险画像"""
        
        # 1. 计算整体风险分数
        if not missing_analyses:
            overall_risk_score = 0.1  # 最低风险
        else:
            risk_scores = []
            for analysis in missing_analyses:
                clause = all_clauses[analysis.clause_id]
                # 基于重要性、风险级别和紧急度计算单个条款的风险贡献
                importance_multiplier = {
                    ClauseImportanceLevel.CRITICAL: 1.0,
                    ClauseImportanceLevel.HIGH: 0.8,
                    ClauseImportanceLevel.MEDIUM: 0.6,
                    ClauseImportanceLevel.LOW: 0.4
                }.get(clause.importance_level, 0.6)
                
                risk_multiplier = {
                    MissingClauseRiskLevel.CRITICAL: 1.0,
                    MissingClauseRiskLevel.HIGH: 0.8,
                    MissingClauseRiskLevel.MEDIUM: 0.6,
                    MissingClauseRiskLevel.LOW: 0.4
                }.get(analysis.risk_level, 0.6)
                
                clause_risk = importance_multiplier * risk_multiplier * analysis.urgency_score
                risk_scores.append(clause_risk)
            
            overall_risk_score = min(1.0, sum(risk_scores) / len(all_clauses))
        
        # 2. 风险分布统计
        risk_distribution = {
            "critical": sum(1 for a in missing_analyses if a.risk_level == MissingClauseRiskLevel.CRITICAL),
            "high": sum(1 for a in missing_analyses if a.risk_level == MissingClauseRiskLevel.HIGH),
            "medium": sum(1 for a in missing_analyses if a.risk_level == MissingClauseRiskLevel.MEDIUM),
            "low": sum(1 for a in missing_analyses if a.risk_level == MissingClauseRiskLevel.LOW)
        }
        
        # 3. 各类别风险分析
        category_risks = {}
        categories = set(a.clause_category for a in missing_analyses)
        for category in categories:
            category_analyses = [a for a in missing_analyses if a.clause_category == category]
            category_risk_scores = [a.urgency_score * a.impact_score for a in category_analyses]
            category_risks[category] = sum(category_risk_scores) / len(category_risk_scores) if category_risk_scores else 0.0
        
        # 4. 关键缺失条款
        critical_missing_clauses = [
            a.clause_name for a in missing_analyses 
            if a.risk_level == MissingClauseRiskLevel.CRITICAL
        ]
        
        # 5. 高优先级行动项
        high_priority_actions = []
        for analysis in missing_analyses:
            if analysis.overall_priority >= 8:
                high_priority_actions.extend(analysis.recommendations[:2])  # 取前两个建议
        
        # 6. 合规状态评估
        compliance_status = {}
        for category in ["payment", "liability", "data_protection", "labor"]:
            category_clauses = [a for a in missing_analyses if a.clause_category == category]
            mandatory_missing = any(
                all_clauses[a.clause_id].legal_requirement == "mandatory" 
                for a in category_clauses
            )
            compliance_status[category] = not mandatory_missing
        
        # 7. 各类风险评分
        business_continuity_risk = min(1.0, sum(
            a.impact_score for a in missing_analyses 
            if a.clause_category in ["termination", "service_level", "delivery"]
        ) / 3)
        
        legal_exposure_risk = min(1.0, sum(
            a.urgency_score for a in missing_analyses
            if a.clause_category in ["liability", "dispute_resolution", "compliance"]
        ) / 3)
        
        financial_loss_risk = min(1.0, sum(
            a.impact_score for a in missing_analyses
            if a.clause_category in ["payment", "penalty", "warranty"]
        ) / 3)
        
        return ContractRiskProfile(
            contract_id="",  # 由调用方设置
            overall_risk_score=overall_risk_score,
            risk_distribution=risk_distribution,
            category_risks=category_risks,
            critical_missing_clauses=critical_missing_clauses,
            high_priority_actions=high_priority_actions,
            compliance_status=compliance_status,
            business_continuity_risk=business_continuity_risk,
            legal_exposure_risk=legal_exposure_risk,
            financial_loss_risk=financial_loss_risk
        )