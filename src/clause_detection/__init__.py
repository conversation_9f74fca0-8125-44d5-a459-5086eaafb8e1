"""
条款检测模块

智能条款检测和缺失分析系统，支持：
- 多维度条款匹配（语义、关键词、正则、混合）
- 缺失条款检测和推荐
- 条款重要性评级
- AI增强的语义理解

主要组件：
- models: 数据模型定义
- detector: 条款检测引擎
- clause_library: 标准条款库管理
- semantic_matcher: 语义匹配器
"""

# 迁移自根目录的条款检测代码
from .models import (
    StandardClause,
    ClauseDetectionResult,
    ClauseRecommendation,
    MatchMethod,
    ClauseImportance
)

from .detector import ClauseDetectionEngine
from .clause_library import ClauseLibraryManager
from .semantic_matcher import SemanticMatcher, SemanticMatchResult

__all__ = [
    # 数据模型
    "StandardClause",
    "ClauseDetectionResult", 
    "ClauseRecommendation",
    "MatchMethod",
    "ClauseImportance",
    
    # 核心组件
    "ClauseDetectionEngine",
    "ClauseLibraryManager",
    "SemanticMatcher",
    "SemanticMatchResult"
]