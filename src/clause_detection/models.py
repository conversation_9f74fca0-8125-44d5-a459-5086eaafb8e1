"""
智能条款检测系统 - 数据模型定义
第4-6周任务：标准条款库和检测算法的数据模型
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any
from datetime import datetime
from enum import Enum

class ClauseCategory(str, Enum):
    """条款分类枚举"""
    BASIC_INFO = "basic_info"           # 基础信息条款
    RIGHTS_OBLIGATIONS = "rights_obligations"  # 权利义务条款
    PAYMENT_TERMS = "payment_terms"     # 付款条款
    DELIVERY_TERMS = "delivery_terms"   # 交付条款
    LIABILITY = "liability"             # 责任条款
    TERMINATION = "termination"         # 终止条款
    DISPUTE_RESOLUTION = "dispute_resolution"  # 争议解决条款
    CONFIDENTIALITY = "confidentiality" # 保密条款
    INTELLECTUAL_PROPERTY = "intellectual_property"  # 知识产权条款
    FORCE_MAJEURE = "force_majeure"     # 不可抗力条款
    MISCELLANEOUS = "miscellaneous"     # 其他条款

class ClauseImportance(str, Enum):
    """条款重要性等级"""
    CRITICAL = "critical"    # 关键条款（必须包含）
    IMPORTANT = "important"  # 重要条款（建议包含）
    OPTIONAL = "optional"    # 可选条款（可以包含）

class MatchMethod(str, Enum):
    """匹配方法枚举"""
    SEMANTIC = "semantic"        # 语义匹配
    KEYWORD = "keyword"          # 关键词匹配
    REGEX = "regex"             # 正则表达式匹配
    HYBRID = "hybrid"           # 混合匹配

class StandardClause(BaseModel):
    """标准条款模型"""
    id: Optional[int] = Field(None, description="条款ID")
    clause_name: str = Field(..., description="条款名称")
    clause_category: ClauseCategory = Field(..., description="条款分类")
    contract_types: List[str] = Field(..., description="适用的合同类型")
    importance_level: ClauseImportance = Field(..., description="重要性等级")
    
    # 条款内容
    standard_text: str = Field(..., description="标准条款文本")
    description: str = Field(..., description="条款描述")
    purpose: str = Field(..., description="条款目的")
    
    # 匹配规则
    keywords: List[str] = Field(default=[], description="关键词列表")
    regex_patterns: List[str] = Field(default=[], description="正则表达式模式")
    semantic_keywords: List[str] = Field(default=[], description="语义关键词")
    
    # 检测配置
    match_threshold: float = Field(0.7, ge=0.0, le=1.0, description="匹配阈值")
    match_methods: List[MatchMethod] = Field(default=[MatchMethod.HYBRID], description="匹配方法")
    
    # 推荐内容
    recommended_text: Optional[str] = Field(None, description="推荐条款文本")
    alternatives: List[str] = Field(default=[], description="替代条款文本")
    
    # 元数据
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    version: str = Field("1.0", description="版本号")
    source: Optional[str] = Field(None, description="条款来源")

class ClauseMatch(BaseModel):
    """条款匹配结果"""
    standard_clause_id: int = Field(..., description="标准条款ID")
    clause_name: str = Field(..., description="条款名称")
    matched_text: str = Field(..., description="匹配到的文本")
    match_score: float = Field(..., ge=0.0, le=1.0, description="匹配分数")
    match_method: MatchMethod = Field(..., description="匹配方法")
    
    # 位置信息
    start_position: int = Field(..., description="开始位置")
    end_position: int = Field(..., description="结束位置")
    paragraph_index: Optional[int] = Field(None, description="段落索引")
    
    # 匹配详情
    matched_keywords: List[str] = Field(default=[], description="匹配的关键词")
    confidence: float = Field(..., ge=0.0, le=1.0, description="置信度")
    match_reason: str = Field(..., description="匹配原因")

class MissingClause(BaseModel):
    """缺失条款信息"""
    standard_clause_id: int = Field(..., description="标准条款ID")
    clause_name: str = Field(..., description="条款名称")
    clause_category: ClauseCategory = Field(..., description="条款分类")
    importance_level: ClauseImportance = Field(..., description="重要性等级")
    
    # 缺失分析
    missing_reason: str = Field(..., description="缺失原因")
    impact_description: str = Field(..., description="缺失影响描述")
    risk_level: str = Field(..., description="风险等级")
    
    # 推荐信息
    recommended_text: str = Field(..., description="推荐条款文本")
    insertion_suggestion: str = Field(..., description="插入位置建议")
    alternatives: List[str] = Field(default=[], description="替代方案")

class ClauseDetectionResult(BaseModel):
    """条款检测结果"""
    contract_id: Optional[str] = Field(None, description="合同ID")
    contract_type: str = Field(..., description="合同类型")
    detection_time: datetime = Field(default_factory=datetime.now, description="检测时间")
    
    # 检测结果
    matched_clauses: List[ClauseMatch] = Field(default=[], description="匹配的条款")
    missing_clauses: List[MissingClause] = Field(default=[], description="缺失的条款")
    
    # 统计信息
    total_standard_clauses: int = Field(..., description="标准条款总数")
    matched_count: int = Field(..., description="匹配条款数量")
    missing_count: int = Field(..., description="缺失条款数量")
    
    # 评分
    completeness_score: float = Field(..., ge=0.0, le=1.0, description="完整性评分")
    compliance_score: float = Field(..., ge=0.0, le=1.0, description="合规性评分")
    overall_score: float = Field(..., ge=0.0, le=1.0, description="总体评分")
    
    # 处理信息
    processing_duration: float = Field(..., description="处理耗时（秒）")
    ai_calls_used: int = Field(0, description="使用的AI调用次数")
    cache_hits: int = Field(0, description="缓存命中次数")

class ClauseRecommendation(BaseModel):
    """条款推荐"""
    clause_name: str = Field(..., description="条款名称")
    recommended_text: str = Field(..., description="推荐文本")
    reason: str = Field(..., description="推荐理由")
    priority: int = Field(..., description="优先级")
    
    # 插入建议
    suggested_position: Optional[str] = Field(None, description="建议插入位置")
    context_hint: Optional[str] = Field(None, description="上下文提示")
    
    # 相关信息
    related_clauses: List[str] = Field(default=[], description="相关条款")
    legal_basis: Optional[str] = Field(None, description="法律依据")

class ClauseLibraryStats(BaseModel):
    """条款库统计信息"""
    total_clauses: int = Field(..., description="条款总数")
    clauses_by_category: Dict[str, int] = Field(default={}, description="按分类统计")
    clauses_by_importance: Dict[str, int] = Field(default={}, description="按重要性统计")
    clauses_by_contract_type: Dict[str, int] = Field(default={}, description="按合同类型统计")
    
    # 使用统计
    most_used_clauses: List[str] = Field(default=[], description="最常用条款")
    least_used_clauses: List[str] = Field(default=[], description="最少用条款")
    
    # 时间信息
    last_updated: datetime = Field(default_factory=datetime.now, description="最后更新时间")
    library_version: str = Field("1.0", description="条款库版本")