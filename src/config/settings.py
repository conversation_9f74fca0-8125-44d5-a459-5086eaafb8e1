"""
系统配置管理

基于Pydantic的配置管理，支持环境变量和配置文件
"""

import os
from typing import Optional, List
from pydantic import BaseModel, Field
from functools import lru_cache

# 尝试导入pydantic-settings，如果失败则使用基本配置
try:
    from pydantic_settings import BaseSettings
except ImportError:
    try:
        from pydantic import BaseSettings
    except ImportError:
        # 如果都失败，创建一个简单的基类
        class BaseSettings(BaseModel):
            class Config:
                env_file = ".env"
                env_file_encoding = "utf-8"
                case_sensitive = False

class Settings(BaseSettings):
    """系统配置类"""
    
    # 应用基础配置
    app_name: str = Field("AI合同审核系统", description="应用名称")
    app_version: str = Field("1.0.0", description="应用版本")
    debug: bool = Field(False, description="调试模式")
    environment: str = Field("development", description="运行环境")
    
    # 服务器配置
    host: str = Field("0.0.0.0", description="服务器地址")
    port: int = Field(8000, description="服务器端口")
    workers: int = Field(1, description="工作进程数")
    
    # 数据库配置
    database_url: str = Field("sqlite:///./contract_audit.db", description="数据库连接URL")
    database_echo: bool = Field(False, description="数据库SQL日志")
    
    # Redis配置
    redis_url: str = Field("redis://localhost:6379/0", description="Redis连接URL")
    redis_password: Optional[str] = Field(None, description="Redis密码")
    
    # AI服务配置
    qwen_api_key: Optional[str] = Field(None, description="千问API密钥")
    qwen_api_base: str = Field("https://dashscope.aliyuncs.com/compatible-mode/v1", description="千问API基础URL")
    qwen_model: str = Field("qwen-plus", description="千问模型名称")
    qwen_max_tokens: int = Field(2000, description="千问最大令牌数")
    qwen_temperature: float = Field(0.7, description="千问温度参数")
    
    # 文件存储配置
    upload_dir: str = Field("./uploads", description="上传文件目录")
    temp_dir: str = Field("./temp", description="临时文件目录")
    max_file_size: int = Field(50 * 1024 * 1024, description="最大文件大小(字节)")
    allowed_extensions: List[str] = Field([".docx", ".doc"], description="允许的文件扩展名")
    
    # 安全配置
    secret_key: str = Field("your-secret-key-here", description="JWT密钥")
    access_token_expire_minutes: int = Field(30, description="访问令牌过期时间(分钟)")
    algorithm: str = Field("HS256", description="JWT算法")
    
    # 日志配置
    log_level: str = Field("INFO", description="日志级别")
    log_file: str = Field("./logs/app.log", description="日志文件路径")
    log_max_size: int = Field(10 * 1024 * 1024, description="日志文件最大大小")
    log_backup_count: int = Field(5, description="日志备份数量")
    
    # 缓存配置
    cache_ttl: int = Field(3600, description="缓存TTL(秒)")
    cache_max_size: int = Field(1000, description="缓存最大条目数")
    
    # API配置
    api_prefix: str = Field("/api/v1", description="API前缀")
    cors_origins: List[str] = Field(["*"], description="CORS允许的源")
    
    # 业务配置
    min_confidence: float = Field(0.6, description="最小置信度")
    max_concurrent_analyses: int = Field(10, description="最大并发分析数")
    analysis_timeout: int = Field(300, description="分析超时时间(秒)")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    return Settings()

# 全局配置实例
settings = get_settings()

# 确保必要的目录存在
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        settings.upload_dir,
        settings.temp_dir,
        os.path.dirname(settings.log_file)
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

# 初始化时创建目录
ensure_directories()