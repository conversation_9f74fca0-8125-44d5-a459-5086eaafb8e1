"""
FastAPI应用主入口

整合所有API路由和中间件，提供统一的API服务
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import time
import logging

from src.config import get_settings
from src.routes import contracts, analysis, config, reports
from .middleware.logging import LoggingMiddleware
# 导入新的风险分析API
from src.risk_analysis.api import router as risk_analysis_router

logger = logging.getLogger(__name__)
settings = get_settings()

def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="基于AI的智能合同审核系统",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None
    )
    
    # 添加中间件
    setup_middleware(app)
    
    # 添加路由
    setup_routes(app)
    
    # 添加事件处理器
    setup_events(app)
    
    return app

def setup_middleware(app: FastAPI):
    """设置中间件"""
    
    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Gzip压缩中间件
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # 自定义日志中间件
    app.add_middleware(LoggingMiddleware)
    
    # 请求处理时间中间件
    @app.middleware("http")
    async def add_process_time_header(request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        return response

def setup_routes(app: FastAPI):
    """设置API路由"""
    
    # 健康检查
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "version": settings.app_version}
    
    # API路由
    app.include_router(
        contracts.router,
        prefix=f"{settings.api_prefix}/contracts",
        tags=["合同管理"]
    )
    
    app.include_router(
        analysis.router,
        prefix=f"{settings.api_prefix}/analysis", 
        tags=["合同分析"]
    )
    
    app.include_router(
        config.router,
        prefix=f"{settings.api_prefix}/config",
        tags=["配置管理"]
    )
    
    app.include_router(
        reports.router,
        prefix=f"{settings.api_prefix}/reports",
        tags=["报告管理"]
    )
    
    # 新增：风险分析API路由
    app.include_router(
        risk_analysis_router,
        tags=["风险分析"]
    )

def setup_events(app: FastAPI):
    """设置应用事件处理器"""
    
    @app.on_event("startup")
    async def startup_event():
        logger.info(f"启动 {settings.app_name} v{settings.app_version}")
        logger.info(f"运行环境: {settings.environment}")
        logger.info(f"调试模式: {settings.debug}")
        
        # 初始化风险知识库
        try:
            from src.risk_analysis.knowledge_base import risk_knowledge_base
            logger.info(f"风险知识库初始化完成，加载了 {len(risk_knowledge_base.rules)} 个规则")
        except Exception as e:
            logger.error(f"风险知识库初始化失败: {e}")
    
    @app.on_event("shutdown")
    async def shutdown_event():
        logger.info(f"关闭 {settings.app_name}")

# 全局异常处理器
def setup_exception_handlers(app: FastAPI):
    """设置全局异常处理器"""
    
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        logger.error(f"全局异常: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": "内部服务器错误",
                "message": str(exc) if settings.debug else "服务暂时不可用",
                "request_id": getattr(request.state, "request_id", None)
            }
        )

# 创建应用实例
app = create_app()
setup_exception_handlers(app)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.api.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        workers=1 if settings.debug else settings.workers
    )
