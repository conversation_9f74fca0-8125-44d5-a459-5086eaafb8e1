"""
AI合同审核系统 - 核心源代码包

这是AI合同审核系统的核心源代码包，包含以下主要模块：

- config: 配置管理
- core: 核心业务逻辑
- element_extraction: 要素提取模块
- clause_detection: 条款检测模块
- risk_analysis: 风险分析模块
- api: API接口层
- database: 数据库相关
- utils: 工具函数

版本: v1.0
作者: AI合同审核系统开发团队
"""

__version__ = "1.0.0"
__author__ = "AI Contract Audit Team"

# 导出主要模块
from . import config
from . import core
from . import element_extraction
from . import clause_detection
from . import risk_analysis
from . import utils

__all__ = [
    "config",
    "core", 
    "element_extraction",
    "clause_detection",
    "risk_analysis",
    "utils"
]