"""
智能风险分析器 - 核心风险识别引擎
整合规则匹配、AI分析和风险评估功能
"""

import asyncio
import logging
import time
import re
import uuid
import json
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime

from .models import (
    RiskRule, RiskPoint, RiskAnalysisResult, RiskAssessment,
    RiskCategory, RiskLevel, RiskImpact, RiskProbability,
    RiskMitigationPlan
)
from .knowledge_base import risk_knowledge_base

logger = logging.getLogger(__name__)

class RiskAnalyzer:
    """智能风险分析器"""
    
    def __init__(self, qwen_client=None):
        """
        初始化风险分析器
        
        Args:
            qwen_client: 千问3 API客户端
        """
        self.qwen_client = qwen_client
        self.knowledge_base = risk_knowledge_base
        
        # 分析配置
        self.config = {
            'min_confidence': 0.6,
            'enable_ai_verification': True,
            'max_ai_calls': 15,
            'context_window': 200,
            'risk_aggregation': True,
            'semantic_analysis_threshold': 0.7,
            'batch_size': 10
        }
        
        # 风险评分权重
        self.risk_weights = {
            RiskLevel.CRITICAL: 10.0,
            RiskLevel.HIGH: 7.5,
            RiskLevel.MEDIUM: 5.0,
            RiskLevel.LOW: 2.5,
            RiskLevel.NEGLIGIBLE: 1.0
        }
        
        # 影响程度权重
        self.impact_weights = {
            RiskImpact.SEVERE: 1.3,
            RiskImpact.MAJOR: 1.15,
            RiskImpact.MODERATE: 1.0,
            RiskImpact.MINOR: 0.85,
            RiskImpact.MINIMAL: 0.7
        }
        
        # 概率权重
        self.probability_weights = {
            RiskProbability.VERY_HIGH: 1.25,
            RiskProbability.HIGH: 1.1,
            RiskProbability.MEDIUM: 1.0,
            RiskProbability.LOW: 0.85,
            RiskProbability.VERY_LOW: 0.7
        }
        
        # 统计信息
        self.stats = {
            'total_analyses': 0,
            'ai_calls_used': 0,
            'average_processing_time': 0.0,
            'detection_accuracy': 0.0
        }
    
    async def analyze_risks(
        self,
        contract_text: str,
        contract_type: str = "general",
        risk_categories: Optional[List[RiskCategory]] = None,
        min_risk_level: RiskLevel = RiskLevel.LOW,
        enable_ai_analysis: bool = True,
        detailed_analysis: bool = False
    ) -> RiskAnalysisResult:
        """
        执行完整的风险分析
        
        Args:
            contract_text: 合同文本
            contract_type: 合同类型
            risk_categories: 关注的风险分类
            min_risk_level: 最低风险等级
            enable_ai_analysis: 是否启用AI分析
            detailed_analysis: 是否进行详细分析
            
        Returns:
            风险分析结果
        """
        start_time = time.time()
        ai_calls_count = 0
        
        logger.info(f"开始风险分析 - 合同类型: {contract_type}, AI分析: {enable_ai_analysis}")
        
        try:
            # 1. 获取适用的风险规则
            applicable_rules = self._get_applicable_rules(
                contract_type, risk_categories, min_risk_level
            )
            logger.info(f"找到 {len(applicable_rules)} 个适用风险规则")
            
            # 2. 执行风险检测
            risk_points, ai_calls = await self._detect_risks(
                contract_text, applicable_rules, contract_type, enable_ai_analysis
            )
            ai_calls_count += ai_calls
            
            # 3. AI增强分析（如果启用详细分析）
            if detailed_analysis and enable_ai_analysis and self.qwen_client:
                enhanced_risks, enhanced_calls = await self._enhanced_ai_analysis(
                    contract_text, risk_points, contract_type
                )
                risk_points.extend(enhanced_risks)
                ai_calls_count += enhanced_calls
            
            # 4. 风险评估和聚合
            risk_assessment = self._assess_risks(risk_points, contract_text)
            
            # 5. 构建分析结果
            processing_duration = time.time() - start_time
            
            result = RiskAnalysisResult(
                contract_type=contract_type,
                risk_points=risk_points,
                risk_assessment=risk_assessment,
                processing_duration=processing_duration,
                rules_applied=len(applicable_rules),
                ai_calls_used=ai_calls_count,
                detection_confidence=self._calculate_detection_confidence(risk_points),
                coverage_score=self._calculate_coverage_score(applicable_rules, risk_points)
            )
            
            # 更新统计信息
            self._update_stats(processing_duration, ai_calls_count)
            
            logger.info(f"风险分析完成 - 耗时: {processing_duration:.2f}秒, AI调用: {ai_calls_count}次")
            logger.info(f"识别风险: {len(risk_points)}, 整体风险等级: {risk_assessment.overall_risk_level}")
            
            return result
            
        except Exception as e:
            logger.error(f"风险分析失败: {e}")
            raise
    
    def _get_applicable_rules(
        self,
        contract_type: str,
        risk_categories: Optional[List[RiskCategory]],
        min_risk_level: RiskLevel
    ) -> List[RiskRule]:
        """获取适用的风险规则"""
        
        # 获取合同类型相关的规则
        rules = self.knowledge_base.get_rules_by_contract_type(contract_type)
        
        # 按风险分类过滤
        if risk_categories:
            rules = [rule for rule in rules if rule.risk_category in risk_categories]
        
        # 按风险等级过滤
        min_weight = self.risk_weights[min_risk_level]
        rules = [rule for rule in rules 
                if self.risk_weights[rule.base_risk_level] >= min_weight]
        
        # 按优先级排序
        rules.sort(key=lambda x: (
            self.risk_weights[x.base_risk_level],
            x.impact_level.value,
            x.probability.value
        ), reverse=True)
        
        return rules
    
    async def _detect_risks(
        self,
        contract_text: str,
        rules: List[RiskRule],
        contract_type: str,
        enable_ai_analysis: bool = True
    ) -> Tuple[List[RiskPoint], int]:
        """检测风险点"""
        
        all_risk_points = []
        ai_calls_count = 0
        
        # 批量处理规则以提高效率
        for i in range(0, len(rules), self.config['batch_size']):
            batch_rules = rules[i:i + self.config['batch_size']]
            
            for rule in batch_rules:
                # 执行规则匹配
                rule_risks = await self._apply_risk_rule(contract_text, rule)
                all_risk_points.extend(rule_risks)
                
                # AI语义分析（如果启用且有语义模式）
                if (enable_ai_analysis and self.qwen_client and 
                    rule.semantic_patterns and ai_calls_count < self.config['max_ai_calls']):
                    
                    semantic_risks = await self._semantic_risk_detection(contract_text, rule)
                    all_risk_points.extend(semantic_risks)
                    ai_calls_count += 1
        
        # AI增强验证（如果启用）
        if (enable_ai_analysis and self.qwen_client and 
            self.config['enable_ai_verification'] and all_risk_points and
            ai_calls_count < self.config['max_ai_calls']):
            
            verified_risks = await self._ai_verify_risks(
                contract_text, all_risk_points, contract_type
            )
            all_risk_points = verified_risks
            ai_calls_count += 1
        
        # 去重和聚合
        aggregated_risks = self._aggregate_risks(all_risk_points)
        
        # 按风险评分排序
        aggregated_risks.sort(key=lambda x: x.risk_score, reverse=True)
        
        return aggregated_risks, ai_calls_count
    
    async def _apply_risk_rule(
        self,
        contract_text: str,
        rule: RiskRule
    ) -> List[RiskPoint]:
        """应用单个风险规则"""
        
        risk_points = []
        
        # 关键词匹配
        if rule.keywords:
            keyword_matches = self._keyword_risk_detection(contract_text, rule)
            risk_points.extend(keyword_matches)
        
        # 正则表达式匹配
        if rule.regex_patterns:
            regex_matches = self._regex_risk_detection(contract_text, rule)
            risk_points.extend(regex_matches)
        
        return risk_points
    
    def _keyword_risk_detection(
        self,
        contract_text: str,
        rule: RiskRule
    ) -> List[RiskPoint]:
        """基于关键词的风险检测"""
        
        risk_points = []
        text_lower = contract_text.lower()
        
        for keyword in rule.keywords:
            keyword_lower = keyword.lower()
            
            # 查找所有匹配位置
            start = 0
            while True:
                pos = text_lower.find(keyword_lower, start)
                if pos == -1:
                    break
                
                # 提取上下文
                context_start = max(0, pos - self.config['context_window'])
                context_end = min(len(contract_text), pos + len(keyword) + self.config['context_window'])
                context = contract_text[context_start:context_end]
                matched_text = contract_text[pos:pos + len(keyword)]
                
                # 计算风险评分
                risk_score = self._calculate_risk_score(rule, context, "keyword")
                
                if risk_score >= self.config['min_confidence'] * 10:
                    risk_point = self._create_risk_point(
                        rule, matched_text, pos, pos + len(keyword),
                        context, risk_score, "keyword_matching"
                    )
                    risk_points.append(risk_point)
                
                start = pos + 1
        
        return risk_points
    
    def _regex_risk_detection(
        self,
        contract_text: str,
        rule: RiskRule
    ) -> List[RiskPoint]:
        """基于正则表达式的风险检测"""
        
        risk_points = []
        
        for pattern in rule.regex_patterns:
            try:
                matches = re.finditer(pattern, contract_text, re.IGNORECASE | re.MULTILINE)
                
                for match in matches:
                    # 提取上下文
                    context_start = max(0, match.start() - self.config['context_window'])
                    context_end = min(len(contract_text), match.end() + self.config['context_window'])
                    context = contract_text[context_start:context_end]
                    
                    # 计算风险评分
                    risk_score = self._calculate_risk_score(rule, context, "regex")
                    
                    if risk_score >= self.config['min_confidence'] * 10:
                        risk_point = self._create_risk_point(
                            rule, match.group(), match.start(), match.end(),
                            context, risk_score, "regex_matching"
                        )
                        risk_points.append(risk_point)
                        
            except re.error as e:
                logger.error(f"正则表达式错误 {pattern}: {e}")
        
        return risk_points
    
    async def _semantic_risk_detection(
        self,
        contract_text: str,
        rule: RiskRule
    ) -> List[RiskPoint]:
        """基于语义模式的风险检测"""
        
        if not self.qwen_client:
            return []
        
        try:
            # 构建语义检测提示
            prompt = self._build_semantic_risk_prompt(contract_text, rule)
            
            # 调用AI API
            response = await self.qwen_client._call_api(prompt, max_tokens=800)
            
            # 解析响应
            risk_points = self._parse_semantic_risk_response(response, rule, contract_text)
            
            return risk_points
            
        except Exception as e:
            logger.error(f"语义风险检测失败 {rule.rule_name}: {e}")
            return []
    
    def _build_semantic_risk_prompt(self, contract_text: str, rule: RiskRule) -> str:
        """构建语义风险检测提示"""
        return f"""
请分析以下合同文本，识别与指定风险相关的内容。

风险规则信息：
- 风险名称：{rule.rule_name}
- 风险分类：{rule.risk_category.value}
- 风险描述：{rule.description}
- 风险说明：{rule.risk_explanation}
- 语义模式：{', '.join(rule.semantic_patterns)}

合同文本（前3000字符）：
{contract_text[:3000]}

请仔细分析合同文本，识别可能存在的风险点。
注意：
1. 重点关注风险的实质内容而非表面文字
2. 考虑条款的缺失、模糊或不当表述
3. 评估风险的严重程度和影响范围

请以JSON格式回复：
{{
    "risks_found": [
        {{
            "matched_text": "相关文本内容",
            "start_position": 开始位置,
            "end_position": 结束位置,
            "risk_score": 7.5,
            "confidence": 0.85,
            "risk_reason": "风险识别原因",
            "severity_assessment": "严重程度评估"
        }}
    ]
}}

如果没有发现相关风险，请返回空的risks_found数组。
"""
    
    def _parse_semantic_risk_response(
        self,
        response: str,
        rule: RiskRule,
        contract_text: str
    ) -> List[RiskPoint]:
        """解析语义风险检测响应"""
        
        try:
            # 提取JSON部分
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                result = json.loads(json_str)
                
                risk_points = []
                for risk_data in result.get('risks_found', []):
                    risk_score = risk_data.get('risk_score', 5.0)
                    confidence = risk_data.get('confidence', 0.7)
                    
                    if confidence >= self.config['min_confidence']:
                        matched_text = risk_data.get('matched_text', '')
                        start_pos = risk_data.get('start_position', 0)
                        end_pos = risk_data.get('end_position', len(matched_text))
                        
                        risk_point = self._create_risk_point(
                            rule, matched_text, start_pos, end_pos,
                            matched_text, risk_score, "semantic_analysis",
                            confidence, risk_data.get('risk_reason', '')
                        )
                        risk_points.append(risk_point)
                
                return risk_points
                
        except Exception as e:
            logger.error(f"解析语义风险响应失败: {e}")
        
        return []
    
    async def _enhanced_ai_analysis(
        self,
        contract_text: str,
        existing_risks: List[RiskPoint],
        contract_type: str
    ) -> Tuple[List[RiskPoint], int]:
        """增强AI分析，发现潜在的隐性风险"""
        
        if not self.qwen_client:
            return [], 0
        
        try:
            # 构建增强分析提示
            existing_risk_summary = [
                f"- {risk.risk_name}: {risk.description}" 
                for risk in existing_risks[:5]  # 限制数量
            ]
            
            prompt = f"""
请对以下合同进行深度风险分析，重点识别可能被忽略的隐性风险。

合同类型：{contract_type}

已识别的风险：
{chr(10).join(existing_risk_summary)}

合同文本（前4000字符）：
{contract_text[:4000]}

请从以下角度进行深度分析：
1. 条款缺失风险：应有但未明确规定的重要条款
2. 表述模糊风险：可能引起歧义或争议的表述
3. 权利义务不对等风险：双方权利义务明显不平衡
4. 履约风险：可能影响合同履行的潜在因素
5. 法律合规风险：可能违反相关法律法规的条款

请以JSON格式回复：
{{
    "hidden_risks": [
        {{
            "risk_name": "风险名称",
            "risk_category": "legal/commercial/financial/operational",
            "risk_level": "critical/high/medium/low",
            "description": "详细描述",
            "matched_text": "相关文本或'条款缺失'",
            "risk_score": 6.5,
            "confidence": 0.8,
            "analysis_reason": "分析原因"
        }}
    ]
}}
"""
            
            response = await self.qwen_client._call_api(prompt, max_tokens=1200)
            
            # 解析增强分析结果
            enhanced_risks = self._parse_enhanced_analysis_response(response, contract_text)
            
            return enhanced_risks, 1
            
        except Exception as e:
            logger.error(f"增强AI分析失败: {e}")
            return [], 0
    
    def _parse_enhanced_analysis_response(
        self,
        response: str,
        contract_text: str
    ) -> List[RiskPoint]:
        """解析增强分析响应"""
        
        try:
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                result = json.loads(json_str)
                
                risk_points = []
                for risk_data in result.get('hidden_risks', []):
                    confidence = risk_data.get('confidence', 0.7)
                    
                    if confidence >= self.config['semantic_analysis_threshold']:
                        # 创建虚拟规则用于增强分析的风险
                        virtual_rule = self._create_virtual_rule(risk_data)
                        
                        matched_text = risk_data.get('matched_text', '条款缺失或表述模糊')
                        risk_score = risk_data.get('risk_score', 5.0)
                        
                        risk_point = self._create_risk_point(
                            virtual_rule, matched_text, 0, len(matched_text),
                            matched_text, risk_score, "enhanced_ai_analysis",
                            confidence, risk_data.get('analysis_reason', '')
                        )
                        risk_points.append(risk_point)
                
                return risk_points
                
        except Exception as e:
            logger.error(f"解析增强分析响应失败: {e}")
        
        return []
    
    def _create_virtual_rule(self, risk_data: Dict[str, Any]) -> RiskRule:
        """为AI增强分析创建虚拟规则"""
        
        risk_category = RiskCategory(risk_data.get('risk_category', 'legal'))
        risk_level = RiskLevel(risk_data.get('risk_level', 'medium'))
        
        return RiskRule(
            id=0,  # 虚拟ID
            rule_name=risk_data.get('risk_name', '未知风险'),
            risk_category=risk_category,
            contract_types=['general'],
            base_risk_level=risk_level,
            impact_level=RiskImpact.MODERATE,
            probability=RiskProbability.MEDIUM,
            description=risk_data.get('description', ''),
            risk_explanation=risk_data.get('analysis_reason', ''),
            potential_consequences=[],
            mitigation_strategies=[],
            recommended_actions=[]
        )
    
    def _create_risk_point(
        self,
        rule: RiskRule,
        matched_text: str,
        start_pos: int,
        end_pos: int,
        context: str,
        risk_score: float,
        detection_method: str,
        confidence: Optional[float] = None,
        additional_description: str = ""
    ) -> RiskPoint:
        """创建风险点实例"""
        
        if confidence is None:
            confidence = min(risk_score / 10, 1.0)
        
        description = rule.description
        if additional_description:
            description += f" - {additional_description}"
        
        return RiskPoint(
            id=str(uuid.uuid4()),
            rule_id=rule.id,
            risk_name=rule.rule_name,
            risk_category=rule.risk_category,
            risk_level=rule.base_risk_level,
            matched_text=matched_text,
            start_position=start_pos,
            end_position=end_pos,
            context=context,
            impact_level=rule.impact_level,
            probability=rule.probability,
            risk_score=risk_score,
            confidence=confidence,
            description=description,
            potential_consequences=rule.potential_consequences,
            mitigation_strategies=rule.mitigation_strategies,
            recommended_actions=rule.recommended_actions,
            urgency_level=self._determine_urgency(rule.base_risk_level),
            legal_basis=rule.legal_basis,
            detection_method=detection_method
        )
    
    def _calculate_risk_score(
        self,
        rule: RiskRule,
        context: str,
        detection_method: str
    ) -> float:
        """计算风险评分"""
        
        # 基础评分
        base_score = self.risk_weights[rule.base_risk_level]
        
        # 影响程度调整
        base_score *= self.impact_weights[rule.impact_level]
        
        # 概率调整
        base_score *= self.probability_weights[rule.probability]
        
        # 检测方法调整
        method_confidence = {
            "keyword": 0.8,
            "regex": 0.9,
            "semantic": 1.0,
            "enhanced_ai": 0.95
        }
        base_score *= method_confidence.get(detection_method.split('_')[0], 0.8)
        
        # 上下文相关性调整
        context_bonus = self._calculate_context_relevance(rule, context)
        base_score += context_bonus
        
        return min(base_score, 10.0)
    
    def _calculate_context_relevance(self, rule: RiskRule, context: str) -> float:
        """计算上下文相关性加分"""
        
        context_lower = context.lower()
        relevance_score = 0.0
        
        # 检查相关关键词
        for keyword in rule.keywords:
            if keyword.lower() in context_lower:
                relevance_score += 0.1
        
        # 检查语义模式
        for pattern in rule.semantic_patterns:
            if pattern.lower() in context_lower:
                relevance_score += 0.2
        
        return min(relevance_score, 1.0)
    
    def _determine_urgency(self, risk_level: RiskLevel) -> str:
        """确定紧急程度"""
        urgency_map = {
            RiskLevel.CRITICAL: "立即处理",
            RiskLevel.HIGH: "优先处理",
            RiskLevel.MEDIUM: "及时处理",
            RiskLevel.LOW: "适时处理",
            RiskLevel.NEGLIGIBLE: "可延后处理"
        }
        return urgency_map[risk_level]
    
    async def _ai_verify_risks(
        self,
        contract_text: str,
        risk_points: List[RiskPoint],
        contract_type: str
    ) -> List[RiskPoint]:
        """AI验证风险点"""
        
        if not risk_points or not self.qwen_client:
            return risk_points
        
        try:
            # 构建验证提示
            risk_summaries = []
            for i, risk in enumerate(risk_points[:10]):  # 限制验证数量
                risk_summaries.append({
                    "index": i,
                    "risk_name": risk.risk_name,
                    "matched_text": risk.matched_text[:200],  # 限制长度
                    "risk_score": risk.risk_score,
                    "detection_method": risk.detection_method
                })
            
            prompt = f"""
请验证以下识别的风险点是否真实有效：

合同类型：{contract_type}

识别的风险点：
{json.dumps(risk_summaries, ensure_ascii=False, indent=2)}

合同文本（前2000字符）：
{contract_text[:2000]}

请对每个风险点进行验证，判断是否为真实风险。

请以JSON格式回复：
{{
    "verified_risks": [
        {{
            "risk_index": 0,
            "is_valid": true,
            "confidence": 0.85,
            "adjusted_score": 7.5,
            "verification_reason": "验证理由"
        }}
    ]
}}
"""
            
            response = await self.qwen_client._call_api(prompt, max_tokens=1000)
            
            # 解析验证结果
            verified_risks = self._parse_verification_response(response, risk_points)
            return verified_risks
            
        except Exception as e:
            logger.error(f"AI风险验证失败: {e}")
            return risk_points
    
    def _parse_verification_response(
        self,
        response: str,
        risk_points: List[RiskPoint]
    ) -> List[RiskPoint]:
        """解析AI验证响应"""
        
        try:
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                result = json.loads(json_str)
                
                verified_risks = []
                verification_results = {
                    item['risk_index']: item 
                    for item in result.get('verified_risks', [])
                }
                
                for i, risk in enumerate(risk_points):
                    verification = verification_results.get(i)
                    
                    if verification and verification.get('is_valid', True):
                        # 调整风险评分
                        if 'adjusted_score' in verification:
                            risk.risk_score = min(verification['adjusted_score'], 10.0)
                        
                        # 调整置信度
                        if 'confidence' in verification:
                            risk.confidence = min(verification['confidence'], 1.0)
                        
                        # 添加验证信息
                        if 'verification_reason' in verification:
                            risk.description += f" (AI验证: {verification['verification_reason']})"
                        
                        verified_risks.append(risk)
                
                return verified_risks
                
        except Exception as e:
            logger.error(f"解析验证响应失败: {e}")
        
        return risk_points
    
    def _aggregate_risks(self, risk_points: List[RiskPoint]) -> List[RiskPoint]:
        """聚合相似的风险点"""
        
        if not self.config['risk_aggregation']:
            return risk_points
        
        aggregated = []
        processed_indices = set()
        
        for i, risk1 in enumerate(risk_points):
            if i in processed_indices:
                continue
            
            similar_risks = [risk1]
            processed_indices.add(i)
            
            # 查找相似风险
            for j, risk2 in enumerate(risk_points[i+1:], i+1):
                if j in processed_indices:
                    continue
                
                if self._are_risks_similar(risk1, risk2):
                    similar_risks.append(risk2)
                    processed_indices.add(j)
            
            # 合并相似风险
            if len(similar_risks) > 1:
                merged_risk = self._merge_similar_risks(similar_risks)
                aggregated.append(merged_risk)
            else:
                aggregated.append(risk1)
        
        return aggregated
    
    def _are_risks_similar(self, risk1: RiskPoint, risk2: RiskPoint) -> bool:
        """判断两个风险是否相似"""
        
        # 相同规则ID
        if risk1.rule_id == risk2.rule_id:
            # 检查位置重叠
            overlap = min(risk1.end_position, risk2.end_position) - max(risk1.start_position, risk2.start_position)
            if overlap > 0:
                return True
        
        # 相同风险类别和相似描述
        if (risk1.risk_category == risk2.risk_category and
            risk1.risk_name == risk2.risk_name):
            return True
        
        return False
    
    def _merge_similar_risks(self, risks: List[RiskPoint]) -> RiskPoint:
        """合并相似风险"""
        
        # 选择评分最高的作为主风险
        main_risk = max(risks, key=lambda x: x.risk_score)
        
        # 合并文本和位置
        all_texts = [risk.matched_text for risk in risks]
        merged_text = " | ".join(set(all_texts))
        
        min_pos = min(risk.start_position for risk in risks)
        max_pos = max(risk.end_position for risk in risks)
        
        # 更新主风险信息
        main_risk.matched_text = merged_text
        main_risk.start_position = min_pos
        main_risk.end_position = max_pos
        main_risk.description += f" (合并了{len(risks)}个相似风险点)"
        
        return main_risk
    
    def _assess_risks(self, risk_points: List[RiskPoint], contract_text: str) -> RiskAssessment:
        """评估整体风险"""
        
        if not risk_points:
            return RiskAssessment(
                overall_risk_level=RiskLevel.NEGLIGIBLE,
                total_risk_score=0.0,
                risk_distribution={},
                total_risks=0,
                risks_by_category={},
                risks_by_level={}
            )
        
        # 计算总风险评分
        total_score = sum(risk.risk_score * risk.confidence for risk in risk_points)
        normalized_score = min(total_score / len(risk_points), 10.0)
        
        # 确定整体风险等级
        overall_level = self._determine_overall_risk_level(normalized_score, risk_points)
        
        # 统计风险分布
        risks_by_category = {}
        risks_by_level = {}
        category_risks = {}
        
        for risk in risk_points:
            # 按分类统计
            category = risk.risk_category.value
            risks_by_category[category] = risks_by_category.get(category, 0) + 1
            
            # 按等级统计
            level = risk.risk_level.value
            risks_by_level[level] = risks_by_level.get(level, 0) + 1
            
            # 分类风险详情
            if category not in category_risks:
                category_risks[category] = {
                    'count': 0,
                    'max_score': 0.0,
                    'avg_score': 0.0,
                    'risks': []
                }
            
            category_risks[category]['count'] += 1
            category_risks[category]['max_score'] = max(
                category_risks[category]['max_score'], risk.risk_score
            )
            category_risks[category]['risks'].append({
                'name': risk.risk_name,
                'score': risk.risk_score,
                'level': risk.risk_level.value
            })
        
        # 计算平均分
        for category_data in category_risks.values():
            if category_data['risks']:
                category_data['avg_score'] = sum(
                    r['score'] for r in category_data['risks']
                ) / len(category_data['risks'])
        
        # 分离关键风险
        critical_risks = [r for r in risk_points if r.risk_level == RiskLevel.CRITICAL]
        high_risks = [r for r in risk_points if r.risk_level == RiskLevel.HIGH]
        
        # 生成优先措施建议
        priority_actions = self._generate_priority_actions(risk_points)
        risk_mitigation_plan = self._generate_mitigation_plan(risk_points)
        
        return RiskAssessment(
            overall_risk_level=overall_level,
            total_risk_score=normalized_score,
            risk_distribution=risks_by_level,
            category_risks=category_risks,
            critical_risks=critical_risks,
            high_risks=high_risks,
            total_risks=len(risk_points),
            risks_by_category=risks_by_category,
            risks_by_level=risks_by_level,
            priority_actions=priority_actions,
            risk_mitigation_plan=risk_mitigation_plan
        )
    
    def _determine_overall_risk_level(
        self, 
        total_score: float, 
        risk_points: List[RiskPoint]
    ) -> RiskLevel:
        """确定整体风险等级"""
        
        # 检查是否有严重风险
        critical_count = sum(1 for r in risk_points if r.risk_level == RiskLevel.CRITICAL)
        high_count = sum(1 for r in risk_points if r.risk_level == RiskLevel.HIGH)
        
        if critical_count > 0:
            return RiskLevel.CRITICAL
        elif high_count >= 3 or total_score >= 8.0:
            return RiskLevel.HIGH
        elif high_count >= 1 or total_score >= 6.0:
            return RiskLevel.MEDIUM
        elif total_score >= 3.0:
            return RiskLevel.LOW
        else:
            return RiskLevel.NEGLIGIBLE
    
    def _generate_priority_actions(self, risk_points: List[RiskPoint]) -> List[str]:
        """生成优先措施建议"""
        
        actions = []
        
        # 按风险等级分组
        critical_risks = [r for r in risk_points if r.risk_level == RiskLevel.CRITICAL]
        high_risks = [r for r in risk_points if r.risk_level == RiskLevel.HIGH]
        
        if critical_risks:
            actions.append(f"立即处理 {len(critical_risks)} 个严重风险点")
            for risk in critical_risks[:3]:  # 限制数量
                if risk.recommended_actions:
                    actions.extend(risk.recommended_actions[:2])
        
        if high_risks:
            actions.append(f"优先处理 {len(high_risks)} 个高风险点")
            for risk in high_risks[:2]:
                if risk.recommended_actions:
                    actions.extend(risk.recommended_actions[:1])
        
        # 通用建议
        if len(risk_points) > 5:
            actions.append("建议进行全面的合同条款审查")
        
        return actions[:10]  # 限制建议数量
    
    def _generate_mitigation_plan(self, risk_points: List[RiskPoint]) -> List[str]:
        """生成风险缓解计划"""
        
        plan = []
        
        # 收集所有缓解策略
        all_strategies = []
        for risk in risk_points:
            all_strategies.extend(risk.mitigation_strategies)
        
        # 去重并按重要性排序
        unique_strategies = list(set(all_strategies))
        
        # 按风险等级优先级排序
        critical_strategies = []
        high_strategies = []
        other_strategies = []
        
        for risk in risk_points:
            if risk.risk_level == RiskLevel.CRITICAL:
                critical_strategies.extend(risk.mitigation_strategies)
            elif risk.risk_level == RiskLevel.HIGH:
                high_strategies.extend(risk.mitigation_strategies)
            else:
                other_strategies.extend(risk.mitigation_strategies)
        
        # 构建缓解计划
        if critical_strategies:
            plan.extend(list(set(critical_strategies))[:3])
        if high_strategies:
            plan.extend(list(set(high_strategies))[:3])
        if other_strategies and len(plan) < 5:
            plan.extend(list(set(other_strategies))[:2])
        
        return plan[:8]  # 限制计划数量
    
    def _calculate_detection_confidence(self, risk_points: List[RiskPoint]) -> float:
        """计算检测置信度"""
        
        if not risk_points:
            return 0.0
        
        total_confidence = sum(risk.confidence for risk in risk_points)
        return total_confidence / len(risk_points)
    
    def _calculate_coverage_score(
        self, 
        applicable_rules: List[RiskRule], 
        risk_points: List[RiskPoint]
    ) -> float:
        """计算覆盖率评分"""
        
        if not applicable_rules:
            return 1.0
        
        # 计算规则覆盖率
        triggered_rules = set(risk.rule_id for risk in risk_points)
        coverage_rate = len(triggered_rules) / len(applicable_rules)
        
        return min(coverage_rate, 1.0)
    
    def _update_stats(self, processing_time: float, ai_calls: int) -> None:
        """更新统计信息"""
        
        self.stats['total_analyses'] += 1
        self.stats['ai_calls_used'] += ai_calls
        
        # 更新平均处理时间
        total_time = (self.stats['average_processing_time'] * 
                     (self.stats['total_analyses'] - 1) + processing_time)
        self.stats['average_processing_time'] = total_time / self.stats['total_analyses']
    
    async def generate_mitigation_plans(
        self, 
        risk_points: List[RiskPoint],
        contract_id: str
    ) -> List[RiskMitigationPlan]:
        """生成详细的风险缓解计划"""
        
        mitigation_plans = []
        
        for risk in risk_points:
            if risk.risk_level in [RiskLevel.CRITICAL, RiskLevel.HIGH]:
                plan = RiskMitigationPlan(
                    risk_point_id=risk.id,
                    plan_name=f"{risk.risk_name}缓解计划",
                    mitigation_type=self._determine_mitigation_type(risk),
                    priority=self._calculate_priority(risk),
                    actions=risk.recommended_actions or ["制定具体缓解措施"],
                    timeline=self._suggest_timeline(risk),
                    responsible_party="合同管理部门",
                    estimated_cost=None,
                    expected_effectiveness=0.8,
                    success_metrics=[f"风险评分降低至{risk.risk_score * 0.3:.1f}以下"],
                    review_schedule="每周审查进展"
                )
                mitigation_plans.append(plan)
        
        return mitigation_plans
    
    def _determine_mitigation_type(self, risk: RiskPoint) -> str:
        """确定缓解类型"""
        
        category_types = {
            RiskCategory.LEGAL: "法律条款完善",
            RiskCategory.FINANCIAL: "财务保障措施",
            RiskCategory.COMMERCIAL: "商业条款优化",
            RiskCategory.OPERATIONAL: "操作流程改进",
            RiskCategory.COMPLIANCE: "合规性整改",
            RiskCategory.TECHNICAL: "技术方案调整",
            RiskCategory.REPUTATION: "声誉保护措施"
        }
        
        return category_types.get(risk.risk_category, "综合性措施")
    
    def _calculate_priority(self, risk: RiskPoint) -> int:
        """计算优先级"""
        
        priority_map = {
            RiskLevel.CRITICAL: 1,
            RiskLevel.HIGH: 2,
            RiskLevel.MEDIUM: 3,
            RiskLevel.LOW: 4,
            RiskLevel.NEGLIGIBLE: 5
        }
        
        return priority_map[risk.risk_level]
    
    def _suggest_timeline(self, risk: RiskPoint) -> str:
        """建议时间安排"""
        
        timeline_map = {
            RiskLevel.CRITICAL: "立即执行，3天内完成",
            RiskLevel.HIGH: "1周内启动，2周内完成",
            RiskLevel.MEDIUM: "2周内启动，1个月内完成",
            RiskLevel.LOW: "1个月内启动，2个月内完成",
            RiskLevel.NEGLIGIBLE: "适时安排，3个月内完成"
        }
        
        return timeline_map[risk.risk_level]

    def get_stats(self) -> Dict[str, Any]:
        """获取分析器统计信息"""
        return self.stats.copy()
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.stats = {
            'total_analyses': 0,
            'ai_calls_used': 0,
            'average_processing_time': 0.0,
            'detection_accuracy': 0.0
        }
    