"""
风险知识库管理系统
负责风险规则的存储、管理和查询
"""

import json
import logging
from typing import List, Dict, Optional, Any
from pathlib import Path
from datetime import datetime

from .models import (
    RiskRule, RiskCategory, RiskLevel, RiskImpact, 
    RiskProbability, RiskStatistics
)

logger = logging.getLogger(__name__)

class RiskKnowledgeBase:
    """风险知识库管理器"""
    
    def __init__(self, data_file: str = "risk_analysis/data/risk_rules.json"):
        self.data_file = Path(data_file)
        self.rules: Dict[int, RiskRule] = {}
        self.next_id = 1
        
        # 确保数据目录存在
        self.data_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 加载现有数据
        self.load_rules()
        
        # 如果没有数据，初始化默认规则
        if not self.rules:
            self._initialize_default_rules()
    
    def load_rules(self) -> None:
        """从文件加载风险规则"""
        try:
            if self.data_file.exists():
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.rules = {}
                for rule_data in data.get('rules', []):
                    # 处理datetime字段的反序列化
                    if 'created_at' in rule_data and isinstance(rule_data['created_at'], str):
                        rule_data['created_at'] = datetime.fromisoformat(rule_data['created_at'])
                    if 'updated_at' in rule_data and isinstance(rule_data['updated_at'], str):
                        rule_data['updated_at'] = datetime.fromisoformat(rule_data['updated_at'])
                    
                    rule = RiskRule(**rule_data)
                    self.rules[rule.id] = rule
                
                self.next_id = data.get('next_id', 1)
                logger.info(f"加载了 {len(self.rules)} 个风险规则")
            else:
                logger.info("风险规则文件不存在，将创建新的知识库")
        except Exception as e:
            logger.error(f"加载风险知识库失败: {e}")
            self.rules = {}
            self.next_id = 1
    
    def save_rules(self) -> None:
        """保存风险规则到文件"""
        try:
            # 自定义序列化函数处理datetime对象
            def serialize_rule(rule: RiskRule) -> Dict[str, Any]:
                """序列化单个规则，处理datetime对象"""
                rule_dict = rule.dict()
                
                # 将datetime对象转换为ISO格式字符串
                if 'created_at' in rule_dict and rule_dict['created_at']:
                    if isinstance(rule_dict['created_at'], datetime):
                        rule_dict['created_at'] = rule_dict['created_at'].isoformat()
                
                if 'updated_at' in rule_dict and rule_dict['updated_at']:
                    if isinstance(rule_dict['updated_at'], datetime):
                        rule_dict['updated_at'] = rule_dict['updated_at'].isoformat()
                
                return rule_dict
            
            data = {
                'rules': [serialize_rule(rule) for rule in self.rules.values()],
                'next_id': self.next_id,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"保存了 {len(self.rules)} 个风险规则")
        except Exception as e:
            logger.error(f"保存风险知识库失败: {e}")
    
    def add_rule(self, rule: RiskRule) -> int:
        """添加新规则"""
        if rule.id is None:
            rule.id = self.next_id
            self.next_id += 1
        
        rule.created_at = datetime.now()
        rule.updated_at = datetime.now()
        
        self.rules[rule.id] = rule
        self.save_rules()
        
        logger.info(f"添加新风险规则: {rule.rule_name} (ID: {rule.id})")
        return rule.id
    
    def get_rules_by_contract_type(self, contract_type: str) -> List[RiskRule]:
        """根据合同类型获取规则"""
        return [
            rule for rule in self.rules.values()
            if contract_type in rule.contract_types and rule.is_active
        ]
    
    def get_rules_by_category(self, category: RiskCategory) -> List[RiskRule]:
        """根据风险分类获取规则"""
        return [
            rule for rule in self.rules.values()
            if rule.risk_category == category and rule.is_active
        ]
    
    def search_rules(self, keyword: str) -> List[RiskRule]:
        """搜索风险规则"""
        keyword_lower = keyword.lower()
        results = []
        
        for rule in self.rules.values():
            if not rule.is_active:
                continue
                
            if (keyword_lower in rule.rule_name.lower() or
                keyword_lower in rule.description.lower() or
                keyword_lower in rule.risk_explanation.lower() or
                any(keyword_lower in kw.lower() for kw in rule.keywords)):
                results.append(rule)
        
        return results
    
    def get_statistics(self) -> RiskStatistics:
        """获取风险知识库统计信息"""
        active_rules = [rule for rule in self.rules.values() if rule.is_active]
        total_rules = len(active_rules)
        
        # 按分类统计
        rules_by_category = {}
        for category in RiskCategory:
            count = len([rule for rule in active_rules if rule.risk_category == category])
            if count > 0:
                rules_by_category[category.value] = count
        
        # 按风险等级统计
        rules_by_level = {}
        for level in RiskLevel:
            count = len([rule for rule in active_rules if rule.base_risk_level == level])
            if count > 0:
                rules_by_level[level.value] = count
        
        return RiskStatistics(
            total_rules=total_rules,
            rules_by_category=rules_by_category,
            rules_by_level=rules_by_level,
            detection_accuracy=0.85,  # 模拟值
            last_updated=datetime.now()
        )
    
    def _initialize_default_rules(self) -> None:
        """初始化默认风险规则"""
        logger.info("初始化默认风险规则...")
        
        default_rules = [
            # 法律风险规则
            RiskRule(
                rule_name="缺少争议解决条款",
                risk_category=RiskCategory.LEGAL,
                contract_types=["sales", "service", "lease", "employment", "general"],
                keywords=["争议", "纠纷", "仲裁", "诉讼", "管辖"],
                regex_patterns=[r"争议.*?解决", r"纠纷.*?处理", r"仲裁.*?委员会"],
                semantic_patterns=["争议解决机制", "纠纷处理程序", "法律管辖"],
                base_risk_level=RiskLevel.HIGH,
                impact_level=RiskImpact.MAJOR,
                probability=RiskProbability.MEDIUM,
                description="合同缺少明确的争议解决条款",
                risk_explanation="没有明确的争议解决机制可能导致纠纷处理困难，增加诉讼成本和时间",
                potential_consequences=[
                    "纠纷处理程序不明确",
                    "可能导致长期诉讼",
                    "增加法律成本",
                    "影响商业关系"
                ],
                mitigation_strategies=[
                    "添加详细的争议解决条款",
                    "明确仲裁或诉讼程序",
                    "指定管辖法院或仲裁机构"
                ],
                recommended_actions=[
                    "补充争议解决条款",
                    "选择合适的争议解决方式",
                    "明确适用法律"
                ]
            ),
            
            # 财务风险规则
            RiskRule(
                rule_name="付款条件不明确",
                risk_category=RiskCategory.FINANCIAL,
                contract_types=["sales", "service", "lease"],
                keywords=["付款", "支付", "款项", "费用", "价款", "逾期"],
                regex_patterns=[r"付款.*?期限", r"支付.*?方式", r"逾期.*?利息"],
                semantic_patterns=["付款安排", "支付条件", "资金结算"],
                base_risk_level=RiskLevel.HIGH,
                impact_level=RiskImpact.MAJOR,
                probability=RiskProbability.HIGH,
                description="付款条件、期限或方式不够明确",
                risk_explanation="模糊的付款条件可能导致资金回收困难，影响现金流",
                potential_consequences=[
                    "资金回收延迟",
                    "现金流压力",
                    "坏账风险增加",
                    "影响业务运营"
                ],
                mitigation_strategies=[
                    "明确付款期限和方式",
                    "设置逾期付款违约金",
                    "要求提供付款担保"
                ],
                recommended_actions=[
                    "详细规定付款条件",
                    "设置分期付款安排",
                    "建立催收机制"
                ]
            ),
            
            # 商业风险规则
            RiskRule(
                rule_name="知识产权归属不明",
                risk_category=RiskCategory.COMMERCIAL,
                contract_types=["service", "employment"],
                keywords=["知识产权", "专利", "著作权", "商标", "技术成果"],
                regex_patterns=[r"知识产权.*?归属", r"技术成果.*?所有", r"专利.*?申请"],
                semantic_patterns=["产权归属", "技术所有权", "创新成果"],
                base_risk_level=RiskLevel.MEDIUM,
                impact_level=RiskImpact.MODERATE,
                probability=RiskProbability.MEDIUM,
                description="知识产权归属和使用权不够明确",
                risk_explanation="知识产权归属不明可能导致后续纠纷，影响技术应用和商业化",
                potential_consequences=[
                    "知识产权纠纷",
                    "技术应用受限",
                    "商业化困难",
                    "法律诉讼风险"
                ],
                mitigation_strategies=[
                    "明确知识产权归属",
                    "约定使用权限",
                    "建立保护机制"
                ],
                recommended_actions=[
                    "详细约定产权归属",
                    "规定使用许可条件",
                    "设置保密义务"
                ]
            ),
            
            # 操作风险规则
            RiskRule(
                rule_name="履约标准不具体",
                risk_category=RiskCategory.OPERATIONAL,
                contract_types=["service", "sales"],
                keywords=["标准", "质量", "验收", "交付", "完成"],
                regex_patterns=[r"质量.*?标准", r"验收.*?条件", r"交付.*?要求"],
                semantic_patterns=["履约标准", "质量要求", "验收标准"],
                base_risk_level=RiskLevel.MEDIUM,
                impact_level=RiskImpact.MODERATE,
                probability=RiskProbability.HIGH,
                description="履约标准、质量要求或验收条件不够具体",
                risk_explanation="模糊的履约标准可能导致验收争议，影响项目交付",
                potential_consequences=[
                    "验收标准争议",
                    "质量纠纷",
                    "项目延期",
                    "客户满意度下降"
                ],
                mitigation_strategies=[
                    "制定详细的履约标准",
                    "明确验收程序",
                    "建立质量保证机制"
                ],
                recommended_actions=[
                    "细化技术规格",
                    "设置验收里程碑",
                    "建立质量监控体系"
                ]
            ),
            
            # 合规风险规则
            RiskRule(
                rule_name="缺少合规声明",
                risk_category=RiskCategory.COMPLIANCE,
                contract_types=["sales", "service", "general"],
                keywords=["合规", "法律", "法规", "标准", "认证"],
                regex_patterns=[r"符合.*?法律", r"遵守.*?法规", r"合规.*?要求"],
                semantic_patterns=["合规要求", "法律遵守", "监管合规"],
                base_risk_level=RiskLevel.MEDIUM,
                impact_level=RiskImpact.MAJOR,
                probability=RiskProbability.LOW,
                description="缺少必要的合规声明和保证",
                risk_explanation="缺少合规声明可能导致监管风险，影响业务合法性",
                potential_consequences=[
                    "监管处罚风险",
                    "业务合法性质疑",
                    "声誉损失",
                    "市场准入限制"
                ],
                mitigation_strategies=[
                    "添加合规声明条款",
                    "确保法律法规遵守",
                    "建立合规监控机制"
                ],
                recommended_actions=[
                    "补充合规保证条款",
                    "进行合规性审查",
                    "建立合规管理体系"
                ]
            ),
            
            # 技术风险规则
            RiskRule(
                rule_name="技术风险未约定",
                risk_category=RiskCategory.TECHNICAL,
                contract_types=["service"],
                keywords=["技术", "系统", "软件", "开发", "实施"],
                regex_patterns=[r"技术.*?风险", r"系统.*?故障", r"开发.*?失败"],
                semantic_patterns=["技术实现风险", "系统可靠性", "开发不确定性"],
                base_risk_level=RiskLevel.MEDIUM,
                impact_level=RiskImpact.MODERATE,
                probability=RiskProbability.MEDIUM,
                description="技术实施风险和责任分担不明确",
                risk_explanation="技术项目存在实施风险，需要明确风险分担和应对措施",
                potential_consequences=[
                    "技术实施失败",
                    "项目延期或超预算",
                    "系统性能不达标",
                    "技术支持不足"
                ],
                mitigation_strategies=[
                    "明确技术风险分担",
                    "设置技术里程碑",
                    "建立风险应对机制"
                ],
                recommended_actions=[
                    "制定技术实施计划",
                    "设置风险控制点",
                    "建立技术支持体系"
                ]
            ),
            
            # 声誉风险规则
            RiskRule(
                rule_name="保密义务不充分",
                risk_category=RiskCategory.REPUTATION,
                contract_types=["service", "employment"],
                keywords=["保密", "机密", "商业秘密", "泄露", "披露"],
                regex_patterns=[r"保密.*?义务", r"商业.*?秘密", r"信息.*?泄露"],
                semantic_patterns=["保密责任", "信息安全", "商业机密保护"],
                base_risk_level=RiskLevel.MEDIUM,
                impact_level=RiskImpact.MAJOR,
                probability=RiskProbability.LOW,
                description="保密义务和信息保护措施不够充分",
                risk_explanation="保密措施不足可能导致商业信息泄露，影响竞争优势",
                potential_consequences=[
                    "商业信息泄露",
                    "竞争优势丧失",
                    "声誉损害",
                    "客户信任度下降"
                ],
                mitigation_strategies=[
                    "加强保密条款",
                    "明确保密范围",
                    "设置违约责任"
                ],
                recommended_actions=[
                    "完善保密协议",
                    "建立信息安全制度",
                    "加强员工保密培训"
                ]
            )
        ]
        
        # 添加所有默认规则
        for rule in default_rules:
            self.add_rule(rule)
        
        logger.info(f"初始化完成，添加了 {len(default_rules)} 个默认风险规则")

# 全局风险知识库实例
risk_knowledge_base = RiskKnowledgeBase()
