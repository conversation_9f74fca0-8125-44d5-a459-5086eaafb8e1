"""
风险分析模块

多维度合同风险识别和评估系统，支持：
- 法律、商业、财务、操作等多类风险识别
- 基于规则和AI的智能风险检测
- 风险等级评估和影响分析
- 风险缓解建议和行动计划

主要组件：
- models: 风险分析数据模型
- analyzer: 风险分析引擎
- knowledge_base: 风险知识库管理
- risk_rules: 风险规则定义
"""

# 迁移自根目录的风险分析代码
from .models import (
    RiskCategory,
    RiskLevel,
    RiskImpact,
    RiskProbability,
    RiskRule,
    RiskPoint,
    RiskAssessment,
    RiskAnalysisResult,
    RiskMitigationPlan
)

from .analyzer import RiskAnalyzer
from .knowledge_base import RiskKnowledgeBase, risk_knowledge_base

__all__ = [
    # 枚举类型
    "RiskCategory",
    "RiskLevel", 
    "RiskImpact",
    "RiskProbability",
    
    # 数据模型
    "RiskRule",
    "RiskPoint",
    "RiskAssessment", 
    "RiskAnalysisResult",
    "RiskMitigationPlan",
    
    # 核心组件
    "RiskAnalyzer",
    "RiskKnowledgeBase",
    "risk_knowledge_base"
]