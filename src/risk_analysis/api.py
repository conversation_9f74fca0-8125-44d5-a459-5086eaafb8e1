"""
风险分析API接口
提供风险分析的RESTful API服务
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime

from .models import (
    RiskAnalysisRequest, RiskAnalysisResponse, RiskAnalysisResult,
    RiskRule, RiskPoint, RiskMitigationPlan, RiskStatistics,
    RiskRuleQuery, RiskCategory, RiskLevel
)
from .analyzer import RiskAnalyzer
from .knowledge_base import risk_knowledge_base
from ..core.qwen_client import get_qwen_client

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v1/risk-analysis", tags=["风险分析"])

# 全局分析器实例
risk_analyzer = None

def get_risk_analyzer():
    """获取风险分析器实例"""
    global risk_analyzer
    if risk_analyzer is None:
        qwen_client = get_qwen_client()
        risk_analyzer = RiskAnalyzer(qwen_client)
    return risk_analyzer

@router.post("/analyze", response_model=RiskAnalysisResponse)
async def analyze_contract_risks(
    request: RiskAnalysisRequest,
    background_tasks: BackgroundTasks,
    analyzer: RiskAnalyzer = Depends(get_risk_analyzer)
):
    """
    分析合同风险
    
    Args:
        request: 风险分析请求
        background_tasks: 后台任务
        analyzer: 风险分析器
        
    Returns:
        风险分析响应
    """
    try:
        start_time = datetime.now()
        
        logger.info(f"开始风险分析 - 合同类型: {request.contract_type}")
        
        # 执行风险分析
        result = await analyzer.analyze_risks(
            contract_text=request.contract_text,
            contract_type=request.contract_type,
            risk_categories=request.risk_categories,
            min_risk_level=request.min_risk_level,
            enable_ai_analysis=request.enable_ai_analysis,
            detailed_analysis=request.detailed_analysis
        )
        
        # 生成缓解计划（如果需要）
        mitigation_plans = []
        if request.include_mitigation_plan:
            mitigation_plans = await analyzer.generate_mitigation_plans(
                result.risk_points, 
                request.context_info.get('contract_id', 'unknown') if request.context_info else 'unknown'
            )
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # 构建响应
        response = RiskAnalysisResponse(
            success=True,
            message=f"风险分析完成，识别到 {len(result.risk_points)} 个风险点",
            result=result,
            mitigation_plans=mitigation_plans,
            processing_time=processing_time,
            request_id=f"risk_{int(start_time.timestamp())}"
        )
        
        # 后台任务：记录分析日志
        background_tasks.add_task(
            _log_analysis_result,
            request.contract_type,
            len(result.risk_points),
            processing_time,
            result.ai_calls_used
        )
        
        logger.info(f"风险分析完成 - 耗时: {processing_time:.2f}秒")
        
        return response
        
    except Exception as e:
        logger.error(f"风险分析失败: {e}")
        return RiskAnalysisResponse(
            success=False,
            message=f"风险分析失败: {str(e)}",
            processing_time=0.0
        )

@router.get("/rules", response_model=List[RiskRule])
async def get_risk_rules(
    query: RiskRuleQuery = Depends(),
    knowledge_base=Depends(lambda: risk_knowledge_base)
):
    """
    获取风险规则列表
    
    Args:
        query: 查询参数
        knowledge_base: 风险知识库
        
    Returns:
        风险规则列表
    """
    try:
        rules = list(knowledge_base.rules.values())
        
        # 按分类过滤
        if query.categories:
            rules = [rule for rule in rules if rule.risk_category in query.categories]
        
        # 按合同类型过滤
        if query.contract_types:
            rules = [rule for rule in rules 
                    if any(ct in rule.contract_types for ct in query.contract_types)]
        
        # 按风险等级过滤
        if query.risk_levels:
            rules = [rule for rule in rules if rule.base_risk_level in query.risk_levels]
        
        # 关键词搜索
        if query.keyword:
            rules = knowledge_base.search_rules(query.keyword)
        
        # 分页
        start_idx = (query.page - 1) * query.page_size
        end_idx = start_idx + query.page_size
        
        return rules[start_idx:end_idx]
        
    except Exception as e:
        logger.error(f"获取风险规则失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取风险规则失败: {str(e)}")

@router.post("/rules", response_model=Dict[str, Any])
async def create_risk_rule(
    rule: RiskRule,
    knowledge_base=Depends(lambda: risk_knowledge_base)
):
    """
    创建新的风险规则
    
    Args:
        rule: 风险规则
        knowledge_base: 风险知识库
        
    Returns:
        创建结果
    """
    try:
        rule_id = knowledge_base.add_rule(rule)
        
        logger.info(f"创建风险规则成功: {rule.rule_name} (ID: {rule_id})")
        
        return {
            "success": True,
            "message": "风险规则创建成功",
            "rule_id": rule_id
        }
        
    except Exception as e:
        logger.error(f"创建风险规则失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建风险规则失败: {str(e)}")

@router.get("/rules/{rule_id}", response_model=RiskRule)
async def get_risk_rule(
    rule_id: int,
    knowledge_base=Depends(lambda: risk_knowledge_base)
):
    """
    获取指定风险规则
    
    Args:
        rule_id: 规则ID
        knowledge_base: 风险知识库
        
    Returns:
        风险规则
    """
    try:
        rule = knowledge_base.rules.get(rule_id)
        if not rule:
            raise HTTPException(status_code=404, detail="风险规则不存在")
        
        return rule
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取风险规则失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取风险规则失败: {str(e)}")

@router.put("/rules/{rule_id}", response_model=Dict[str, Any])
async def update_risk_rule(
    rule_id: int,
    rule: RiskRule,
    knowledge_base=Depends(lambda: risk_knowledge_base)
):
    """
    更新风险规则
    
    Args:
        rule_id: 规则ID
        rule: 更新的风险规则
        knowledge_base: 风险知识库
        
    Returns:
        更新结果
    """
    try:
        if rule_id not in knowledge_base.rules:
            raise HTTPException(status_code=404, detail="风险规则不存在")
        
        rule.id = rule_id
        rule.updated_at = datetime.now()
        knowledge_base.rules[rule_id] = rule
        knowledge_base.save_rules()
        
        logger.info(f"更新风险规则成功: {rule.rule_name} (ID: {rule_id})")
        
        return {
            "success": True,
            "message": "风险规则更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新风险规则失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新风险规则失败: {str(e)}")

@router.delete("/rules/{rule_id}", response_model=Dict[str, Any])
async def delete_risk_rule(
    rule_id: int,
    knowledge_base=Depends(lambda: risk_knowledge_base)
):
    """
    删除风险规则
    
    Args:
        rule_id: 规则ID
        knowledge_base: 风险知识库
        
    Returns:
        删除结果
    """
    try:
        if rule_id not in knowledge_base.rules:
            raise HTTPException(status_code=404, detail="风险规则不存在")
        
        rule = knowledge_base.rules[rule_id]
        rule.is_active = False
        knowledge_base.save_rules()
        
        logger.info(f"删除风险规则成功: {rule.rule_name} (ID: {rule_id})")
        
        return {
            "success": True,
            "message": "风险规则删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除风险规则失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除风险规则失败: {str(e)}")

@router.get("/statistics", response_model=RiskStatistics)
async def get_risk_statistics(
    knowledge_base=Depends(lambda: risk_knowledge_base)
):
    """
    获取风险知识库统计信息
    
    Args:
        knowledge_base: 风险知识库
        
    Returns:
        统计信息
    """
    try:
        return knowledge_base.get_statistics()
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.get("/categories", response_model=List[Dict[str, Any]])
async def get_risk_categories():
    """
    获取风险分类列表
    
    Returns:
        风险分类列表
    """
    try:
        categories = []
        for category in RiskCategory:
            categories.append({
                "code": category.value,
                "name": _get_category_name(category),
                "description": _get_category_description(category)
            })
        
        return categories
        
    except Exception as e:
        logger.error(f"获取风险分类失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取风险分类失败: {str(e)}")

@router.get("/levels", response_model=List[Dict[str, Any]])
async def get_risk_levels():
    """
    获取风险等级列表
    
    Returns:
        风险等级列表
    """
    try:
        levels = []
        for level in RiskLevel:
            levels.append({
                "code": level.value,
                "name": _get_level_name(level),
                "description": _get_level_description(level),
                "weight": _get_level_weight(level)
            })
        
        return levels
        
    except Exception as e:
        logger.error(f"获取风险等级失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取风险等级失败: {str(e)}")

@router.post("/batch-analyze", response_model=List[RiskAnalysisResponse])
async def batch_analyze_contracts(
    requests: List[RiskAnalysisRequest],
    background_tasks: BackgroundTasks,
    analyzer: RiskAnalyzer = Depends(get_risk_analyzer)
):
    """
    批量分析合同风险
    
    Args:
        requests: 批量分析请求
        background_tasks: 后台任务
        analyzer: 风险分析器
        
    Returns:
        批量分析响应
    """
    try:
        responses = []
        
        for i, request in enumerate(requests):
            try:
                start_time = datetime.now()
                
                # 执行风险分析
                result = await analyzer.analyze_risks(
                    contract_text=request.contract_text,
                    contract_type=request.contract_type,
                    risk_categories=request.risk_categories,
                    min_risk_level=request.min_risk_level,
                    enable_ai_analysis=request.enable_ai_analysis,
                    detailed_analysis=request.detailed_analysis
                )
                
                processing_time = (datetime.now() - start_time).total_seconds()
                
                response = RiskAnalysisResponse(
                    success=True,
                    message=f"批量分析第{i+1}个合同完成",
                    result=result,
                    processing_time=processing_time,
                    request_id=f"batch_{i+1}_{int(start_time.timestamp())}"
                )
                
                responses.append(response)
                
            except Exception as e:
                logger.error(f"批量分析第{i+1}个合同失败: {e}")
                responses.append(RiskAnalysisResponse(
                    success=False,
                    message=f"批量分析第{i+1}个合同失败: {str(e)}",
                    processing_time=0.0
                ))
        
        return responses
        
    except Exception as e:
        logger.error(f"批量风险分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量风险分析失败: {str(e)}")

# 辅助函数
async def _log_analysis_result(
    contract_type: str,
    risks_count: int,
    processing_time: float,
    ai_calls: int
):
    """记录分析结果日志"""
    try:
        logger.info(f"风险分析日志 - 类型: {contract_type}, 风险数: {risks_count}, "
                   f"耗时: {processing_time:.2f}s, AI调用: {ai_calls}次")
    except Exception as e:
        logger.error(f"记录分析日志失败: {e}")

def _get_category_name(category: RiskCategory) -> str:
    """获取风险分类名称"""
    names = {
        RiskCategory.LEGAL: "法律风险",
        RiskCategory.COMMERCIAL: "商业风险",
        RiskCategory.FINANCIAL: "财务风险",
        RiskCategory.OPERATIONAL: "操作风险",
        RiskCategory.COMPLIANCE: "合规风险",
        RiskCategory.TECHNICAL: "技术风险",
        RiskCategory.REPUTATION: "声誉风险"
    }
    return names.get(category, category.value)

def _get_category_description(category: RiskCategory) -> str:
    """获取风险分类描述"""
    descriptions = {
        RiskCategory.LEGAL: "与法律条款、争议解决相关的风险",
        RiskCategory.COMMERCIAL: "与商业条款、知识产权相关的风险",
        RiskCategory.FINANCIAL: "与付款、财务保障相关的风险",
        RiskCategory.OPERATIONAL: "与履约、执行相关的风险",
        RiskCategory.COMPLIANCE: "与合规、监管相关的风险",
        RiskCategory.TECHNICAL: "与技术实施相关的风险",
        RiskCategory.REPUTATION: "与声誉、保密相关的风险"
    }
    return descriptions.get(category, "")

def _get_level_name(level: RiskLevel) -> str:
    """获取风险等级名称"""
    names = {
        RiskLevel.CRITICAL: "严重风险",
        RiskLevel.HIGH: "高风险",
        RiskLevel.MEDIUM: "中风险",
        RiskLevel.LOW: "低风险",
        RiskLevel.NEGLIGIBLE: "可忽略风险"
    }
    return names.get(level, level.value)

def _get_level_description(level: RiskLevel) -> str:
    """获取风险等级描述"""
    descriptions = {
        RiskLevel.CRITICAL: "需要立即处理的严重风险",
        RiskLevel.HIGH: "需要优先处理的高风险",
        RiskLevel.MEDIUM: "需要及时处理的中等风险",
        RiskLevel.LOW: "可适时处理的低风险",
        RiskLevel.NEGLIGIBLE: "影响较小的可忽略风险"
    }
    return descriptions.get(level, "")

def _get_level_weight(level: RiskLevel) -> float:
    """获取风险等级权重"""
    weights = {
        RiskLevel.CRITICAL: 10.0,
        RiskLevel.HIGH: 7.5,
        RiskLevel.MEDIUM: 5.0,
        RiskLevel.LOW: 2.5,
        RiskLevel.NEGLIGIBLE: 1.0
    }
    return weights.get(level, 1.0)
