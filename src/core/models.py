"""
核心数据模型

定义系统核心的数据结构和模型
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field

class ContractAnalysisResult(BaseModel):
    """合同分析结果"""
    contract_id: Optional[str] = Field(None, description="合同ID")
    contract_type: str = Field(..., description="合同类型")
    analysis_time: datetime = Field(default_factory=datetime.now, description="分析时间")
    
    # 分析结果
    extracted_elements: List[Dict[str, Any]] = Field(default=[], description="提取的要素")
    detected_clauses: List[Dict[str, Any]] = Field(default=[], description="检测的条款")
    risk_points: List[Dict[str, Any]] = Field(default=[], description="风险点")
    
    # 统计信息
    processing_duration: float = Field(..., description="处理耗时")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="整体置信度")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ExtractedElement(BaseModel):
    """提取的要素"""
    element_id: str = Field(..., description="要素ID")
    element_name: str = Field(..., description="要素名称")
    element_type: str = Field(..., description="要素类型")
    value: str = Field(..., description="要素值")
    confidence: float = Field(..., ge=0.0, le=1.0, description="置信度")
    
    # 位置信息
    start_position: Optional[int] = Field(None, description="开始位置")
    end_position: Optional[int] = Field(None, description="结束位置")
    context: Optional[str] = Field(None, description="上下文")
    
    # 验证信息
    is_valid: bool = Field(True, description="是否有效")
    validation_errors: List[str] = Field(default=[], description="验证错误")
    
    # 元数据
    extraction_method: str = Field(..., description="提取方法")
    extracted_at: datetime = Field(default_factory=datetime.now, description="提取时间")