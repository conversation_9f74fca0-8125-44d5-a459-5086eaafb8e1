"""
AI客户端模块

封装千问3 API调用，提供统一的AI服务接口
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
import httpx
from src.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

class QwenAPIClient:
    """千问3 API客户端"""
    
    def __init__(self, api_key: Optional[str] = None):
        """初始化客户端"""
        self.api_key = api_key or settings.qwen_api_key
        self.api_base = settings.qwen_api_base
        self.model = settings.qwen_model
        self.max_tokens = settings.qwen_max_tokens
        self.temperature = settings.qwen_temperature
        
        if not self.api_key:
            logger.warning("千问API密钥未配置，AI功能将不可用")
    
    async def analyze_text_async(self, text: str, **kwargs) -> Dict[str, Any]:
        """异步文本分析"""
        if not self.api_key:
            raise ValueError("千问API密钥未配置")
        
        try:
            response = await self._call_api(text, **kwargs)
            return {
                "analysis": response,
                "confidence": 0.85,  # 模拟置信度
                "elements": []
            }
        except Exception as e:
            logger.error(f"AI分析失败: {e}")
            raise
    
    async def _call_api(self, prompt: str, max_tokens: Optional[int] = None) -> str:
        """调用千问API"""
        if not self.api_key:
            raise ValueError("千问API密钥未配置")
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": max_tokens or self.max_tokens,
            "temperature": self.temperature
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.api_base}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=30.0
                )
                response.raise_for_status()
                
                result = response.json()
                return result["choices"][0]["message"]["content"]
                
            except httpx.HTTPError as e:
                logger.error(f"千问API调用失败: {e}")
                raise
            except Exception as e:
                logger.error(f"API调用异常: {e}")
                raise