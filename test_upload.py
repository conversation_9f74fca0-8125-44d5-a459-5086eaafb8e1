#!/usr/bin/env python3
"""测试文件上传API"""

import requests
import io

# 创建一个测试文件
test_content = b"This is a test document content for upload testing."
test_file = io.BytesIO(test_content)

# 准备上传数据
files = {
    'file': ('test_document.txt', test_file, 'text/plain')
}

try:
    # 发送上传请求
    response = requests.post('http://localhost:8000/api/v1/contracts/upload', files=files)
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    
except Exception as e:
    print(f"Upload test failed: {e}")