# AI合同审核系统 - 环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# =============================================================================
# 应用基础配置
# =============================================================================
APP_NAME="AI合同审核系统"
APP_VERSION="1.0.0"
DEBUG=true
ENVIRONMENT="development"

# =============================================================================
# 服务器配置
# =============================================================================
HOST="0.0.0.0"
PORT=8000
WORKERS=1

# =============================================================================
# 数据库配置
# =============================================================================
# SQLite (开发环境)
DATABASE_URL="sqlite:///./data/contract_audit.db"

# PostgreSQL (生产环境)
# DATABASE_URL="postgresql://username:password@localhost:5432/contract_audit"

DATABASE_ECHO=false

# =============================================================================
# Redis配置
# =============================================================================
REDIS_URL="redis://localhost:6379/0"
REDIS_PASSWORD=""

# =============================================================================
# AI服务配置 - 千问3 API
# =============================================================================
# 千问API密钥 (必须配置)
QWEN_API_KEY="your-qwen-api-key-here"

# 千问API配置
QWEN_API_BASE="https://dashscope.aliyuncs.com/compatible-mode/v1"
QWEN_MODEL="qwen-plus"
QWEN_MAX_TOKENS=2000
QWEN_TEMPERATURE=0.7

# =============================================================================
# 文件存储配置
# =============================================================================
UPLOAD_DIR="./uploads"
TEMP_DIR="./temp"
MAX_FILE_SIZE=52428800  # 50MB
ALLOWED_EXTENSIONS=[".docx", ".doc"]

# =============================================================================
# 安全配置
# =============================================================================
# JWT密钥 (生产环境请使用强密钥)
SECRET_KEY="your-super-secret-jwt-key-change-this-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM="HS256"

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL="INFO"
LOG_FILE="./logs/app.log"
LOG_MAX_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5

# =============================================================================
# 缓存配置
# =============================================================================
CACHE_TTL=3600  # 1小时
CACHE_MAX_SIZE=1000

# =============================================================================
# API配置
# =============================================================================
API_PREFIX="/api/v1"
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000"]

# =============================================================================
# 业务配置
# =============================================================================
MIN_CONFIDENCE=0.6
MAX_CONCURRENT_ANALYSES=10
ANALYSIS_TIMEOUT=300  # 5分钟

# =============================================================================
# 开发配置
# =============================================================================
# 是否启用API文档 (生产环境建议设为false)
ENABLE_DOCS=true

# 是否启用调试工具
ENABLE_DEBUG_TOOLBAR=true

# 测试数据库 (仅测试环境)
TEST_DATABASE_URL="sqlite:///:memory:"