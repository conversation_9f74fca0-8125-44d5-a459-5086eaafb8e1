#!/usr/bin/env python3
"""
开发服务器启动脚本

启动开发环境的API服务器，支持热重载和调试
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def check_environment():
    """检查开发环境"""
    print("🔍 检查开发环境...")
    
    # 检查.env文件
    env_file = project_root / ".env"
    if not env_file.exists():
        print("⚠️  .env文件不存在，使用默认配置")
        # 复制示例配置
        example_env = project_root / ".env.example"
        if example_env.exists():
            import shutil
            shutil.copy(example_env, env_file)
            print("✅ 已复制.env.example到.env")
    
    # 检查必要目录
    directories = ["logs", "temp", "uploads"]
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(exist_ok=True)
    
    print("✅ 环境检查完成")

def start_api_server(host="127.0.0.1", port=8000, reload=True, workers=1):
    """启动API服务器"""
    print(f"🚀 启动API服务器 http://{host}:{port}")
    
    cmd = [
        sys.executable, "-m", "uvicorn",
        "src.api.main:app",
        "--host", host,
        "--port", str(port)
    ]
    
    if reload:
        cmd.append("--reload")
        cmd.extend(["--reload-dir", "src"])
    
    if workers > 1:
        cmd.extend(["--workers", str(workers)])
    
    # 设置环境变量
    env = os.environ.copy()
    env["PYTHONPATH"] = str(project_root)
    
    try:
        subprocess.run(cmd, env=env, cwd=project_root)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")

def start_frontend_dev():
    """启动前端开发服务器"""
    frontend_dir = project_root / "web"
    if not frontend_dir.exists():
        print("⚠️  前端目录不存在，跳过前端服务器启动")
        return
    
    print("🌐 启动前端开发服务器...")
    
    # 检查package.json
    package_json = frontend_dir / "package.json"
    if not package_json.exists():
        print("❌ package.json不存在")
        return
    
    # 启动前端服务器
    try:
        subprocess.run(["npm", "run", "dev"], cwd=frontend_dir)
    except FileNotFoundError:
        print("❌ npm未安装，请先安装Node.js")
    except KeyboardInterrupt:
        print("\n👋 前端服务器已停止")

def run_tests():
    """运行测试"""
    print("🧪 运行测试...")
    
    cmd = [sys.executable, "-m", "pytest", "tests/", "-v"]
    
    env = os.environ.copy()
    env["PYTHONPATH"] = str(project_root)
    
    subprocess.run(cmd, env=env, cwd=project_root)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI合同审核系统开发服务器")
    parser.add_argument("--host", default="127.0.0.1", help="服务器地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--no-reload", action="store_true", help="禁用热重载")
    parser.add_argument("--workers", type=int, default=1, help="工作进程数")
    parser.add_argument("--frontend", action="store_true", help="同时启动前端服务器")
    parser.add_argument("--test", action="store_true", help="运行测试")
    
    args = parser.parse_args()
    
    # 检查环境
    check_environment()
    
    if args.test:
        run_tests()
        return
    
    if args.frontend:
        # 在新进程中启动前端服务器
        import threading
        frontend_thread = threading.Thread(target=start_frontend_dev)
        frontend_thread.daemon = True
        frontend_thread.start()
    
    # 启动API服务器
    start_api_server(
        host=args.host,
        port=args.port,
        reload=not args.no_reload,
        workers=args.workers
    )

if __name__ == "__main__":
    main()