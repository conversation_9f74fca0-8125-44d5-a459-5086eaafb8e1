#!/usr/bin/env python3
"""
依赖安装脚本

自动安装项目所需的Python依赖包
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"正在{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}成功")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        print(f"错误输出: {e.stderr}")
        return None

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")

def install_requirements():
    """安装requirements.txt中的依赖"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    # 升级pip
    run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip")
    
    # 安装依赖
    result = run_command(
        f"{sys.executable} -m pip install -r {requirements_file}",
        "安装Python依赖"
    )
    
    return result is not None

def install_dev_dependencies():
    """安装开发依赖"""
    dev_packages = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0", 
        "pytest-cov>=4.0.0",
        "black>=22.0.0",
        "flake8>=5.0.0",
        "mypy>=1.0.0",
        "pre-commit>=2.20.0"
    ]
    
    for package in dev_packages:
        run_command(
            f"{sys.executable} -m pip install {package}",
            f"安装{package}"
        )

def setup_pre_commit():
    """设置pre-commit钩子"""
    if Path(".pre-commit-config.yaml").exists():
        run_command("pre-commit install", "设置pre-commit钩子")

def create_directories():
    """创建必要的目录"""
    directories = [
        "logs",
        "temp", 
        "uploads",
        "data/configs",
        "data/samples",
        "data/exports"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def main():
    """主函数"""
    print("🚀 开始安装AI合同审核系统依赖...")
    
    # 检查Python版本
    check_python_version()
    
    # 创建必要目录
    create_directories()
    
    # 安装依赖
    if install_requirements():
        print("✅ 基础依赖安装完成")
    else:
        print("❌ 基础依赖安装失败")
        sys.exit(1)
    
    # 询问是否安装开发依赖
    if input("是否安装开发依赖? (y/N): ").lower() == 'y':
        install_dev_dependencies()
        setup_pre_commit()
        print("✅ 开发依赖安装完成")
    
    print("\n🎉 依赖安装完成！")
    print("\n下一步:")
    print("1. 配置环境变量: cp .env.example .env")
    print("2. 编辑配置文件: vim .env")
    print("3. 初始化数据库: python scripts/setup/setup_database.py")
    print("4. 启动开发服务器: python scripts/development/start_dev.py")

if __name__ == "__main__":
    main()