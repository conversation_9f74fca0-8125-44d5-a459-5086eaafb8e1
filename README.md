# AI合同审核系统

基于千问3大语言模型的智能合同分析系统，提供合同要素提取、风险识别和缺失条款检测功能。

## 🎯 项目状态

### ✅ 已完成功能 (100%)
- **配置化要素管理系统** - 支持无代码配置新合同类型和要素
- **智能条款检测算法** - 多维度条款匹配和缺失检测
- **风险分析模块** - 多维度风险识别和评估系统

### 🔄 当前开发阶段
- **第一阶段**: 核心业务功能完善 (第1-8周, 100%完成) ✅
- **当前阶段**: 用户界面与交互 (第9-14周, 准备开始)

## 🚀 快速开始

### 方法一：使用新的项目结构 (推荐)

```bash
# 1. 安装依赖
python scripts/setup/install_dependencies.py

# 2. 配置环境
cp .env.example .env
# 编辑 .env 文件，配置千问3 API密钥

# 3. 初始化数据库（可选，如需要持久化存储）
# 安装PostgreSQL后执行：
# psql -U username -d database_name -f database/init_database.sql

# 4. 启动开发服务器
python scripts/development/start_dev.py

# 4. 访问系统
# API文档: http://localhost:8000/docs
# 健康检查: http://localhost:8000/health
```

### 方法二：使用MVP版本

```bash
# 1. 进入MVP目录
cd mvp

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置千问3 API
python setup_qwen.py

# 4. 启动服务
python start_mvp.py
```

## 📁 项目结构 (当前实现)

```
ai-contract-audit/
├── docs/                    # 📚 完整文档系统
│   ├── requirements/        # 需求文档
│   ├── design/             # 设计文档 ✅
│   │   ├── system_architecture.md
│   │   └── technical_implementation_summary.md
│   ├── development/        # 开发文档
│   └── project_management/ # 项目管理
├── src/                    # 🔧 核心源代码 ✅ 已完成
│   ├── config/            # 配置管理 ✅
│   │   └── settings.py    # 系统配置
│   ├── core/              # 核心业务逻辑 ✅
│   │   ├── ai_client.py   # AI客户端
│   │   └── models.py      # 核心数据模型
│   ├── element_extraction/ # 要素提取模块 ✅
│   │   ├── config_manager.py
│   │   ├── extractor.py
│   │   ├── models.py
│   │   └── validators.py
│   ├── clause_detection/   # 条款检测模块 ✅
│   │   ├── detector.py
│   │   ├── clause_library.py
│   │   ├── semantic_matcher.py
│   │   └── models.py
│   ├── risk_analysis/      # 风险分析模块 ✅
│   │   ├── analyzer.py
│   │   ├── knowledge_base.py
│   │   └── models.py
│   ├── api/               # API接口层 ✅
│   │   ├── main.py        # FastAPI应用
│   │   ├── routes/        # API路由
│   │   ├── middleware/    # 中间件
│   │   └── schemas/       # API数据模型
│   └── utils/             # 工具函数 ✅
├── tests/                  # 🧪 测试框架 ✅
│   ├── unit/              # 单元测试
│   ├── integration/       # 集成测试
│   ├── e2e/              # 端到端测试
│   └── fixtures/         # 测试数据
├── scripts/               # 🔨 开发脚本 ✅
│   ├── setup/            # 安装脚本
│   └── development/      # 开发工具
├── deployment/            # 🚀 部署配置 ✅
│   └── docker/           # Docker配置
├── database/              # 🗄️ 数据库脚本 ✅
│   ├── schema/           # 数据库结构
│   ├── seeds/            # 示例数据
│   └── README.md         # 数据库文档
├── mvp/                   # 🎯 MVP实现 (保留)
└── data/                  # 📊 数据文件
```

## 🧪 运行测试

```bash
# 运行所有测试
python scripts/development/start_dev.py --test

# 或使用pytest直接运行
pytest tests/ -v

# 运行特定模块测试
pytest tests/unit/test_clause_detection/ -v
```

## 📋 核心功能

### ✅ 已实现功能
- **配置化要素管理** - 完全配置化的要素提取系统，支持热更新
- **智能条款检测** - 语义、关键词、正则、混合匹配算法
- **缺失条款分析** - AI增强的缺失条款检测和推荐系统
- **风险点识别** - 多维度风险分析和评估引擎
- **标准条款库** - 8个预置标准条款，支持动态扩展
- **API接口** - 完整的RESTful API，支持异步处理
- **文档处理** - 智能Word文档解析和结构化分析
- **AI集成** - 千问3 API深度集成，支持降级机制

### 🔄 开发中功能
- **前端用户界面** - Vue 3 + Element Plus界面开发
- **用户管理系统** - JWT认证和权限控制

### ⏳ 计划功能
- **前端用户界面** - Vue 3 + Element Plus
- **用户管理系统** - JWT认证 + RBAC权限
- **批量处理** - 支持批量合同分析
- **报告导出** - 多格式报告导出

## 🔧 技术栈

### 后端技术 ✅ 已完成
- **框架**: FastAPI + Python 3.8+ ✅
- **AI服务**: 千问3 (Qwen) API ✅
- **文档处理**: python-docx ✅
- **数据验证**: Pydantic Models ✅
- **异步处理**: asyncio + uvicorn ✅
- **测试**: pytest + pytest-asyncio ✅
- **数据库**: PostgreSQL + Redis (计划中)

### AI/ML技术栈 ✅ 已完成
- **核心模型**: 千问3 (Qwen) API服务 ✅
- **文本处理**: jieba分词 + 正则表达式 ✅
- **语义分析**: 千问3 API语义匹配 ✅
- **降级机制**: 规则匹配备用方案 ✅
- **缓存优化**: TTL缓存减少API调用 ✅

### 前端技术 ⏳ 计划中
- **框架**: Vue 3 + TypeScript
- **UI库**: Element Plus
- **构建**: Vite
- **状态管理**: Pinia

### 开发工具 ✅ 已配置
- **代码质量**: Black + Flake8 + MyPy ✅
- **测试覆盖**: pytest-cov ✅
- **容器化**: Docker + Docker Compose ✅
- **依赖管理**: requirements.txt + 安装脚本 ✅

## 📖 详细文档

### 📋 需求与设计文档 ✅ 已完成
- [业务需求文档](docs/requirements/business_requirements.md) - 功能需求和业务流程
- [系统架构设计](docs/design/system_architecture.md) - 整体架构和技术选型
- [技术实现总结](docs/design/technical_implementation_summary.md) - 当前实现状态和技术细节
- [数据库设计](docs/design/database_design.md) - 数据库结构和设计说明

### 📊 项目管理文档 ✅ 已完成
- [项目进度报告](docs/project_management/progress_reports.md) - 详细的进度跟踪和成果
- [项目状态更新](docs/project_management/current_status_update.md) - 最新项目状态

### 👥 用户文档 ⏳ 计划中
- [安装指南](docs/user_manual/installation.md) - 系统安装和配置
- [使用手册](docs/user_manual/user_guide.md) - 功能使用说明
- [API参考](docs/user_manual/api_reference.md) - API接口文档

### 🔧 开发文档 ⏳ 计划中
- [环境搭建](docs/development/setup_guide.md) - 开发环境配置
- [编码规范](docs/development/coding_standards.md) - 代码风格和规范
- [测试指南](docs/development/testing_guide.md) - 测试策略和用例

## 🏗️ 开发指南

### 环境要求
- Python 3.8+
- Redis (可选，用于缓存)
- PostgreSQL (生产环境)

### 开发流程
1. Fork项目并创建功能分支
2. 安装开发依赖: `pip install -r requirements.txt`
3. 运行测试确保基础功能正常
4. 开发新功能并编写测试
5. 提交PR并等待代码审查

### 代码规范
- 使用Black进行代码格式化
- 遵循PEP 8编码规范
- 编写完整的类型注解
- 保持测试覆盖率>85%

## 🔐 安全说明

1. **API密钥安全**: 千问3 API密钥请妥善保管，不要提交到代码仓库
2. **数据加密**: 合同数据传输和存储均已加密
3. **访问控制**: 实施基于角色的访问控制
4. **审计日志**: 记录所有重要操作的审计日志

## 📊 性能指标

- **处理速度**: 单份合同审核<3分钟
- **并发能力**: 支持50个并发用户
- **准确率**: 要素提取准确率>90%
- **可用性**: 系统可用性>99.5%

## 🤝 贡献指南

欢迎贡献代码、报告问题或提出建议！

1. 查看[开发文档](docs/development/)了解开发规范
2. 在Issues中报告问题或提出功能请求
3. 提交PR时请确保通过所有测试
4. 遵循代码审查流程

## 📄 许可证

本项目采用MIT许可证，详见[LICENSE](LICENSE)文件。

## 📞 联系我们

- 项目维护者: AI合同审核系统开发团队
- 技术支持: 请在Issues中提出问题
- 文档问题: 请查看[文档中心](docs/README.md)

---

**注意**: 本项目正在积极开发中，API可能会发生变化。生产环境使用前请充分测试。
