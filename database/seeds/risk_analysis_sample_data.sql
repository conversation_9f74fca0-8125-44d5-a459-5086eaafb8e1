-- =====================================================
-- 风险分析模块示例数据
-- 版本: v1.0
-- 创建日期: 2025-01-01
-- 说明: 风险分析系统的示例配置数据
-- =====================================================

-- 插入风险分类数据
INSERT INTO risk_categories (category_code, category_name, category_description, category_level, category_path) VALUES
('legal', '法律风险', '与法律条款、争议解决、合规性相关的风险', 1, 'legal'),
('commercial', '商业风险', '与商业条款、知识产权、竞争相关的风险', 1, 'commercial'),
('financial', '财务风险', '与付款、财务保障、成本相关的风险', 1, 'financial'),
('operational', '操作风险', '与履约、执行、质量相关的风险', 1, 'operational'),
('compliance', '合规风险', '与监管要求、行业标准相关的风险', 1, 'compliance'),
('technical', '技术风险', '与技术实施、系统可靠性相关的风险', 1, 'technical'),
('reputation', '声誉风险', '与声誉保护、保密性相关的风险', 1, 'reputation');

-- 插入风险规则数据
INSERT INTO risk_rules (
    rule_name, rule_code, risk_category_id, contract_types,
    keywords, regex_patterns, semantic_patterns,
    base_risk_level, impact_level, probability,
    description, risk_explanation, potential_consequences,
    mitigation_strategies, recommended_actions,
    legal_basis, is_active, priority
) VALUES
-- 法律风险规则
(
    '缺少争议解决条款',
    'LEGAL_001',
    (SELECT id FROM risk_categories WHERE category_code = 'legal'),
    '["sales", "service", "lease", "employment", "general"]',
    '["争议", "纠纷", "仲裁", "诉讼", "管辖", "法院"]',
    '["争议.*?解决", "纠纷.*?处理", "仲裁.*?委员会", "管辖.*?法院"]',
    '["争议解决机制", "纠纷处理程序", "法律管辖", "仲裁条款"]',
    'high',
    'major',
    'medium',
    '合同缺少明确的争议解决条款或争议解决机制不完善',
    '没有明确的争议解决机制可能导致纠纷处理困难，增加诉讼成本和时间，影响双方关系',
    '["纠纷处理程序不明确", "可能导致长期诉讼", "增加法律成本", "影响商业关系", "执行困难"]',
    '["添加详细的争议解决条款", "明确仲裁或诉讼程序", "指定管辖法院或仲裁机构", "约定争议解决的时限"]',
    '["补充争议解决条款", "选择合适的争议解决方式", "明确适用法律", "设置调解前置程序"]',
    '《中华人民共和国仲裁法》、《中华人民共和国民事诉讼法》',
    true,
    90
),
(
    '违约责任不明确',
    'LEGAL_002',
    (SELECT id FROM risk_categories WHERE category_code = 'legal'),
    '["sales", "service", "lease", "general"]',
    '["违约", "责任", "赔偿", "损失", "补偿", "违约金"]',
    '["违约.*?责任", "赔偿.*?损失", "违约金.*?标准"]',
    '["违约责任条款", "损害赔偿", "违约金计算", "免责条件"]',
    'medium',
    'moderate',
    'high',
    '违约责任条款不够明确或违约金标准不合理',
    '违约责任不明确可能导致违约后果难以确定，影响合同执行效果',
    '["违约后果不确定", "难以追究责任", "损失难以弥补", "执行效果差"]',
    '["明确违约责任条款", "设置合理的违约金标准", "约定损害赔偿计算方法"]',
    '["完善违约责任条款", "设置违约金上限", "明确免责条件"]',
    '《中华人民共和国合同法》相关条款',
    true,
    85
),

-- 财务风险规则
(
    '付款条件不明确',
    'FINANCIAL_001',
    (SELECT id FROM risk_categories WHERE category_code = 'financial'),
    '["sales", "service", "lease"]',
    '["付款", "支付", "款项", "费用", "价款", "逾期", "账期"]',
    '["付款.*?期限", "支付.*?方式", "逾期.*?利息", "账期.*?天"]',
    '["付款安排", "支付条件", "资金结算", "付款保障"]',
    'high',
    'major',
    'high',
    '付款条件、期限或方式不够明确，缺少付款保障措施',
    '模糊的付款条件可能导致资金回收困难，影响现金流和业务运营',
    '["资金回收延迟", "现金流压力", "坏账风险增加", "影响业务运营", "资金周转困难"]',
    '["明确付款期限和方式", "设置逾期付款违约金", "要求提供付款担保", "建立分期付款机制"]',
    '["详细规定付款条件", "设置分期付款安排", "建立催收机制", "要求银行保函"]',
    '《中华人民共和国合同法》、相关财务管理规定',
    true,
    95
),
(
    '缺少财务担保',
    'FINANCIAL_002',
    (SELECT id FROM risk_categories WHERE category_code = 'financial'),
    '["sales", "service", "lease"]',
    '["担保", "保证金", "抵押", "质押", "保函", "履约保证"]',
    '["履约.*?保证", "保证金.*?比例", "银行.*?保函"]',
    '["财务担保", "履约保障", "风险保证", "资金安全"]',
    'medium',
    'moderate',
    'medium',
    '缺少必要的财务担保措施，如履约保证金、银行保函等',
    '缺少财务担保可能增加合同履行风险，影响资金安全',
    '["履约风险增加", "资金安全隐患", "违约损失扩大", "追偿困难"]',
    '["设置履约保证金", "要求银行保函", "建立担保机制", "约定担保责任"]',
    '["要求提供履约保证金", "设置银行保函", "建立多重担保"]',
    '《中华人民共和国担保法》',
    true,
    80
),

-- 商业风险规则
(
    '知识产权归属不明',
    'COMMERCIAL_001',
    (SELECT id FROM risk_categories WHERE category_code = 'commercial'),
    '["service", "employment", "technology"]',
    '["知识产权", "专利", "著作权", "商标", "技术成果", "版权"]',
    '["知识产权.*?归属", "技术成果.*?所有", "专利.*?申请", "版权.*?归属"]',
    '["产权归属", "技术所有权", "创新成果", "知识产权保护"]',
    'medium',
    'moderate',
    'medium',
    '知识产权归属和使用权不够明确，可能引起产权纠纷',
    '知识产权归属不明可能导致后续纠纷，影响技术应用和商业化',
    '["知识产权纠纷", "技术应用受限", "商业化困难", "法律诉讼风险", "竞争优势丧失"]',
    '["明确知识产权归属", "约定使用权限", "建立保护机制", "设置转让条件"]',
    '["详细约定产权归属", "规定使用许可条件", "设置保密义务", "建立保护措施"]',
    '《中华人民共和国专利法》、《中华人民共和国著作权法》',
    true,
    75
),
(
    '竞业限制条款缺失',
    'COMMERCIAL_002',
    (SELECT id FROM risk_categories WHERE category_code = 'commercial'),
    '["employment", "service"]',
    '["竞业限制", "竞业禁止", "同业竞争", "商业秘密", "保密"]',
    '["竞业.*?限制", "竞业.*?禁止", "同业.*?竞争"]',
    '["竞业限制", "竞争保护", "商业机密", "行业限制"]',
    'low',
    'minor',
    'low',
    '缺少竞业限制条款，可能面临不正当竞争风险',
    '缺少竞业限制可能导致核心人员流失后的竞争风险',
    '["不正当竞争", "商业秘密泄露", "客户流失", "市场份额下降"]',
    '["设置合理的竞业限制", "明确限制范围和期限", "提供经济补偿"]',
    '["添加竞业限制条款", "设置补偿标准", "明确限制范围"]',
    '《中华人民共和国劳动合同法》',
    true,
    60
),

-- 操作风险规则
(
    '履约标准不具体',
    'OPERATIONAL_001',
    (SELECT id FROM risk_categories WHERE category_code = 'operational'),
    '["service", "sales", "construction"]',
    '["标准", "质量", "验收", "交付", "完成", "规格", "要求"]',
    '["质量.*?标准", "验收.*?条件", "交付.*?要求", "技术.*?规格"]',
    '["履约标准", "质量要求", "验收标准", "交付条件"]',
    'medium',
    'moderate',
    'high',
    '履约标准、质量要求或验收条件不够具体明确',
    '模糊的履约标准可能导致验收争议，影响项目交付和质量控制',
    '["验收标准争议", "质量纠纷", "项目延期", "客户满意度下降", "返工成本增加"]',
    '["制定详细的履约标准", "明确验收程序", "建立质量保证机制", "设置里程碑"]',
    '["细化技术规格", "设置验收里程碑", "建立质量监控体系", "明确验收标准"]',
    '相关行业标准和质量管理规范',
    true,
    85
),
(
    '交付时间不合理',
    'OPERATIONAL_002',
    (SELECT id FROM risk_categories WHERE category_code = 'operational'),
    '["service", "sales", "construction"]',
    '["交付", "完成", "期限", "工期", "时间", "延期", "逾期"]',
    '["交付.*?期限", "完成.*?时间", "工期.*?安排", "延期.*?责任"]',
    '["交付安排", "时间管理", "进度控制", "期限约定"]',
    'medium',
    'moderate',
    'high',
    '交付时间安排不合理或缺少延期处理机制',
    '不合理的交付时间可能导致项目延期，影响整体进度',
    '["项目延期风险", "进度失控", "成本增加", "客户不满", "违约风险"]',
    '["制定合理的时间安排", "设置缓冲时间", "建立延期处理机制", "明确里程碑"]',
    '["优化时间安排", "设置进度监控", "建立延期补救措施"]',
    '项目管理相关标准',
    true,
    75
),

-- 合规风险规则
(
    '缺少合规声明',
    'COMPLIANCE_001',
    (SELECT id FROM risk_categories WHERE category_code = 'compliance'),
    '["sales", "service", "general"]',
    '["合规", "法律", "法规", "标准", "认证", "许可", "资质"]',
    '["符合.*?法律", "遵守.*?法规", "合规.*?要求", "资质.*?证明"]',
    '["合规要求", "法律遵守", "监管合规", "资质认证"]',
    'medium',
    'major',
    'low',
    '缺少必要的合规声明和保证，可能存在合规风险',
    '缺少合规声明可能导致监管风险，影响业务合法性和市场准入',
    '["监管处罚风险", "业务合法性质疑", "声誉损失", "市场准入限制", "执照吊销风险"]',
    '["添加合规声明条款", "确保法律法规遵守", "建立合规监控机制", "定期合规审查"]',
    '["补充合规保证条款", "进行合规性审查", "建立合规管理体系", "设置合规监控"]',
    '相关行业法律法规和监管要求',
    true,
    70
),

-- 技术风险规则
(
    '技术风险未约定',
    'TECHNICAL_001',
    (SELECT id FROM risk_categories WHERE category_code = 'technical'),
    '["service", "technology", "construction"]',
    '["技术", "系统", "软件", "开发", "实施", "故障", "性能"]',
    '["技术.*?风险", "系统.*?故障", "开发.*?失败", "性能.*?要求"]',
    '["技术实现风险", "系统可靠性", "开发不确定性", "技术保障"]',
    'medium',
    'moderate',
    'medium',
    '技术实施风险和责任分担不明确，缺少技术保障措施',
    '技术项目存在实施风险，需要明确风险分担和应对措施',
    '["技术实施失败", "项目延期或超预算", "系统性能不达标", "技术支持不足", "兼容性问题"]',
    '["明确技术风险分担", "设置技术里程碑", "建立风险应对机制", "提供技术保障"]',
    '["制定技术实施计划", "设置风险控制点", "建立技术支持体系", "明确技术标准"]',
    '相关技术标准和行业规范',
    true,
    65
),

-- 声誉风险规则
(
    '保密义务不充分',
    'REPUTATION_001',
    (SELECT id FROM risk_categories WHERE category_code = 'reputation'),
    '["service", "employment", "technology"]',
    '["保密", "机密", "商业秘密", "泄露", "披露", "信息安全"]',
    '["保密.*?义务", "商业.*?秘密", "信息.*?泄露", "保密.*?协议"]',
    '["保密责任", "信息安全", "商业机密保护", "数据保护"]',
    'medium',
    'major',
    'low',
    '保密义务和信息保护措施不够充分，存在信息泄露风险',
    '保密措施不足可能导致商业信息泄露，影响竞争优势和声誉',
    '["商业信息泄露", "竞争优势丧失", "声誉损害", "客户信任度下降", "法律责任"]',
    '["加强保密条款", "明确保密范围", "设置违约责任", "建立信息安全制度"]',
    '["完善保密协议", "建立信息安全制度", "加强员工保密培训", "设置访问控制"]',
    '《中华人民共和国保守国家秘密法》、相关信息安全法规',
    true,
    70
);

-- 插入风险知识库统计数据
INSERT INTO risk_knowledge_stats (
    stat_date, total_rules, active_rules,
    rules_by_category, rules_by_level,
    total_detections, detection_accuracy, false_positive_rate,
    avg_processing_time, avg_ai_calls_per_analysis
) VALUES (
    CURRENT_DATE,
    (SELECT COUNT(*) FROM risk_rules),
    (SELECT COUNT(*) FROM risk_rules WHERE is_active = true),
    '{"legal": 2, "financial": 2, "commercial": 2, "operational": 2, "compliance": 1, "technical": 1, "reputation": 1}',
    '{"critical": 0, "high": 2, "medium": 7, "low": 2, "negligible": 0}',
    0,
    0.85,
    0.15,
    2.5,
    3.2
);

COMMIT;
