-- =====================================================
-- AI合同审核系统 - 示例配置数据
-- 版本: v1.0
-- 创建日期: 2025-01-01
-- 说明: 配置化要素管理系统的示例数据
-- =====================================================

-- 清理现有数据（仅用于开发环境）
-- TRUNCATE TABLE config_change_logs, contract_element_instances, element_extraction_logs, 
--          element_relationships, element_validation_rules, element_extraction_rules, 
--          contract_element_templates, element_categories, contract_type_recognition, 
--          contract_types RESTART IDENTITY CASCADE;

-- =====================================================
-- 1. 合同类型数据
-- =====================================================

INSERT INTO contract_types (type_code, type_name, type_description, sort_order, created_by) VALUES
('general', '通用合同', '适用于所有类型合同的通用配置', 0, 'system'),
('sales', '销售合同', '商品销售相关的合同', 10, 'system'),
('service', '服务合同', '服务提供相关的合同', 20, 'system'),
('employment', '劳动合同', '雇佣关系相关的合同', 30, 'system'),
('lease', '租赁合同', '房屋或设备租赁合同', 40, 'system'),
('partnership', '合作协议', '业务合作相关的协议', 50, 'system'),
('technology', '技术开发合同', '软件或技术开发合同', 60, 'system');

-- =====================================================
-- 2. 合同类型识别配置
-- =====================================================

-- 销售合同识别配置
INSERT INTO contract_type_recognition (contract_type_id, recognition_type, recognition_config, weight, confidence_threshold) VALUES
((SELECT id FROM contract_types WHERE type_code = 'sales'), 'keyword', 
 '{"keywords": ["销售合同", "买卖合同", "商品销售", "产品销售", "货物买卖"], "weights": [1.0, 1.0, 0.8, 0.8, 0.9]}', 
 1.0, 0.7),
((SELECT id FROM contract_types WHERE type_code = 'sales'), 'pattern', 
 '{"patterns": [".*销售.*合同.*", ".*买卖.*协议.*", ".*商品.*买卖.*"], "weights": [0.9, 0.8, 0.7]}', 
 0.8, 0.6);

-- 服务合同识别配置
INSERT INTO contract_type_recognition (contract_type_id, recognition_type, recognition_config, weight, confidence_threshold) VALUES
((SELECT id FROM contract_types WHERE type_code = 'service'), 'keyword', 
 '{"keywords": ["服务合同", "服务协议", "技术服务", "咨询服务", "维护服务"], "weights": [1.0, 1.0, 0.8, 0.8, 0.8]}', 
 1.0, 0.7),
((SELECT id FROM contract_types WHERE type_code = 'service'), 'pattern', 
 '{"patterns": [".*服务.*合同.*", ".*服务.*协议.*", ".*技术.*服务.*"], "weights": [0.9, 0.9, 0.8]}', 
 0.8, 0.6);

-- 技术开发合同识别配置
INSERT INTO contract_type_recognition (contract_type_id, recognition_type, recognition_config, weight, confidence_threshold) VALUES
((SELECT id FROM contract_types WHERE type_code = 'technology'), 'keyword', 
 '{"keywords": ["技术开发", "软件开发", "系统开发", "开发合同", "委托开发"], "weights": [1.0, 0.9, 0.9, 0.8, 0.8]}', 
 1.0, 0.7),
((SELECT id FROM contract_types WHERE type_code = 'technology'), 'pattern', 
 '{"patterns": [".*技术.*开发.*", ".*软件.*开发.*", ".*系统.*开发.*"], "weights": [0.9, 0.9, 0.8]}', 
 0.8, 0.6);

-- =====================================================
-- 3. 要素分类数据
-- =====================================================

-- 一级分类
INSERT INTO element_categories (category_code, category_name, category_description, sort_order, created_by) VALUES
('document_structure', '文档结构要素', '合同文档的结构性要素', 10, 'system'),
('party_info', '当事方信息', '合同各方的基本信息', 20, 'system'),
('contract_terms', '合同条款', '合同的具体条款内容', 30, 'system'),
('financial_terms', '财务条款', '与金钱相关的条款', 40, 'system'),
('time_terms', '时间条款', '与时间相关的条款', 50, 'system'),
('legal_terms', '法律条款', '法律责任和争议解决条款', 60, 'system');

-- 二级分类 - 文档结构要素
INSERT INTO element_categories (category_code, category_name, category_description, parent_category_id, sort_order, created_by) VALUES
('doc_title', '合同标题', '合同的标题信息', (SELECT id FROM element_categories WHERE category_code = 'document_structure'), 10, 'system'),
('doc_number', '合同编号', '合同的编号信息', (SELECT id FROM element_categories WHERE category_code = 'document_structure'), 20, 'system'),
('doc_date', '签署日期', '合同的签署日期', (SELECT id FROM element_categories WHERE category_code = 'document_structure'), 30, 'system');

-- 二级分类 - 当事方信息
INSERT INTO element_categories (category_code, category_name, category_description, parent_category_id, sort_order, created_by) VALUES
('party_basic', '基本信息', '当事方的基本信息', (SELECT id FROM element_categories WHERE category_code = 'party_info'), 10, 'system'),
('party_contact', '联系信息', '当事方的联系方式', (SELECT id FROM element_categories WHERE category_code = 'party_info'), 20, 'system'),
('party_legal', '法律信息', '当事方的法律相关信息', (SELECT id FROM element_categories WHERE category_code = 'party_info'), 30, 'system');

-- 二级分类 - 财务条款
INSERT INTO element_categories (category_code, category_name, category_description, parent_category_id, sort_order, created_by) VALUES
('payment_amount', '金额条款', '合同涉及的金额', (SELECT id FROM element_categories WHERE category_code = 'financial_terms'), 10, 'system'),
('payment_method', '支付方式', '款项支付的方式', (SELECT id FROM element_categories WHERE category_code = 'financial_terms'), 20, 'system'),
('payment_schedule', '支付计划', '款项支付的时间安排', (SELECT id FROM element_categories WHERE category_code = 'financial_terms'), 30, 'system');

-- =====================================================
-- 4. 要素模板数据
-- =====================================================

-- 通用要素模板
INSERT INTO contract_element_templates (
    element_name, element_code, element_display_name, element_description, 
    element_category_id, contract_type_id, data_type, is_required, weight, sort_order,
    element_config, validation_rules, help_text, created_by
) VALUES
-- 文档结构要素
('合同标题', 'contract_title', '合同标题', '合同的正式标题',
 (SELECT id FROM element_categories WHERE category_code = 'doc_title'), NULL, 'text', TRUE, 1.0, 10,
 '{"extraction_methods": ["title_position", "keyword_match", "ai_analysis"]}',
 '{"min_length": 5, "max_length": 200, "required": true}',
 '合同文档的正式标题，通常位于文档开头', 'system'),

('合同编号', 'contract_number', '合同编号', '合同的唯一编号',
 (SELECT id FROM element_categories WHERE category_code = 'doc_number'), NULL, 'text', FALSE, 0.8, 20,
 '{"extraction_methods": ["pattern_match", "keyword_match"]}',
 '{"pattern": "^[A-Z0-9\\-_]{5,30}$", "required": false}',
 '合同的唯一标识编号', 'system'),

('签署日期', 'signing_date', '签署日期', '合同的签署日期',
 (SELECT id FROM element_categories WHERE category_code = 'doc_date'), NULL, 'date', TRUE, 1.0, 30,
 '{"extraction_methods": ["date_pattern", "keyword_match"]}',
 '{"date_format": ["YYYY-MM-DD", "YYYY年MM月DD日"], "required": true}',
 '合同正式签署的日期', 'system'),

-- 当事方信息要素
('甲方名称', 'party_a_name', '甲方', '合同甲方的名称',
 (SELECT id FROM element_categories WHERE category_code = 'party_basic'), NULL, 'party_info', TRUE, 1.0, 40,
 '{"extraction_methods": ["party_pattern", "keyword_match"]}',
 '{"min_length": 2, "max_length": 100, "required": true}',
 '合同中甲方（第一方）的正式名称', 'system'),

('乙方名称', 'party_b_name', '乙方', '合同乙方的名称',
 (SELECT id FROM element_categories WHERE category_code = 'party_basic'), NULL, 'party_info', TRUE, 1.0, 50,
 '{"extraction_methods": ["party_pattern", "keyword_match"]}',
 '{"min_length": 2, "max_length": 100, "required": true}',
 '合同中乙方（第二方）的正式名称', 'system'),

-- 财务要素
('合同金额', 'contract_amount', '合同总金额', '合同涉及的总金额',
 (SELECT id FROM element_categories WHERE category_code = 'payment_amount'), NULL, 'amount', TRUE, 1.0, 60,
 '{"extraction_methods": ["amount_pattern", "keyword_match", "ai_analysis"]}',
 '{"min_value": 0, "currency": ["人民币", "USD", "EUR"], "required": true}',
 '合同中约定的总金额', 'system'),

('付款方式', 'payment_method', '付款方式', '款项的支付方式',
 (SELECT id FROM element_categories WHERE category_code = 'payment_method'), NULL, 'text', FALSE, 0.8, 70,
 '{"extraction_methods": ["keyword_match", "pattern_match"]}',
 '{"options": ["银行转账", "现金", "支票", "电汇"], "required": false}',
 '合同约定的付款方式', 'system');

-- 销售合同特有要素
INSERT INTO contract_element_templates (
    element_name, element_code, element_display_name, element_description, 
    element_category_id, contract_type_id, data_type, is_required, weight, sort_order,
    element_config, validation_rules, help_text, created_by
) VALUES
('商品名称', 'product_name', '商品/产品名称', '销售的商品或产品名称',
 (SELECT id FROM element_categories WHERE category_code = 'contract_terms'), 
 (SELECT id FROM contract_types WHERE type_code = 'sales'), 'text', TRUE, 1.0, 80,
 '{"extraction_methods": ["keyword_match", "pattern_match"]}',
 '{"min_length": 2, "max_length": 200, "required": true}',
 '销售合同中的商品或产品名称', 'system'),

('商品数量', 'product_quantity', '商品数量', '销售商品的数量',
 (SELECT id FROM element_categories WHERE category_code = 'contract_terms'), 
 (SELECT id FROM contract_types WHERE type_code = 'sales'), 'text', TRUE, 0.9, 90,
 '{"extraction_methods": ["number_pattern", "keyword_match"]}',
 '{"pattern": "^\\d+(\\.\\d+)?\\s*[个件台套批]?$", "required": true}',
 '销售商品的具体数量和单位', 'system');

-- 技术开发合同特有要素
INSERT INTO contract_element_templates (
    element_name, element_code, element_display_name, element_description, 
    element_category_id, contract_type_id, data_type, is_required, weight, sort_order,
    element_config, validation_rules, help_text, created_by
) VALUES
('开发内容', 'development_scope', '开发内容', '技术开发的具体内容和范围',
 (SELECT id FROM element_categories WHERE category_code = 'contract_terms'), 
 (SELECT id FROM contract_types WHERE type_code = 'technology'), 'text', TRUE, 1.0, 100,
 '{"extraction_methods": ["section_analysis", "keyword_match", "ai_analysis"]}',
 '{"min_length": 10, "max_length": 1000, "required": true}',
 '技术开发合同中的开发内容和范围描述', 'system'),

('交付时间', 'delivery_date', '交付时间', '项目的预期交付时间',
 (SELECT id FROM element_categories WHERE category_code = 'time_terms'), 
 (SELECT id FROM contract_types WHERE type_code = 'technology'), 'date', TRUE, 1.0, 110,
 '{"extraction_methods": ["date_pattern", "keyword_match"]}',
 '{"date_format": ["YYYY-MM-DD", "YYYY年MM月DD日"], "required": true}',
 '技术开发项目的预期完成和交付时间', 'system');-- =
====================================================
-- 5. 要素提取规则数据
-- =====================================================

-- 合同标题提取规则
INSERT INTO element_extraction_rules (element_template_id, rule_name, rule_type, rule_config, priority, confidence_score) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_title'), 
 '标题位置识别', 'regex', 
 '{"pattern": "^(.{5,100})(合同|协议|契约)(.{0,50})$", "flags": "MULTILINE", "group": 0}', 
 10, 0.9),
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_title'), 
 '标题关键词匹配', 'keyword', 
 '{"keywords": ["合同", "协议", "契约"], "context_window": 50, "position_weight": true}', 
 8, 0.8);

-- 合同编号提取规则
INSERT INTO element_extraction_rules (element_template_id, rule_name, rule_type, rule_config, priority, confidence_score) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_number'), 
 '编号模式匹配', 'regex', 
 '{"pattern": "(合同编号|编号|Contract No\\.?)[:：]?\\s*([A-Z0-9\\-_]{5,30})", "flags": "IGNORECASE", "group": 2}', 
 10, 0.9),
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_number'), 
 '编号关键词匹配', 'keyword', 
 '{"keywords": ["合同编号", "编号", "Contract No"], "extract_following": true, "max_distance": 20}', 
 8, 0.7);

-- 签署日期提取规则
INSERT INTO element_extraction_rules (element_template_id, rule_name, rule_type, rule_config, priority, confidence_score) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'signing_date'), 
 '中文日期格式', 'regex', 
 '{"pattern": "(\\d{4})年(\\d{1,2})月(\\d{1,2})日", "flags": "MULTILINE", "group": 0}', 
 10, 0.95),
((SELECT id FROM contract_element_templates WHERE element_code = 'signing_date'), 
 '数字日期格式', 'regex', 
 '{"pattern": "(\\d{4})[/-](\\d{1,2})[/-](\\d{1,2})", "flags": "MULTILINE", "group": 0}', 
 9, 0.9),
((SELECT id FROM contract_element_templates WHERE element_code = 'signing_date'), 
 '签署日期关键词', 'keyword', 
 '{"keywords": ["签署日期", "签订日期", "合同日期"], "extract_following": true, "date_extraction": true}', 
 7, 0.8);

-- 甲方名称提取规则
INSERT INTO element_extraction_rules (element_template_id, rule_name, rule_type, rule_config, priority, confidence_score) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'party_a_name'), 
 '甲方标准格式', 'regex', 
 '{"pattern": "甲\\s*方[:：]?\\s*([^，,；;\\n]{2,100})", "flags": "MULTILINE", "group": 1}', 
 10, 0.95),
((SELECT id FROM contract_element_templates WHERE element_code = 'party_a_name'), 
 '甲方关键词匹配', 'keyword', 
 '{"keywords": ["甲方"], "extract_following": true, "stop_chars": ["，", ",", "；", ";", "\\n"], "max_length": 100}', 
 8, 0.8);

-- 乙方名称提取规则
INSERT INTO element_extraction_rules (element_template_id, rule_name, rule_type, rule_config, priority, confidence_score) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'party_b_name'), 
 '乙方标准格式', 'regex', 
 '{"pattern": "乙\\s*方[:：]?\\s*([^，,；;\\n]{2,100})", "flags": "MULTILINE", "group": 1}', 
 10, 0.95),
((SELECT id FROM contract_element_templates WHERE element_code = 'party_b_name'), 
 '乙方关键词匹配', 'keyword', 
 '{"keywords": ["乙方"], "extract_following": true, "stop_chars": ["，", ",", "；", ";", "\\n"], "max_length": 100}', 
 8, 0.8);

-- 合同金额提取规则
INSERT INTO element_extraction_rules (element_template_id, rule_name, rule_type, rule_config, priority, confidence_score) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_amount'), 
 '人民币金额格式', 'regex', 
 '{"pattern": "(人民币|￥|RMB)\\s*([\\d,，]+(?:\\.\\d{1,2})?)\\s*(元|万元)?", "flags": "IGNORECASE", "group": 2}', 
 10, 0.9),
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_amount'), 
 '数字金额格式', 'regex', 
 '{"pattern": "([\\d,，]+(?:\\.\\d{1,2})?)\\s*(元|万元|USD|美元)", "flags": "IGNORECASE", "group": 1}', 
 9, 0.85),
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_amount'), 
 '金额关键词匹配', 'keyword', 
 '{"keywords": ["合同金额", "总金额", "价款", "费用"], "extract_following": true, "number_extraction": true}', 
 7, 0.8);

-- 销售合同特有规则
INSERT INTO element_extraction_rules (element_template_id, rule_name, rule_type, rule_config, priority, confidence_score) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'product_name'), 
 '商品名称关键词', 'keyword', 
 '{"keywords": ["商品名称", "产品名称", "货物名称", "标的物"], "extract_following": true, "max_length": 200}', 
 10, 0.9),
((SELECT id FROM contract_element_templates WHERE element_code = 'product_quantity'), 
 '数量格式匹配', 'regex', 
 '{"pattern": "(数量|总量)[:：]?\\s*([\\d,，]+(?:\\.\\d+)?)\\s*([个件台套批]?)", "flags": "IGNORECASE", "group": 2}', 
 10, 0.9);

-- 技术开发合同特有规则
INSERT INTO element_extraction_rules (element_template_id, rule_name, rule_type, rule_config, priority, confidence_score) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'development_scope'), 
 '开发内容关键词', 'keyword', 
 '{"keywords": ["开发内容", "开发范围", "项目内容", "工作内容"], "extract_following": true, "section_extraction": true}', 
 10, 0.9),
((SELECT id FROM contract_element_templates WHERE element_code = 'delivery_date'), 
 '交付时间格式', 'regex', 
 '{"pattern": "(交付时间|完成时间|交付日期)[:：]?\\s*(\\d{4}年\\d{1,2}月\\d{1,2}日|\\d{4}[/-]\\d{1,2}[/-]\\d{1,2})", "flags": "IGNORECASE", "group": 2}', 
 10, 0.9);

-- =====================================================
-- 6. 要素验证规则数据
-- =====================================================

-- 合同标题验证规则
INSERT INTO element_validation_rules (element_template_id, validation_name, validation_type, validation_config, error_message) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_title'), 
 '标题长度验证', 'format', 
 '{"min_length": 5, "max_length": 200}', 
 '合同标题长度应在5-200字符之间'),
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_title'), 
 '标题必需验证', 'business_rule', 
 '{"required": true, "not_empty": true}', 
 '合同标题不能为空');

-- 合同金额验证规则
INSERT INTO element_validation_rules (element_template_id, validation_name, validation_type, validation_config, error_message) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_amount'), 
 '金额范围验证', 'range', 
 '{"min_value": 0, "max_value": 999999999.99}', 
 '合同金额应为正数且不超过10亿'),
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_amount'), 
 '金额格式验证', 'format', 
 '{"pattern": "^\\d+(\\.\\d{1,2})?$", "currency_required": true}', 
 '金额格式不正确，应为数字格式');

-- 日期验证规则
INSERT INTO element_validation_rules (element_template_id, validation_name, validation_type, validation_config, error_message) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'signing_date'), 
 '日期格式验证', 'format', 
 '{"date_formats": ["YYYY-MM-DD", "YYYY年MM月DD日"], "valid_date": true}', 
 '日期格式不正确'),
((SELECT id FROM contract_element_templates WHERE element_code = 'signing_date'), 
 '日期范围验证', 'range', 
 '{"min_date": "1900-01-01", "max_date": "2100-12-31"}', 
 '日期应在合理范围内');

-- 当事方验证规则
INSERT INTO element_validation_rules (element_template_id, validation_name, validation_type, validation_config, error_message) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'party_a_name'), 
 '甲方名称长度验证', 'format', 
 '{"min_length": 2, "max_length": 100, "not_empty": true}', 
 '甲方名称长度应在2-100字符之间'),
((SELECT id FROM contract_element_templates WHERE element_code = 'party_b_name'), 
 '乙方名称长度验证', 'format', 
 '{"min_length": 2, "max_length": 100, "not_empty": true}', 
 '乙方名称长度应在2-100字符之间');

-- =====================================================
-- 7. 要素关系数据
-- =====================================================

-- 甲方和乙方不能相同
INSERT INTO element_relationships (source_element_id, target_element_id, relationship_type, relationship_config) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'party_a_name'),
 (SELECT id FROM contract_element_templates WHERE element_code = 'party_b_name'),
 'conflicts_with',
 '{"validation": "not_equal", "error_message": "甲方和乙方不能是同一个实体"}');

-- 合同金额依赖于当事方信息
INSERT INTO element_relationships (source_element_id, target_element_id, relationship_type, relationship_config) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_amount'),
 (SELECT id FROM contract_element_templates WHERE element_code = 'party_a_name'),
 'depends_on',
 '{"validation": "both_present", "error_message": "合同金额需要明确的当事方信息"}'),
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_amount'),
 (SELECT id FROM contract_element_templates WHERE element_code = 'party_b_name'),
 'depends_on',
 '{"validation": "both_present", "error_message": "合同金额需要明确的当事方信息"}');

-- 销售合同中商品名称和数量组合
INSERT INTO element_relationships (source_element_id, target_element_id, relationship_type, relationship_config) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'product_name'),
 (SELECT id FROM contract_element_templates WHERE element_code = 'product_quantity'),
 'combines_with',
 '{"validation": "both_or_neither", "error_message": "商品名称和数量应该同时存在"}');

-- 技术开发合同中开发内容和交付时间的依赖关系
INSERT INTO element_relationships (source_element_id, target_element_id, relationship_type, relationship_config) VALUES
((SELECT id FROM contract_element_templates WHERE element_code = 'delivery_date'),
 (SELECT id FROM contract_element_templates WHERE element_code = 'development_scope'),
 'depends_on',
 '{"validation": "scope_before_delivery", "error_message": "交付时间需要明确的开发内容"}');

-- =====================================================
-- 8. 初始化完成标记
-- =====================================================

-- 插入示例数据初始化完成标记
INSERT INTO config_change_logs (table_name, record_id, change_type, new_values, changed_by, change_reason)
VALUES ('system', 1, 'create', '{"action": "sample_data_initialized", "version": "1.0", "timestamp": "' || CURRENT_TIMESTAMP || '"}', 'system', '示例配置数据初始化完成');

-- 显示初始化统计信息
SELECT 
    '合同类型' as 配置项, COUNT(*) as 数量 FROM contract_types WHERE is_active = TRUE
UNION ALL
SELECT 
    '要素分类' as 配置项, COUNT(*) as 数量 FROM element_categories WHERE is_active = TRUE
UNION ALL
SELECT 
    '要素模板' as 配置项, COUNT(*) as 数量 FROM contract_element_templates WHERE is_active = TRUE
UNION ALL
SELECT 
    '提取规则' as 配置项, COUNT(*) as 数量 FROM element_extraction_rules WHERE is_active = TRUE
UNION ALL
SELECT 
    '验证规则' as 配置项, COUNT(*) as 数量 FROM element_validation_rules WHERE is_active = TRUE
UNION ALL
SELECT 
    '要素关系' as 配置项, COUNT(*) as 数量 FROM element_relationships WHERE is_active = TRUE;

COMMIT;

-- =====================================================
-- 示例数据初始化完成
-- =====================================================