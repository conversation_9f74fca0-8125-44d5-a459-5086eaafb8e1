-- =====================================================
-- AI合同审核系统 - 数据库初始化脚本
-- 版本: v1.0
-- 创建日期: 2025-01-01
-- 说明: 完整的数据库初始化，包括表结构和示例数据
-- =====================================================

-- 设置数据库参数
SET client_encoding = 'UTF8';
SET timezone = 'Asia/Shanghai';

-- 开始事务
BEGIN;

-- 显示初始化开始信息
SELECT 'AI合同审核系统数据库初始化开始...' as 状态, CURRENT_TIMESTAMP as 时间;

-- 1. 执行表结构创建
\echo '正在创建数据库表结构...'
\i database/schema/database_schema.sql

-- 2. 执行示例数据插入
\echo '正在插入示例配置数据...'
\i database/seeds/sample_data.sql

-- 显示初始化完成信息
SELECT 'AI合同审核系统数据库初始化完成!' as 状态, CURRENT_TIMESTAMP as 时间;

-- 显示数据库统计信息
SELECT 
    schemaname as 模式名,
    tablename as 表名,
    n_tup_ins as 插入行数,
    n_tup_upd as 更新行数,
    n_tup_del as 删除行数
FROM pg_stat_user_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

COMMIT;

-- =====================================================
-- 初始化完成
-- =====================================================