-- =====================================================
-- AI合同审核系统 - 配置化要素管理系统数据库表结构
-- 版本: v1.0
-- 创建日期: 2025-01-01
-- 说明: 支持完全配置化的要素管理系统
-- =====================================================

-- 设置数据库编码
SET client_encoding = 'UTF8';

-- 1. 合同类型管理表
CREATE TABLE contract_types (
    id SERIAL PRIMARY KEY,
    type_code VARCHAR(50) UNIQUE NOT NULL,           -- 类型代码，如 'sales', 'service'
    type_name VARCHAR(100) NOT NULL,                 -- 类型名称，如 '销售合同'
    type_description TEXT,                           -- 类型描述
    parent_type_id INTEGER REFERENCES contract_types(id), -- 支持类型继承
    is_active BOOLEAN DEFAULT TRUE,                  -- 是否启用
    sort_order INTEGER DEFAULT 0,                   -- 排序
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- 2. 合同类型识别配置表
CREATE TABLE contract_type_recognition (
    id SERIAL PRIMARY KEY,
    contract_type_id INTEGER NOT NULL REFERENCES contract_types(id),
    recognition_type VARCHAR(20) NOT NULL,           -- 'keyword', 'pattern', 'ai_prompt'
    recognition_config JSONB NOT NULL,               -- 识别配置
    weight DECIMAL(3,2) DEFAULT 1.0,                -- 权重
    confidence_threshold DECIMAL(3,2) DEFAULT 0.7,  -- 置信度阈值
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 要素分类表（支持多级分类）
CREATE TABLE element_categories (
    id SERIAL PRIMARY KEY,
    category_code VARCHAR(100) UNIQUE NOT NULL,      -- 分类代码
    category_name VARCHAR(200) NOT NULL,             -- 分类名称
    category_description TEXT,                       -- 分类描述
    parent_category_id INTEGER REFERENCES element_categories(id), -- 父分类
    category_level INTEGER DEFAULT 1,               -- 分类层级
    category_path VARCHAR(500),                      -- 分类路径，如 'root/document/title'
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- 4. 要素模板表（核心配置表）
CREATE TABLE contract_element_templates (
    id SERIAL PRIMARY KEY,
    element_name VARCHAR(200) NOT NULL,              -- 要素名称
    element_code VARCHAR(100) NOT NULL,              -- 要素代码
    element_display_name VARCHAR(200),               -- 显示名称
    element_description TEXT,                        -- 要素描述
    element_category_id INTEGER NOT NULL REFERENCES element_categories(id),
    contract_type_id INTEGER REFERENCES contract_types(id), -- NULL表示通用要素
    data_type VARCHAR(50) DEFAULT 'text',            -- 数据类型：text, amount, date, party_info, clause
    is_required BOOLEAN DEFAULT FALSE,               -- 是否必需
    is_unique BOOLEAN DEFAULT FALSE,                 -- 是否唯一
    weight DECIMAL(3,2) DEFAULT 1.0,                -- 权重
    sort_order INTEGER DEFAULT 0,                   -- 排序
    element_config JSONB,                           -- 要素配置（提取规则、验证规则等）
    validation_rules JSONB,                         -- 验证规则配置
    default_value TEXT,                             -- 默认值
    help_text TEXT,                                 -- 帮助文本
    is_active BOOLEAN DEFAULT TRUE,
    version INTEGER DEFAULT 1,                      -- 版本号
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    
    UNIQUE(element_code, contract_type_id, version)
);

-- 5. 要素提取规则表
CREATE TABLE element_extraction_rules (
    id SERIAL PRIMARY KEY,
    element_template_id INTEGER NOT NULL REFERENCES contract_element_templates(id),
    rule_name VARCHAR(200) NOT NULL,                -- 规则名称
    rule_type VARCHAR(50) NOT NULL,                 -- 规则类型：regex, keyword, ai_prompt, custom
    rule_config JSONB NOT NULL,                     -- 规则配置
    priority INTEGER DEFAULT 0,                     -- 优先级（数字越大优先级越高）
    confidence_score DECIMAL(3,2) DEFAULT 0.8,     -- 该规则的置信度
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. 要素验证规则表
CREATE TABLE element_validation_rules (
    id SERIAL PRIMARY KEY,
    element_template_id INTEGER NOT NULL REFERENCES contract_element_templates(id),
    validation_name VARCHAR(200) NOT NULL,          -- 验证规则名称
    validation_type VARCHAR(50) NOT NULL,           -- 验证类型：format, range, business_rule, custom
    validation_config JSONB NOT NULL,               -- 验证配置
    error_message TEXT,                             -- 错误消息
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 7. 要素关系表
CREATE TABLE element_relationships (
    id SERIAL PRIMARY KEY,
    source_element_id INTEGER NOT NULL REFERENCES contract_element_templates(id),
    target_element_id INTEGER NOT NULL REFERENCES contract_element_templates(id),
    relationship_type VARCHAR(50) NOT NULL,         -- 关系类型：depends_on, conflicts_with, combines_with, calculates_from
    relationship_config JSONB,                      -- 关系配置
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(source_element_id, target_element_id, relationship_type)
);

-- 8. 合同要素实例表（存储提取的要素数据）
CREATE TABLE contract_element_instances (
    id SERIAL PRIMARY KEY,
    contract_id VARCHAR(100) NOT NULL,              -- 合同ID
    element_template_id INTEGER NOT NULL REFERENCES contract_element_templates(id),
    extracted_value TEXT,                           -- 提取的值
    confidence_score DECIMAL(3,2),                  -- 置信度
    extraction_method VARCHAR(50),                  -- 提取方法
    source_position JSONB,                          -- 在原文档中的位置信息
    validation_status VARCHAR(20) DEFAULT 'pending', -- 验证状态：pending, valid, invalid
    validation_errors JSONB,                        -- 验证错误信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 9. 要素提取日志表
CREATE TABLE element_extraction_logs (
    id SERIAL PRIMARY KEY,
    contract_id VARCHAR(100) NOT NULL,
    element_template_id INTEGER REFERENCES contract_element_templates(id),
    extraction_status VARCHAR(20) NOT NULL,         -- 提取状态：success, failed, partial
    extraction_method VARCHAR(50),                  -- 提取方法
    processing_time INTEGER,                        -- 处理时间（毫秒）
    error_message TEXT,                             -- 错误信息
    extraction_details JSONB,                       -- 提取详情
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 10. 配置变更日志表
CREATE TABLE config_change_logs (
    id SERIAL PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,               -- 变更的表名
    record_id INTEGER NOT NULL,                     -- 记录ID
    change_type VARCHAR(20) NOT NULL,               -- 变更类型：create, update, delete
    old_values JSONB,                               -- 旧值
    new_values JSONB,                               -- 新值
    changed_by VARCHAR(100),                        -- 变更人
    change_reason TEXT,                             -- 变更原因
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);-- =====
================================================
-- 索引创建
-- =====================================================

-- 合同类型表索引
CREATE INDEX idx_contract_types_code ON contract_types(type_code);
CREATE INDEX idx_contract_types_active ON contract_types(is_active);
CREATE INDEX idx_contract_types_parent ON contract_types(parent_type_id);

-- 合同类型识别配置表索引
CREATE INDEX idx_contract_type_recognition_type ON contract_type_recognition(contract_type_id);
CREATE INDEX idx_contract_type_recognition_active ON contract_type_recognition(is_active);

-- 要素分类表索引
CREATE INDEX idx_element_categories_code ON element_categories(category_code);
CREATE INDEX idx_element_categories_parent ON element_categories(parent_category_id);
CREATE INDEX idx_element_categories_level ON element_categories(category_level);
CREATE INDEX idx_element_categories_path ON element_categories(category_path);

-- 要素模板表索引
CREATE INDEX idx_element_templates_code ON contract_element_templates(element_code);
CREATE INDEX idx_element_templates_category ON contract_element_templates(element_category_id);
CREATE INDEX idx_element_templates_contract_type ON contract_element_templates(contract_type_id);
CREATE INDEX idx_element_templates_data_type ON contract_element_templates(data_type);
CREATE INDEX idx_element_templates_active ON contract_element_templates(is_active);

-- 要素提取规则表索引
CREATE INDEX idx_extraction_rules_template ON element_extraction_rules(element_template_id);
CREATE INDEX idx_extraction_rules_type ON element_extraction_rules(rule_type);
CREATE INDEX idx_extraction_rules_priority ON element_extraction_rules(priority DESC);

-- 要素验证规则表索引
CREATE INDEX idx_validation_rules_template ON element_validation_rules(element_template_id);
CREATE INDEX idx_validation_rules_type ON element_validation_rules(validation_type);

-- 要素关系表索引
CREATE INDEX idx_element_relationships_source ON element_relationships(source_element_id);
CREATE INDEX idx_element_relationships_target ON element_relationships(target_element_id);
CREATE INDEX idx_element_relationships_type ON element_relationships(relationship_type);

-- 合同要素实例表索引
CREATE INDEX idx_element_instances_contract ON contract_element_instances(contract_id);
CREATE INDEX idx_element_instances_template ON contract_element_instances(element_template_id);
CREATE INDEX idx_element_instances_status ON contract_element_instances(validation_status);
CREATE INDEX idx_element_instances_created ON contract_element_instances(created_at);

-- 要素提取日志表索引
CREATE INDEX idx_extraction_logs_contract ON element_extraction_logs(contract_id);
CREATE INDEX idx_extraction_logs_template ON element_extraction_logs(element_template_id);
CREATE INDEX idx_extraction_logs_status ON element_extraction_logs(extraction_status);
CREATE INDEX idx_extraction_logs_created ON element_extraction_logs(created_at);

-- 配置变更日志表索引
CREATE INDEX idx_config_change_logs_table ON config_change_logs(table_name);
CREATE INDEX idx_config_change_logs_record ON config_change_logs(record_id);
CREATE INDEX idx_config_change_logs_type ON config_change_logs(change_type);
CREATE INDEX idx_config_change_logs_created ON config_change_logs(created_at);

-- =====================================================
-- 触发器函数
-- =====================================================

-- 更新时间戳触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间戳触发器
CREATE TRIGGER update_contract_types_updated_at BEFORE UPDATE ON contract_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contract_type_recognition_updated_at BEFORE UPDATE ON contract_type_recognition FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_element_categories_updated_at BEFORE UPDATE ON element_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contract_element_templates_updated_at BEFORE UPDATE ON contract_element_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_element_extraction_rules_updated_at BEFORE UPDATE ON element_extraction_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_element_validation_rules_updated_at BEFORE UPDATE ON element_validation_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_element_relationships_updated_at BEFORE UPDATE ON element_relationships FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contract_element_instances_updated_at BEFORE UPDATE ON contract_element_instances FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 分类路径自动更新触发器函数
CREATE OR REPLACE FUNCTION update_category_path()
RETURNS TRIGGER AS $$
DECLARE
    parent_path VARCHAR(500);
BEGIN
    IF NEW.parent_category_id IS NULL THEN
        NEW.category_path = NEW.category_code;
        NEW.category_level = 1;
    ELSE
        SELECT category_path, category_level INTO parent_path, NEW.category_level 
        FROM element_categories 
        WHERE id = NEW.parent_category_id;
        
        NEW.category_path = parent_path || '/' || NEW.category_code;
        NEW.category_level = NEW.category_level + 1;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_element_categories_path BEFORE INSERT OR UPDATE ON element_categories FOR EACH ROW EXECUTE FUNCTION update_category_path();

-- 配置变更日志触发器函数
CREATE OR REPLACE FUNCTION log_config_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO config_change_logs (table_name, record_id, change_type, old_values, changed_by)
        VALUES (TG_TABLE_NAME, OLD.id, 'delete', row_to_json(OLD), current_user);
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO config_change_logs (table_name, record_id, change_type, old_values, new_values, changed_by)
        VALUES (TG_TABLE_NAME, NEW.id, 'update', row_to_json(OLD), row_to_json(NEW), current_user);
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO config_change_logs (table_name, record_id, change_type, new_values, changed_by)
        VALUES (TG_TABLE_NAME, NEW.id, 'create', row_to_json(NEW), current_user);
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

-- 为配置表创建变更日志触发器
CREATE TRIGGER log_contract_types_changes AFTER INSERT OR UPDATE OR DELETE ON contract_types FOR EACH ROW EXECUTE FUNCTION log_config_changes();
CREATE TRIGGER log_element_categories_changes AFTER INSERT OR UPDATE OR DELETE ON element_categories FOR EACH ROW EXECUTE FUNCTION log_config_changes();
CREATE TRIGGER log_element_templates_changes AFTER INSERT OR UPDATE OR DELETE ON contract_element_templates FOR EACH ROW EXECUTE FUNCTION log_config_changes();
CREATE TRIGGER log_extraction_rules_changes AFTER INSERT OR UPDATE OR DELETE ON element_extraction_rules FOR EACH ROW EXECUTE FUNCTION log_config_changes();
CREATE TRIGGER log_validation_rules_changes AFTER INSERT OR UPDATE OR DELETE ON element_validation_rules FOR EACH ROW EXECUTE FUNCTION log_config_changes();
CREATE TRIGGER log_element_relationships_changes AFTER INSERT OR UPDATE OR DELETE ON element_relationships FOR EACH ROW EXECUTE FUNCTION log_config_changes();

-- =====================================================
-- 数据完整性约束
-- =====================================================

-- 确保要素关系不能自引用
ALTER TABLE element_relationships ADD CONSTRAINT chk_no_self_reference CHECK (source_element_id != target_element_id);

-- 确保置信度在有效范围内
ALTER TABLE element_extraction_rules ADD CONSTRAINT chk_confidence_range CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0);
ALTER TABLE contract_element_instances ADD CONSTRAINT chk_instance_confidence_range CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0);

-- 确保权重在有效范围内
ALTER TABLE contract_types ADD CONSTRAINT chk_weight_range CHECK (sort_order >= 0);
ALTER TABLE element_categories ADD CONSTRAINT chk_category_weight_range CHECK (sort_order >= 0);
ALTER TABLE contract_element_templates ADD CONSTRAINT chk_template_weight_range CHECK (weight >= 0.0 AND weight <= 10.0);

-- 确保分类层级合理
ALTER TABLE element_categories ADD CONSTRAINT chk_category_level CHECK (category_level > 0 AND category_level <= 10);

-- =====================================================
-- 注释说明
-- =====================================================

COMMENT ON TABLE contract_types IS '合同类型管理表，支持类型继承';
COMMENT ON TABLE contract_type_recognition IS '合同类型识别配置表，支持多种识别方式';
COMMENT ON TABLE element_categories IS '要素分类表，支持多级分类结构';
COMMENT ON TABLE contract_element_templates IS '要素模板表，核心配置表';
COMMENT ON TABLE element_extraction_rules IS '要素提取规则表，支持多种提取方式';
COMMENT ON TABLE element_validation_rules IS '要素验证规则表，支持多种验证类型';
COMMENT ON TABLE element_relationships IS '要素关系表，支持复杂的要素关系';
COMMENT ON TABLE contract_element_instances IS '合同要素实例表，存储提取的要素数据';
COMMENT ON TABLE element_extraction_logs IS '要素提取日志表，记录提取过程';
COMMENT ON TABLE config_change_logs IS '配置变更日志表，记录所有配置变更';

-- =====================================================
-- 初始化完成
-- =====================================================

-- 插入系统初始化标记
INSERT INTO config_change_logs (table_name, record_id, change_type, new_values, changed_by, change_reason)
VALUES ('system', 0, 'create', '{"action": "database_initialized", "version": "1.0"}', 'system', '数据库初始化完成');

COMMIT;