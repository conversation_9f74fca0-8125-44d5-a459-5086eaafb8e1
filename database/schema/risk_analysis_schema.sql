-- =====================================================
-- AI合同审核系统 - 风险分析模块数据库表结构
-- 版本: v1.0
-- 创建日期: 2025-01-01
-- 说明: 多维度合同风险识别和评估系统
-- =====================================================

-- 设置数据库编码
SET client_encoding = 'UTF8';

-- 1. 风险分类表
CREATE TABLE risk_categories (
    id SERIAL PRIMARY KEY,
    category_code VARCHAR(50) UNIQUE NOT NULL,       -- 分类代码：legal, commercial, financial等
    category_name VARCHAR(100) NOT NULL,             -- 分类名称
    category_description TEXT,                       -- 分类描述
    parent_category_id INTEGER REFERENCES risk_categories(id), -- 支持分类继承
    category_level INTEGER DEFAULT 1,               -- 分类层级
    category_path VARCHAR(500),                      -- 分类路径
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 风险规则表
CREATE TABLE risk_rules (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(200) NOT NULL,                 -- 规则名称
    rule_code VARCHAR(100) UNIQUE NOT NULL,          -- 规则代码
    risk_category_id INTEGER NOT NULL REFERENCES risk_categories(id),
    contract_types JSONB NOT NULL,                   -- 适用合同类型数组
    
    -- 检测规则配置
    keywords JSONB DEFAULT '[]',                     -- 风险关键词数组
    regex_patterns JSONB DEFAULT '[]',               -- 正则表达式数组
    semantic_patterns JSONB DEFAULT '[]',            -- 语义模式数组
    ai_prompts JSONB DEFAULT '[]',                   -- AI提示词数组
    
    -- 风险评估
    base_risk_level VARCHAR(20) NOT NULL,           -- 基础风险等级
    impact_level VARCHAR(20) NOT NULL,              -- 影响程度
    probability VARCHAR(20) NOT NULL,               -- 发生概率
    risk_weight DECIMAL(3,2) DEFAULT 1.0,          -- 风险权重
    
    -- 规则描述
    description TEXT NOT NULL,                       -- 风险描述
    risk_explanation TEXT,                          -- 风险说明
    potential_consequences JSONB DEFAULT '[]',       -- 潜在后果数组
    
    -- 缓解建议
    mitigation_strategies JSONB DEFAULT '[]',        -- 缓解策略数组
    recommended_actions JSONB DEFAULT '[]',          -- 建议措施数组
    
    -- 法律依据
    legal_basis TEXT,                               -- 法律依据
    regulatory_requirements JSONB DEFAULT '[]',     -- 监管要求数组
    
    -- 规则配置
    detection_config JSONB,                         -- 检测配置
    scoring_config JSONB,                           -- 评分配置
    
    -- 元数据
    is_active BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 0,                     -- 优先级
    version INTEGER DEFAULT 1,                      -- 版本号
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- 3. 风险点实例表
CREATE TABLE risk_points (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contract_id VARCHAR(100) NOT NULL,              -- 合同ID
    rule_id INTEGER NOT NULL REFERENCES risk_rules(id),
    
    -- 基本信息
    risk_name VARCHAR(200) NOT NULL,                -- 风险名称
    risk_category VARCHAR(50) NOT NULL,             -- 风险分类
    risk_level VARCHAR(20) NOT NULL,               -- 风险等级
    
    -- 位置信息
    matched_text TEXT NOT NULL,                     -- 匹配的文本
    start_position INTEGER NOT NULL,               -- 开始位置
    end_position INTEGER NOT NULL,                 -- 结束位置
    paragraph_index INTEGER,                       -- 段落索引
    context_text TEXT,                             -- 上下文
    
    -- 风险评估
    impact_level VARCHAR(20) NOT NULL,             -- 影响程度
    probability VARCHAR(20) NOT NULL,              -- 发生概率
    risk_score DECIMAL(4,2) NOT NULL,              -- 风险评分 (0-10)
    confidence DECIMAL(3,2) NOT NULL,              -- 识别置信度 (0-1)
    
    -- 风险详情
    description TEXT NOT NULL,                      -- 风险描述
    potential_consequences JSONB DEFAULT '[]',      -- 潜在后果
    affected_parties JSONB DEFAULT '[]',           -- 受影响方
    
    -- 缓解建议
    mitigation_strategies JSONB DEFAULT '[]',       -- 缓解策略
    recommended_actions JSONB DEFAULT '[]',         -- 建议措施
    urgency_level VARCHAR(50) NOT NULL,            -- 紧急程度
    
    -- 相关信息
    related_clauses JSONB DEFAULT '[]',            -- 相关条款
    legal_basis TEXT,                              -- 法律依据
    
    -- 检测信息
    detection_method VARCHAR(50) NOT NULL,         -- 检测方法
    detection_details JSONB,                       -- 检测详情
    ai_analysis_result JSONB,                      -- AI分析结果
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'detected',         -- 状态：detected, reviewed, resolved
    review_notes TEXT,                             -- 审查备注
    resolution_actions JSONB,                      -- 解决措施
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. 风险评估结果表
CREATE TABLE risk_assessments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contract_id VARCHAR(100) NOT NULL,              -- 合同ID
    contract_type VARCHAR(100) NOT NULL,            -- 合同类型
    
    -- 整体评估
    overall_risk_level VARCHAR(20) NOT NULL,        -- 整体风险等级
    total_risk_score DECIMAL(4,2) NOT NULL,         -- 总风险评分
    risk_distribution JSONB NOT NULL,               -- 风险分布统计
    
    -- 分类风险评估
    category_risks JSONB DEFAULT '{}',              -- 分类风险详情
    
    -- 关键风险
    critical_risks_count INTEGER DEFAULT 0,         -- 严重风险数量
    high_risks_count INTEGER DEFAULT 0,            -- 高风险数量
    medium_risks_count INTEGER DEFAULT 0,          -- 中风险数量
    low_risks_count INTEGER DEFAULT 0,             -- 低风险数量
    
    -- 统计信息
    total_risks INTEGER NOT NULL,                   -- 风险总数
    risks_by_category JSONB DEFAULT '{}',          -- 按分类统计
    risks_by_level JSONB DEFAULT '{}',             -- 按等级统计
    
    -- 评估建议
    priority_actions JSONB DEFAULT '[]',           -- 优先措施
    risk_mitigation_plan JSONB DEFAULT '[]',       -- 风险缓解计划
    
    -- 处理信息
    processing_duration DECIMAL(8,3),              -- 处理耗时（秒）
    rules_applied INTEGER DEFAULT 0,               -- 应用的规则数
    ai_calls_used INTEGER DEFAULT 0,               -- AI调用次数
    
    -- 质量指标
    detection_confidence DECIMAL(3,2),             -- 检测置信度
    coverage_score DECIMAL(3,2),                   -- 覆盖率评分
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. 风险缓解计划表
CREATE TABLE risk_mitigation_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    risk_point_id UUID NOT NULL REFERENCES risk_points(id),
    contract_id VARCHAR(100) NOT NULL,
    
    -- 缓解计划基本信息
    plan_name VARCHAR(200) NOT NULL,               -- 计划名称
    mitigation_type VARCHAR(50) NOT NULL,          -- 缓解类型
    priority INTEGER NOT NULL,                     -- 优先级 (1-5)
    
    -- 缓解措施
    actions JSONB NOT NULL,                        -- 具体措施数组
    timeline VARCHAR(200),                         -- 时间安排
    responsible_party VARCHAR(200),                -- 负责方
    
    -- 成本和效果
    estimated_cost DECIMAL(12,2),                  -- 预估成本
    expected_effectiveness DECIMAL(3,2),           -- 预期效果 (0-1)
    
    -- 监控指标
    success_metrics JSONB DEFAULT '[]',            -- 成功指标
    review_schedule VARCHAR(200),                  -- 审查计划
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'planned',          -- 状态：planned, in_progress, completed, cancelled
    progress_notes TEXT,                           -- 进展备注
    completion_date TIMESTAMP,                     -- 完成日期
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. 风险分析日志表
CREATE TABLE risk_analysis_logs (
    id SERIAL PRIMARY KEY,
    contract_id VARCHAR(100) NOT NULL,
    analysis_type VARCHAR(50) NOT NULL,            -- 分析类型：full, incremental, rule_based
    
    -- 分析配置
    analysis_config JSONB,                         -- 分析配置
    rules_applied JSONB,                           -- 应用的规则
    
    -- 分析结果
    risks_detected INTEGER DEFAULT 0,              -- 检测到的风险数
    processing_time DECIMAL(8,3),                  -- 处理时间
    ai_calls_made INTEGER DEFAULT 0,               -- AI调用次数
    
    -- 状态信息
    status VARCHAR(20) NOT NULL,                   -- 状态：success, failed, partial
    error_message TEXT,                            -- 错误信息
    analysis_details JSONB,                        -- 分析详情
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 7. 风险知识库统计表
CREATE TABLE risk_knowledge_stats (
    id SERIAL PRIMARY KEY,
    stat_date DATE NOT NULL DEFAULT CURRENT_DATE,
    
    -- 规则统计
    total_rules INTEGER DEFAULT 0,
    active_rules INTEGER DEFAULT 0,
    rules_by_category JSONB DEFAULT '{}',
    rules_by_level JSONB DEFAULT '{}',
    
    -- 检测统计
    total_detections INTEGER DEFAULT 0,
    detection_accuracy DECIMAL(3,2),
    false_positive_rate DECIMAL(3,2),
    
    -- 性能统计
    avg_processing_time DECIMAL(8,3),
    avg_ai_calls_per_analysis DECIMAL(4,1),
    
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(stat_date)
);

-- =====================================================
-- 索引创建
-- =====================================================

-- 风险分类表索引
CREATE INDEX idx_risk_categories_code ON risk_categories(category_code);
CREATE INDEX idx_risk_categories_parent ON risk_categories(parent_category_id);
CREATE INDEX idx_risk_categories_level ON risk_categories(category_level);

-- 风险规则表索引
CREATE INDEX idx_risk_rules_code ON risk_rules(rule_code);
CREATE INDEX idx_risk_rules_category ON risk_rules(risk_category_id);
CREATE INDEX idx_risk_rules_level ON risk_rules(base_risk_level);
CREATE INDEX idx_risk_rules_active ON risk_rules(is_active);
CREATE INDEX idx_risk_rules_priority ON risk_rules(priority DESC);
CREATE INDEX idx_risk_rules_contract_types ON risk_rules USING GIN(contract_types);

-- 风险点实例表索引
CREATE INDEX idx_risk_points_contract ON risk_points(contract_id);
CREATE INDEX idx_risk_points_rule ON risk_points(rule_id);
CREATE INDEX idx_risk_points_category ON risk_points(risk_category);
CREATE INDEX idx_risk_points_level ON risk_points(risk_level);
CREATE INDEX idx_risk_points_score ON risk_points(risk_score DESC);
CREATE INDEX idx_risk_points_status ON risk_points(status);
CREATE INDEX idx_risk_points_created ON risk_points(created_at);

-- 风险评估结果表索引
CREATE INDEX idx_risk_assessments_contract ON risk_assessments(contract_id);
CREATE INDEX idx_risk_assessments_type ON risk_assessments(contract_type);
CREATE INDEX idx_risk_assessments_level ON risk_assessments(overall_risk_level);
CREATE INDEX idx_risk_assessments_score ON risk_assessments(total_risk_score DESC);
CREATE INDEX idx_risk_assessments_created ON risk_assessments(created_at);

-- 风险缓解计划表索引
CREATE INDEX idx_mitigation_plans_risk_point ON risk_mitigation_plans(risk_point_id);
CREATE INDEX idx_mitigation_plans_contract ON risk_mitigation_plans(contract_id);
CREATE INDEX idx_mitigation_plans_priority ON risk_mitigation_plans(priority);
CREATE INDEX idx_mitigation_plans_status ON risk_mitigation_plans(status);

-- 风险分析日志表索引
CREATE INDEX idx_risk_analysis_logs_contract ON risk_analysis_logs(contract_id);
CREATE INDEX idx_risk_analysis_logs_type ON risk_analysis_logs(analysis_type);
CREATE INDEX idx_risk_analysis_logs_status ON risk_analysis_logs(status);
CREATE INDEX idx_risk_analysis_logs_created ON risk_analysis_logs(created_at);

-- =====================================================
-- 触发器函数
-- =====================================================

-- 更新时间戳触发器函数
CREATE OR REPLACE FUNCTION update_risk_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间戳触发器
CREATE TRIGGER update_risk_categories_updated_at BEFORE UPDATE ON risk_categories FOR EACH ROW EXECUTE FUNCTION update_risk_updated_at_column();
CREATE TRIGGER update_risk_rules_updated_at BEFORE UPDATE ON risk_rules FOR EACH ROW EXECUTE FUNCTION update_risk_updated_at_column();
CREATE TRIGGER update_risk_points_updated_at BEFORE UPDATE ON risk_points FOR EACH ROW EXECUTE FUNCTION update_risk_updated_at_column();
CREATE TRIGGER update_risk_assessments_updated_at BEFORE UPDATE ON risk_assessments FOR EACH ROW EXECUTE FUNCTION update_risk_updated_at_column();
CREATE TRIGGER update_risk_mitigation_plans_updated_at BEFORE UPDATE ON risk_mitigation_plans FOR EACH ROW EXECUTE FUNCTION update_risk_updated_at_column();

-- 风险分类路径自动更新触发器函数
CREATE OR REPLACE FUNCTION update_risk_category_path()
RETURNS TRIGGER AS $$
DECLARE
    parent_path VARCHAR(500);
BEGIN
    IF NEW.parent_category_id IS NULL THEN
        NEW.category_path = NEW.category_code;
        NEW.category_level = 1;
    ELSE
        SELECT category_path, category_level INTO parent_path, NEW.category_level 
        FROM risk_categories 
        WHERE id = NEW.parent_category_id;
        
        NEW.category_path = parent_path || '/' || NEW.category_code;
        NEW.category_level = NEW.category_level + 1;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_risk_categories_path BEFORE INSERT OR UPDATE ON risk_categories FOR EACH ROW EXECUTE FUNCTION update_risk_category_path();

-- =====================================================
-- 数据完整性约束
-- =====================================================

-- 确保风险评分在有效范围内
ALTER TABLE risk_points ADD CONSTRAINT chk_risk_score_range CHECK (risk_score >= 0.0 AND risk_score <= 10.0);
ALTER TABLE risk_assessments ADD CONSTRAINT chk_total_risk_score_range CHECK (total_risk_score >= 0.0 AND total_risk_score <= 10.0);

-- 确保置信度在有效范围内
ALTER TABLE risk_points ADD CONSTRAINT chk_confidence_range CHECK (confidence >= 0.0 AND confidence <= 1.0);
ALTER TABLE risk_assessments ADD CONSTRAINT chk_detection_confidence_range CHECK (detection_confidence >= 0.0 AND detection_confidence <= 1.0);
ALTER TABLE risk_assessments ADD CONSTRAINT chk_coverage_score_range CHECK (coverage_score >= 0.0 AND coverage_score <= 1.0);

-- 确保位置信息合理
ALTER TABLE risk_points ADD CONSTRAINT chk_position_range CHECK (start_position >= 0 AND end_position > start_position);

-- 确保优先级在有效范围内
ALTER TABLE risk_mitigation_plans ADD CONSTRAINT chk_priority_range CHECK (priority >= 1 AND priority <= 5);

-- 确保预期效果在有效范围内
ALTER TABLE risk_mitigation_plans ADD CONSTRAINT chk_effectiveness_range CHECK (expected_effectiveness >= 0.0 AND expected_effectiveness <= 1.0);

-- =====================================================
-- 注释说明
-- =====================================================

COMMENT ON TABLE risk_categories IS '风险分类表，支持多级分类结构';
COMMENT ON TABLE risk_rules IS '风险规则表，定义风险识别规则和评估标准';
COMMENT ON TABLE risk_points IS '风险点实例表，存储识别的具体风险点';
COMMENT ON TABLE risk_assessments IS '风险评估结果表，存储整体风险评估结果';
COMMENT ON TABLE risk_mitigation_plans IS '风险缓解计划表，管理风险处理措施';
COMMENT ON TABLE risk_analysis_logs IS '风险分析日志表，记录分析过程和结果';
COMMENT ON TABLE risk_knowledge_stats IS '风险知识库统计表，记录统计信息';

-- =====================================================
-- 初始化完成
-- =====================================================

COMMIT;
