# AI合同审核系统 - 数据库文档

## 📁 目录结构

```
database/
├── README.md                    # 数据库文档（本文件）
├── init_database.sql           # 数据库初始化脚本
├── schema/                     # 数据库结构
│   └── database_schema.sql     # 完整表结构定义
└── seeds/                      # 示例数据
    └── sample_data.sql         # 配置示例数据
```

## 🗄️ 数据库设计概述

### 核心表结构（10个表）

1. **contract_types** - 合同类型管理
2. **contract_type_recognition** - 合同类型识别配置
3. **element_categories** - 要素分类管理
4. **contract_element_templates** - 要素模板配置（核心表）
5. **element_extraction_rules** - 要素提取规则
6. **element_validation_rules** - 要素验证规则
7. **element_relationships** - 要素关系配置
8. **contract_element_instances** - 要素实例数据
9. **element_extraction_logs** - 提取日志
10. **config_change_logs** - 配置变更日志

### 设计特点

- ✅ **完全配置化**：支持无代码配置新合同类型和要素
- ✅ **多层级分类**：支持无限层级的要素分类
- ✅ **灵活规则**：支持多种提取和验证规则
- ✅ **关系管理**：支持复杂的要素关系（依赖、互斥、组合、计算）
- ✅ **版本控制**：完整的配置变更历史
- ✅ **性能优化**：合理的索引和约束设计

## 🚀 快速开始

### 1. 数据库初始化

```bash
# 方法1：使用完整初始化脚本（推荐）
psql -U username -d database_name -f database/init_database.sql

# 方法2：分步执行
psql -U username -d database_name -f database/schema/database_schema.sql
psql -U username -d database_name -f database/seeds/sample_data.sql
```

### 2. 验证安装

```sql
-- 检查表是否创建成功
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- 检查示例数据
SELECT 
    '合同类型' as 配置项, COUNT(*) as 数量 FROM contract_types
UNION ALL
SELECT 
    '要素模板' as 配置项, COUNT(*) as 数量 FROM contract_element_templates
UNION ALL
SELECT 
    '提取规则' as 配置项, COUNT(*) as 数量 FROM element_extraction_rules;
```

## 📊 示例数据说明

### 合同类型（7种）
- `general` - 通用合同
- `sales` - 销售合同
- `service` - 服务合同
- `employment` - 劳动合同
- `lease` - 租赁合同
- `partnership` - 合作协议
- `technology` - 技术开发合同

### 要素分类（6个一级分类）
- `document_structure` - 文档结构要素
- `party_info` - 当事方信息
- `contract_terms` - 合同条款
- `financial_terms` - 财务条款
- `time_terms` - 时间条款
- `legal_terms` - 法律条款

### 要素模板（10个核心要素）
- 合同标题、合同编号、签署日期
- 甲方名称、乙方名称
- 合同金额、付款方式
- 商品名称、商品数量（销售合同）
- 开发内容、交付时间（技术开发合同）

## 🔧 数据库维护

### 备份数据库

```bash
# 备份完整数据库
pg_dump -U username -d database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 仅备份数据（不含结构）
pg_dump -U username -d database_name --data-only > data_backup_$(date +%Y%m%d_%H%M%S).sql

# 仅备份结构（不含数据）
pg_dump -U username -d database_name --schema-only > schema_backup_$(date +%Y%m%d_%H%M%S).sql
```

### 恢复数据库

```bash
# 恢复完整数据库
psql -U username -d database_name < backup_file.sql

# 恢复数据
psql -U username -d database_name < data_backup_file.sql
```

### 清理数据

```sql
-- 清理所有配置数据（谨慎使用）
TRUNCATE TABLE config_change_logs, contract_element_instances, element_extraction_logs, 
         element_relationships, element_validation_rules, element_extraction_rules, 
         contract_element_templates, element_categories, contract_type_recognition, 
         contract_types RESTART IDENTITY CASCADE;

-- 重新插入示例数据
\i database/seeds/sample_data.sql
```

## 📈 性能优化

### 索引策略
- 所有外键都有对应索引
- 查询频繁的字段建立索引
- 复合索引优化复杂查询

### 查询优化建议

```sql
-- 1. 使用索引字段进行查询
SELECT * FROM contract_element_templates 
WHERE element_code = 'contract_title' AND is_active = TRUE;

-- 2. 利用分类路径快速查询
SELECT * FROM element_categories 
WHERE category_path LIKE 'document_structure/%';

-- 3. 使用JSONB字段的GIN索引
CREATE INDEX idx_element_config_gin ON contract_element_templates USING GIN (element_config);
```

## 🔒 安全配置

### 用户权限管理

```sql
-- 创建应用用户
CREATE USER contract_app WITH PASSWORD 'secure_password';

-- 授予必要权限
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO contract_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO contract_app;

-- 限制删除权限（仅管理员）
REVOKE DELETE ON contract_types, element_categories, contract_element_templates FROM contract_app;
```

### 数据加密

```sql
-- 敏感数据加密存储（如需要）
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- 示例：加密存储合同内容
-- UPDATE contract_element_instances 
-- SET extracted_value = crypt(extracted_value, gen_salt('bf'));
```

## 📋 常用查询

### 配置查询

```sql
-- 查看所有合同类型及其要素
SELECT 
    ct.type_name as 合同类型,
    COUNT(cet.id) as 要素数量,
    STRING_AGG(cet.element_name, ', ') as 要素列表
FROM contract_types ct
LEFT JOIN contract_element_templates cet ON ct.id = cet.contract_type_id
WHERE ct.is_active = TRUE AND (cet.is_active = TRUE OR cet.id IS NULL)
GROUP BY ct.id, ct.type_name
ORDER BY ct.sort_order;

-- 查看要素分类树
WITH RECURSIVE category_tree AS (
    SELECT id, category_name, category_path, 0 as level
    FROM element_categories 
    WHERE parent_category_id IS NULL
    
    UNION ALL
    
    SELECT ec.id, ec.category_name, ec.category_path, ct.level + 1
    FROM element_categories ec
    JOIN category_tree ct ON ec.parent_category_id = ct.id
)
SELECT 
    REPEAT('  ', level) || category_name as 分类结构,
    category_path as 路径
FROM category_tree
ORDER BY category_path;
```

### 统计查询

```sql
-- 配置统计
SELECT 
    '合同类型' as 项目, COUNT(*) as 数量 FROM contract_types WHERE is_active = TRUE
UNION ALL
SELECT 
    '要素分类', COUNT(*) FROM element_categories WHERE is_active = TRUE
UNION ALL
SELECT 
    '要素模板', COUNT(*) FROM contract_element_templates WHERE is_active = TRUE
UNION ALL
SELECT 
    '提取规则', COUNT(*) FROM element_extraction_rules WHERE is_active = TRUE
UNION ALL
SELECT 
    '验证规则', COUNT(*) FROM element_validation_rules WHERE is_active = TRUE;

-- 变更历史统计
SELECT 
    table_name as 表名,
    change_type as 变更类型,
    COUNT(*) as 次数,
    MAX(created_at) as 最后变更时间
FROM config_change_logs
GROUP BY table_name, change_type
ORDER BY table_name, change_type;
```

## 🐛 故障排除

### 常见问题

1. **外键约束错误**
   ```sql
   -- 检查外键引用
   SELECT conname, conrelid::regclass, confrelid::regclass
   FROM pg_constraint WHERE contype = 'f';
   ```

2. **索引性能问题**
   ```sql
   -- 检查索引使用情况
   SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
   FROM pg_stat_user_indexes
   ORDER BY idx_scan DESC;
   ```

3. **JSONB查询优化**
   ```sql
   -- 为JSONB字段创建GIN索引
   CREATE INDEX idx_element_config_gin ON contract_element_templates USING GIN (element_config);
   CREATE INDEX idx_validation_rules_gin ON element_validation_rules USING GIN (validation_config);
   ```

### 日志分析

```sql
-- 查看最近的配置变更
SELECT 
    table_name,
    change_type,
    changed_by,
    change_reason,
    created_at
FROM config_change_logs
ORDER BY created_at DESC
LIMIT 20;

-- 查看提取错误日志
SELECT 
    contract_id,
    extraction_status,
    error_message,
    created_at
FROM element_extraction_logs
WHERE extraction_status = 'failed'
ORDER BY created_at DESC;
```

## 📞 技术支持

如遇到数据库相关问题：

1. 查看本文档的故障排除部分
2. 检查PostgreSQL日志文件
3. 验证数据完整性和约束
4. 联系系统管理员

---

**数据库版本**: PostgreSQL 12+  
**字符编码**: UTF-8  
**时区**: Asia/Shanghai  
**维护者**: AI合同审核系统开发团队