<!DOCTYPE html>
<html>
<head>
    <title>API连接测试</title>
</head>
<body>
    <h1>API连接测试</h1>
    <button onclick="testAPI()">测试后端连接</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            try {
                // 测试健康检查接口
                const response = await fetch('/api/health');
                const data = await response.json();
                resultDiv.innerHTML = `<p style="color: green;">后端连接成功: ${JSON.stringify(data)}</p>`;
                
                // 测试文档列表接口
                const docResponse = await fetch('/api/v1/contracts');
                const docData = await docResponse.json();
                resultDiv.innerHTML += `<p style="color: green;">文档接口测试: ${JSON.stringify(docData)}</p>`;
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">连接失败: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>