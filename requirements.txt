# AI合同审核系统 - Python依赖包

# Web框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# 数据库
sqlalchemy>=2.0.0
alembic>=1.13.0
psycopg2-binary>=2.9.0
redis>=5.0.0

# 文档处理
python-docx>=1.1.0
python-multipart>=0.0.6

# AI和NLP
openai>=1.3.0
jieba>=0.42.1
numpy>=1.24.0
scikit-learn>=1.3.0

# 异步支持
asyncio-mqtt>=0.13.0
aiofiles>=23.2.1
aioredis>=2.0.1

# HTTP客户端
httpx>=0.25.0
requests>=2.31.0

# 数据验证和序列化
marshmallow>=3.20.0
cerberus>=1.3.5

# 日志和监控
loguru>=0.7.2
prometheus-client>=0.19.0

# 安全
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# 工具库
click>=8.1.7
rich>=13.7.0
typer>=0.9.0

# 配置管理
python-dotenv>=1.0.0
dynaconf>=3.2.0

# 测试 (开发依赖)
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0
httpx>=0.25.0  # 用于API测试

# 代码质量 (开发依赖)
black>=23.11.0
flake8>=6.1.0
mypy>=1.7.0
isort>=5.12.0

# 文档生成 (开发依赖)
mkdocs>=1.5.0
mkdocs-material>=9.4.0

# 性能分析 (开发依赖)
memory-profiler>=0.61.0
py-spy>=0.3.14